"use client";

import { useRef, useEffect } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import { OrbitControls } from "@react-three/drei";
import type { Mesh } from "three";
import { destinations } from "@/data/destinations";
import * as THREE from "three";

interface GlobeProps {
  selectedCountry: string;
  onSelectCountry: (id: string) => void;
}

function Globe({ selectedCountry, onSelectCountry }: GlobeProps) {
  const globeRef = useRef<Mesh>(null);
  const markersRef = useRef<Mesh[]>([]);

  // Create geometry for countries
  const countryGeometry = new THREE.SphereGeometry(2, 64, 64);
  const countryMaterial = new THREE.MeshStandardMaterial({
    color: "#1a1a2e",
    metalness: 0.3,
    roughness: 0.7,
  });

  // Handle marker click with hover effect
  const handleMarkerClick = (countryId: string) => {
    onSelectCountry(countryId);
  };

  const handleMarkerHover = (index: number, isHover: boolean) => {
    if (markersRef.current[index]) {
      const marker = markersRef.current[index];
      if (marker.material instanceof THREE.MeshStandardMaterial) {
        marker.material.emissiveIntensity = isHover ? 1 : 0.5;
        marker.scale.set(
          isHover ? 1.2 : 1,
          isHover ? 1.2 : 1,
          isHover ? 1.2 : 1
        );
      }
    }
  };

  return (
    <>
      {/* Earth */}
      <mesh
        ref={globeRef}
        geometry={countryGeometry}
        material={countryMaterial}
      />

      {/* Country Markers */}
      {destinations.map((destination, index) => {
        // Convert lat/lng to 3D coordinates on sphere
        const lat = destination.coordinates.lat * (Math.PI / 180);
        const lng = destination.coordinates.lng * (Math.PI / 180);

        // Calculate position on sphere
        const x = 2.1 * Math.cos(lat) * Math.sin(lng);
        const y = 2.1 * Math.sin(lat);
        const z = 2.1 * Math.cos(lat) * Math.cos(lng);

        // Create marker material with glow effect
        const markerMaterial = new THREE.MeshStandardMaterial({
          color: selectedCountry === destination.id ? "#89B83F" : "#ffffff",
          emissive: selectedCountry === destination.id ? "#89B83F" : "#ffffff",
          emissiveIntensity: 0.5,
          metalness: 0.1,
          roughness: 0.5,
        });

        return (
          <mesh
            key={destination.id}
            position={[x, y, z]}
            ref={(el) => {
              if (el) markersRef.current[index] = el;
            }}
            onClick={() => handleMarkerClick(destination.id)}
            onPointerEnter={() => handleMarkerHover(index, true)}
            onPointerLeave={() => handleMarkerHover(index, false)}
          >
            <sphereGeometry args={[0.08, 16, 16]} />
            {markerMaterial}
          </mesh>
        );
      })}

      {/* Ambient Lighting */}
      <ambientLight intensity={0.6} />

      {/* Directional Lighting */}
      <directionalLight position={[5, 5, 5]} intensity={0.8} />
    </>
  );
}

export default function GlobeContainer({
  selectedCountry,
  onSelectCountry,
}: GlobeProps) {
  return (
    <div className="h-full w-full">
      <Canvas camera={{ position: [0, 0, 5], fov: 45 }}>
        <Globe
          selectedCountry={selectedCountry}
          onSelectCountry={onSelectCountry}
        />
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          minZoom={0.5}
          maxZoom={2}
          zoomSpeed={0.5}
          rotateSpeed={0.5}
          minPolarAngle={Math.PI / 4}
          maxPolarAngle={Math.PI - Math.PI / 4}
          dampingFactor={0.05}
          enableDamping={true}
        />
      </Canvas>
    </div>
  );
}
