"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Building, GraduationCap } from "lucide-react"

interface Country {
  name: string
  image: string
  universities: number
  description: string
}

interface CountryCardProps {
  country: Country
}

export default function CountryCard({ country }: CountryCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      whileHover={{ y: -10 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="h-full"
    >
      <Card className="overflow-hidden h-full border-none shadow-md hover:shadow-xl transition-shadow duration-300">
        <div className="relative h-48 w-full">
          <Image src={country.image || "/placeholder.svg"} alt={country.name} fill className="object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-foreground/80 to-transparent" />
          <div className="absolute bottom-0 left-0 p-4 text-background">
            <h3 className="text-xl font-bold">{country.name}</h3>
          </div>
        </div>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Building className="h-4 w-4 text-primary" />
            <span className="text-sm">{country.universities} Universities</span>
          </div>
          <p className="text-muted-foreground text-sm">{country.description}</p>

          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{
              opacity: isHovered ? 1 : 0,
              height: isHovered ? "auto" : 0,
            }}
            transition={{ duration: 0.3 }}
            className="mt-4 overflow-hidden"
          >
            <div className="pt-4 border-t border-border">
              <div className="flex items-center gap-2 mb-2">
                <GraduationCap className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">Popular Programs</span>
              </div>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Business & Management</li>
                <li>• Computer Science & IT</li>
                <li>• Engineering & Technology</li>
              </ul>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
