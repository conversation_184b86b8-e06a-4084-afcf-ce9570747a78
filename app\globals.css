@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  cursor: none;
}

/* Fix for Leaflet maps z-index */
.leaflet-container,
.leaflet-pane,
.leaflet-top,
.leaflet-bottom,
.leaflet-control {
  z-index: 40 !important;
}

button,
a,
input[type="button"],
input[type="submit"],
input[type="reset"],
[role="button"],
.button,
.btn {
  cursor: none !important;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 14% 17%;
    --card: 90, 7.89%, 14.9%;
    --card-foreground: 222 14% 17%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 14% 17%;
    --primary: 93 53% 55%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 14% 17%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222 14% 17%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 93 53% 55%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 100, 5.88%, 10%;
    --foreground: 210 40% 98%;
    --card: 100, 5.88%, 10%;
    --card-foreground: 210 40% 98%;
    --popover: 222 14% 17%;
    --popover-foreground: 210 40% 98%;
    --primary: 93 53% 55%;
    --primary-foreground: 222 14% 17%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 93 53% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .perspective {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotateY-180 {
    transform: rotateY(180deg);
  }

  .scroll-mt-24 {
    scroll-margin-top: 6rem;
  }

  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .animation-delay-500 {
    animation-delay: 500ms;
  }

  .crisp-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
  }
}

html {
  scroll-behavior: smooth;
}
