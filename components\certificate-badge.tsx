import { Dialog, DialogContent, DialogTrigger } from "./ui/dialog";
import { Badge } from "./ui/badge";
import { Award } from "lucide-react";
import Image from "next/image";

interface CertificateBadgeProps {
  tag: string;
  name: string;
  image: string;
  member: string;
}

export const CertificateBadge = ({
  tag,
  name,
  image,
  member,
}: CertificateBadgeProps) => {
  return (
    <div className="flex flex-wrap gap-2 mt-auto pt-4 justify-end">
      <Dialog key={tag}>
        <DialogTrigger asChild>
          <Badge
            variant="outline"
            className="cursor-pointer hover:bg-primary/10 transition-colors flex items-center gap-1"
          >
            <Award className="h-3 w-3" />
            <span className="text-xs">{tag}</span>
          </Badge>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-center">{name}</h3>
            <div className="relative h-[400px] w-full overflow-hidden rounded-md">
              <Image
                src={image || "/placeholder.svg"}
                alt={name}
                fill
                className="object-contain"
              />
            </div>
            <p className="text-center text-sm text-muted-foreground">
              Certificate issued to {member}
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
