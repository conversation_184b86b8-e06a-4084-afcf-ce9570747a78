import type { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";

interface MarkhorLogoProps extends HTMLAttributes<SVGElement> {
  fill?: string;
  width?: number;
  height?: number;
}

export default function MarkhorLogo({
  fill = "currentColor",
  width = 35,
  height = 23,
  className,
  ...props
}: MarkhorLogoProps) {
  return (
    <svg
      version="1.2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="137 219 35 23"
      width={width}
      height={height}
      className={cn(className)}
      {...props}
    >
      <title>Markhor International</title>
      <g id="Layer 1">
        <g id="&lt;Group&gt;">
          <g id="&lt;Group&gt;">
            <g id="&lt;Group&gt;">
              <path
                id="&lt;Path&gt;"
                fill={fill}
                d="m158.1 237.5h2.9c-5.4-9-5.3-8.9-10.6-17.8-3.5 6-9.4 15.9-12.9 21.9h2.8c2.6-4.4 7.5-12.8 10.1-17.2 3.5 5.8 4.3 7.3 7.7 13.1z"
              />
              <path
                id="&lt;Path&gt;"
                fill={fill}
                d="m152 237.5h-2.8c5.2-9 5.1-8.9 10.3-17.8 3.4 6 9.2 15.9 12.6 21.9h-2.7c-2.6-4.4-7.4-12.8-9.9-17.2-3.4 5.8-4.2 7.3-7.5 13.1z"
              />
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}
