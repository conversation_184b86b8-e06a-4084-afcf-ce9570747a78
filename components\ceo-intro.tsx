import Image from "next/image";
import Link from "next/link";
import { Instagram, Facebook, Mail, Linkedin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CertificateBadge } from "./certificate-badge";

interface CEOIntroProps {
  name?: string;
  title?: string;
  bio?: string;
  imageUrl?: string;
  socialLinks?: {
    email?: string;
    instagram?: string;
    facebook?: string;
    linkedin?: string;
  };
}

export function CEOIntro({
  name = "Hira Iqbal",

  title = "Founder @markhor_goc",
  bio = "Business Counselor | British Council Certified Foreign Education Counselor @markhorinternational | Humanitarian",
  imageUrl = "/ceo_pfp4.jpg",
  socialLinks = {
    email: "<EMAIL>",
    instagram: "https://www.instagram.com/hiraiiqbal",
    facebook: "https://www.facebook.com/hiraiiqbal",
    linkedin: "https://www.linkedin.com/in/hira-iqbal-1a6b34207/",
  },
}: CEOIntroProps) {
  return (
    <Card className="overflow-hidden border rounded-lg shadow-lg bg-background">
      <CardContent className="p-6 sm:p-8">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12">
          <div className="flex flex-col items-center lg:items-start space-y-4 lg:max-w-[300px]">
            <div className="overflow-hidden border-4 border-background shadow-xl">
              <Image
                src={imageUrl || "/placeholder.svg"}
                alt={name}
                width={200}
                height={200}
                className="object-top object-cover"
              />
            </div>
            <div className="flex justify-center space-x-4 w-full">
              {socialLinks.email && (
                <Link href={`mailto:${socialLinks.email}`}>
                  <Button size="icon" variant="ghost">
                    <Mail className="h-5 w-5" />
                    <span className="sr-only">Email</span>
                  </Button>
                </Link>
              )}
              {socialLinks.instagram && (
                <Link
                  href={socialLinks.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="icon" variant="ghost">
                    <Instagram className="h-5 w-5" />
                    <span className="sr-only">Instagram</span>
                  </Button>
                </Link>
              )}
              {socialLinks.facebook && (
                <Link
                  href={socialLinks.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="icon" variant="ghost">
                    <Facebook className="h-5 w-5" />
                    <span className="sr-only">Facebook</span>
                  </Button>
                </Link>
              )}{" "}
              {socialLinks.linkedin && (
                <Link
                  href={socialLinks.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="icon" variant="ghost">
                    <Linkedin className="h-5 w-5" />
                    <span className="sr-only">LinkedIn</span>
                  </Button>
                </Link>
              )}
            </div>
          </div>
          <div className="flex flex-col justify-center space-y-4 flex-1">
            <div className="space-y-2 text-center lg:text-left">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">
                {name}
              </h2>
              <p className="text-xl text-muted-foreground">
                {title.includes("@") ? <>Founder & CEO</> : title}
              </p>
            </div>
            <div className="prose prose-sm max-w-none">
              <p
                className="text-base leading-relaxed mb-6  indent-8  text-justify
 "
              >
                As the Founder and CEO of Markhor International, I have had the
                privilege of helping hundreds of students shape their academic
                futures since 2018. With multiple certifications including
                British Council Certified Foreign Education Counselor, British
                Council Certified IELTS Counselor, Pearson Certified PTE
                Trainer, and Europe-Certified Counselor, I bring a well-rounded
                and globally informed approach to international education
                consulting.
              </p>
              <p
                className="text-base leading-relaxed mb-6 text-justify indent-8
 "
              >
                Over the years, I have also actively participated in numerous
                national and international workshops and training programs to
                ensure I stay updated with the ever-evolving trends of global
                education and visa policies. This enables me to provide each
                student with accurate, timely, and strategic guidance.
              </p>
              <p
                className="text-base leading-relaxed mb-6 text-justify indent-8
 "
              >
                At Markhor International, we specialize in guiding students
                through every stage of their journey — from choosing the right
                course and university to navigating the admission process,
                securing visas, and ensuring a smooth transition upon arrival.
                We cater to students pursuing Foundation, Undergraduate,
                Postgraduate, Research, and Doctoral programs in top
                destinations including the UK, USA, Canada, Australia, Europe,
                and Turkey.
              </p>
              <p
                className="text-base leading-relaxed mb-6 text-justify indent-8
 "
              >
                Our services also extend to visit visa guidance and helping
                students apply for fully funded scholarships offered by
                prestigious institutions and governments worldwide.
              </p>
              <p
                className="text-base leading-relaxed mb-6 text-justify indent-8
 "
              >
                We are committed to personalized counseling, transparency, and
                result-driven strategies that ensure our students don't just
                travel abroad — they thrive abroad.
              </p>
              <p
                className="text-base leading-relaxed mb-6 text-justify indent-8
 "
              >
                At Markhor International, we believe in empowering students to
                Roll the Globe on Their Fingers — by unlocking global
                opportunities with confidence and clarity. Warm regards, Hira
                Iqbal Founder & CEO Markhor International
              </p>
            </div>
            <div className="relative">
              <div className="absolute left-0 top-0 h-full w-1 bg-primary rounded-full"></div>
              <blockquote className="pl-4 italic">
                "At Markhor International, we don't just help students roll the
                globe on their fingers; we help them leave their mark on it."
              </blockquote>
            </div>
            {/* Responsive Badge Layout */}
            {/* Improved Responsive Badge Layout */}
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-3">Certifications</h3>
              <div className="flex flex-wrap gap-2">
                <CertificateBadge
                  tag="GUS Academy"
                  name="GUS Academy"
                  image="/team/certificates/berlin_hira.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="IELTS Counsellers"
                  name="IELTS Counsellers Workshop"
                  image="/team/certificates/ielts_counseller.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="UK Agent Training"
                  name="UK Agent and Counseller Training"
                  image="/team/certificates/agent_hire.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="Study Group L3"
                  name="Study Group Agent Level 3"
                  image="/team/certificates/studygroup_hira.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="GISMA Director Q&A"
                  name="Q&A with GISMA's Director of Recruitment - COVID 19 in Germany"
                  image="/team/certificates/gisma_hira.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="Germany360"
                  name="Series of Germany 360 Industry Workshops"
                  image="/team/certificates/germany360_hira.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="PTE Core"
                  name="PTE Core - Train the Trainer Workshop"
                  image="/team/certificates/hira_pte_core.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="PTE Academic/UKVI"
                  name="PTE Academic/UKVI - Train the Trainer Workshop"
                  image="/team/certificates/hira_pte_ukvi.jpg"
                  member="Hira Iqbal"
                />{" "}
                <CertificateBadge
                  tag="Traning Session - Debrecen"
                  name="Traning Session - Debrecen"
                  image="/team/certificates/hira_debrecen.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="Markhor 2019"
                  name="Markhor 2019"
                  image="/team/certificates/hira_markhor2019.jpg"
                  member="Hira Iqbal"
                />
                <CertificateBadge
                  tag="Women Enterpreneurship Bootcamp"
                  name="Women Enterpreneurship Bootcamp"
                  image="/team/certificates/hira_women_camp.jpg"
                  member="Hira Iqbal"
                />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
