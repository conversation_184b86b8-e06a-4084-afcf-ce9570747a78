"use client";

import Image from "next/image";
import Link from "next/link";
import { cn } from "@/lib/utils";
import localFont from "next/font/local";
import MarkhorLogo from "./m-logo";

const logoFont = localFont({ src: "../public/Expressa Bold.ttf" });

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  showText?: boolean;
  showLogo?: boolean;
  darkMode?: boolean;
}

export function Logo({
  className,
  size = "md",
  showLogo = true,
  showText = true,
  darkMode = false,
}: LogoProps) {
  const sizes = {
    sm: { width: 30, height: 30 },
    md: { width: 40, height: 40 },
    lg: { width: 51, height: 51 },
  };

  return (
    <Link href="/" className={cn("flex items-center ", className)}>
      {showLogo && (
        <div className="relative">
          <Image
            src="/logo-lg.svg"
            alt="Markhor International"
            fill
            className="object-contain"
            priority
          />
        </div>
      )}
      {showText && (
        <div
          className="flex items-center tracking-widest"
          style={logoFont.style}
        >
          <MarkhorLogo fill={darkMode ? "#ffffff" : "#8bc540"} />
          <span className={cn("text-lg font-bold ")}>arkhor</span>
          <span
            className={cn(
              "ml-2 text-lg font-medium leading-tight text-primary"
            )}
          >
            International
          </span>
        </div>
      )}
    </Link>
  );
}
