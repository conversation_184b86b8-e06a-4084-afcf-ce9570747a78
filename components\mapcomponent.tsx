/* eslint-disable */
// @ts-nocheck

"use client";

import React, { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSO<PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import countriesData from "@/public/countries.json";

// WorldMap Component
const MapComponent = ({ selectedCountry }) => {
    // Fix for Leaflet icon issue in Next.js
    useEffect(() => {
        // This is needed because Leaflet's CSS assumes there's an images folder with marker icons
        delete L.Icon.Default.prototype._getIconUrl;
        L.Icon.Default.mergeOptions({
            iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
            iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
            shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
        });
    }, []);

    const countryStyle = (feature) => ({
        fillColor:
            feature.properties.name === selectedCountry ? "#86c94f" : "#e5e7eb",
        weight: 1,
        opacity: 1,
        color: "white",
        fillOpacity: 0.7,
    });

    return (
        <MapContainer
            center={[20, 0]}
            zoom={2}
            className="h-full w-full rounded-xl shadow-lg"
            style={{ height: "500px" }} // Add explicit height
            scrollWheelZoom={false}
            dragging={true}
            doubleClickZoom={false}
            touchZoom={false}
            keyboard={false}
            zoomControl={false}
        >
            <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
            <GeoJSON data={countriesData} style={countryStyle} />
        </MapContainer>
    );
};

export default MapComponent;