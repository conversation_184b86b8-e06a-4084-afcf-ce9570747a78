"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { MessageCircle } from "lucide-react";

interface WhatsAppButtonProps {
  phoneNumber: string;
  message?: string;
  showAfterScroll?: number; // Show after scrolling this many pixels
}

export function WhatsAppButton({
  phoneNumber,
  message = "Hello! I'm interested in your services.",
  showAfterScroll = 300,
}: WhatsAppButtonProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Handle scroll to show button after scrolling down
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > showAfterScroll) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [showAfterScroll]);

  // Format phone number (remove any non-digit characters)
  const formattedPhone = phoneNumber.replace(/\D/g, "");

  // Create WhatsApp URL
  const whatsappUrl = `https://wa.me/message/3OSI3VSB2QT4F1`;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed right-6 bottom-6 z-50"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5 }}
          transition={{ duration: 0.3 }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <Link
            href={whatsappUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="relative block"
            data-cursor-text="Chat with us"
          >
            {/* Pulse animation rings */}
            {!isHovered && (
              <>
                <span className="absolute inset-0 rounded-full bg-green-500 opacity-25 animate-ping-slow" />
                <span
                  className="absolute inset-0 rounded-full bg-green-500 opacity-25 animate-ping-slow animation-delay-500"
                  style={{ animationDelay: "500ms" }}
                />
              </>
            )}
            <div className="relative flex items-center justify-center w-14 h-14 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-colors">
              <MessageCircle size={24} />
            </div>

            {/* Tooltip */}
            <AnimatePresence>
              {isHovered && (
                <motion.div
                  className="absolute right-full mr-4 bottom-2 bg-white text-gray-800 px-4 py-2 rounded-lg shadow-lg whitespace-nowrap"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="absolute right-0 top-1/2 translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-white transform rotate-45" />
                  <span className="relative z-10 font-medium">
                    Chat with us on WhatsApp
                  </span>
                </motion.div>
              )}
            </AnimatePresence>
          </Link>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
