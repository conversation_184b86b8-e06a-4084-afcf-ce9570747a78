"use client";

import type React from "react";

import { useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MapPin, Phone, Mail, Clock, Send } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";

export default function ContactPage() {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
    preferredContact: "whatsapp",
    serviceInterest: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleRadioChange = (value: string) => {
    setFormData((prev) => ({ ...prev, preferredContact: value }));
  };

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Send data to our API route
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        // Show success toast
        toast({
          title: "Message Sent",
          description:
            "Thank you for contacting us. We'll get back to you shortly.",
        });

        // Reset form
        setFormData({
          name: "",
          email: "",
          phone: "",
          subject: "",
          message: "",
          preferredContact: "email",
          serviceInterest: "",
        });
      } else {
        // Show error toast
        toast({
          title: "Error",
          description:
            "There was a problem sending your message. Please try again later.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Error",
        description:
          "There was a problem sending your message. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <main className="pt-24 pb-16">
      {/* Hero Section */}
      <section className="container mx-auto px-4 mb-24">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto"
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-6">Get in Touch</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Have questions about our services or ready to start your
            international education journey? Contact our team of experts today.
          </p>
        </motion.div>
      </section>

      {/* Contact Information */}
      <section className="bg-muted/30 py-24 mb-24">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactInfo.map((item, index) => (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-none shadow-md hover:shadow-lg transition-shadow duration-300 bg-background">
                  <CardContent className="p-6 flex flex-col items-center text-center">
                    <div className="bg-primary/10 p-4 rounded-full mb-4">
                      {item.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                    <p className="text-muted-foreground mb-4">
                      {item.description}
                    </p>
                    {item.links.map((link, idx) => (
                      <a
                        key={idx}
                        href={link.url}
                        className="text-primary hover:underline block mb-1"
                      >
                        {link.text}
                      </a>
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form and Map */}
      <section className="container mx-auto px-4 mb-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6">Inquire Now</h2>
            <p className="text-muted-foreground mb-8">
              Fill out the form below and our team will get back to you as soon
              as possible.
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Your full name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Your email address"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Your phone number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="serviceInterest">Service of Interest</Label>
                  <Select
                    onValueChange={(value) =>
                      handleSelectChange("serviceInterest", value)
                    }
                    value={formData.serviceInterest}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a service" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="visa">Visa Consultation</SelectItem>
                      <SelectItem value="admissions">
                        University Admissions
                      </SelectItem>
                      <SelectItem value="scholarship">
                        Scholarship Guidance
                      </SelectItem>
                      <SelectItem value="test-prep">
                        Test Preparation
                      </SelectItem>
                      <SelectItem value="pre-departure">
                        Pre-Departure Briefing
                      </SelectItem>
                      <SelectItem value="career">Career Counseling</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Subject</Label>
                <Input
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  placeholder="Subject of your message"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Your message"
                  rows={5}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label>Preferred Contact Method</Label>
                <RadioGroup
                  value={formData.preferredContact}
                  onValueChange={handleRadioChange}
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="whatsapp" id="whatsapp-contact" />
                    <Label htmlFor="phone-contact">Whatsapp</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="phone" id="phone-contact" />
                    <Label htmlFor="phone-contact">Phone</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="email" id="email-contact" />
                    <Label htmlFor="email-contact">Email</Label>
                  </div>
                </RadioGroup>
              </div>

              <Button
                type="submit"
                className="w-full md:w-auto"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="mr-2 h-4 w-4 animate-spin">⏳</span>{" "}
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" /> Send Message
                  </>
                )}
              </Button>
            </form>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6">Visit Our Office</h2>
            <p className="text-muted-foreground mb-8">
              We're conveniently located in the heart of the city. Feel free to
              drop by during our office hours.
            </p>

            <div className="rounded-lg overflow-hidden h-[400px] mb-8 relative group cursor-pointer">
              <Link
                href="https://www.google.com/maps?q=31.4534002,74.2731833"
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full h-full"
              >
                <Image
                  src="https://maps.googleapis.com/maps/api/staticmap?center=31.4697,74.2728&zoom=15&size=1200x800&maptype=roadmap&markers=color:red%7C31.4697,74.2728&key=AIzaSyBSg-HHoQ4g04YLUbPUUrk_MNZsuB8wyPs"
                  alt="Office location map - 898-R1, Phase 2 Johar Town Lahore"
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-white/90 text-black px-4 py-2 rounded-full flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-primary" />
                    <span className="font-medium">Open in Google Maps</span>
                  </div>
                </div>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xl font-semibold mb-2">Main Office</h3>
                <p className="text-muted-foreground mb-4">
                  898-R1, Phase 2
                  <br />
                  Johar Town 54000
                  <br />
                  Lahore, Punjab
                  <br />
                  Pakistan
                </p>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-2">Office Hours</h3>
                <p className="text-muted-foreground">
                  Monday - Friday: 10:00 AM - 6:00 PM
                  <br />
                  24/7: Online Consultation
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-primary/10 py-24 mb-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Find quick answers to common questions about our services
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="mb-6"
              >
                <div className="bg-background rounded-lg p-6 shadow-sm">
                  <h3 className="text-xl font-semibold mb-3">{faq.question}</h3>
                  <p className="text-muted-foreground">{faq.answer}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 mb-24">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto text-center"
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Book a free consultation with our education experts and take the
            first step toward your international education goals.
          </p>
          <Button size="lg">
            <Link
              href={`https://wa.me/message/3OSI3VSB2QT4F1`}
              target="_blank"
              rel="noopener noreferrer"
              className="relative block"
              data-cursor-text="Chat with us"
            >
              Book a Free Consultation
            </Link>
          </Button>
        </motion.div>
      </section>
    </main>
  );
}

const contactInfo = [
  {
    title: "Our Location",
    description: "Visit our office for in-person consultations",
    icon: <MapPin className="h-6 w-6 text-primary" />,
    links: [
      {
        text: "898-R1, Phase 2 Johar Town Lahore",
        url: "https://www.google.com/maps?q=31.4697,74.2728",
      },
    ],
  },
  {
    title: "Call Us",
    description: "Speak directly with our expert consultants",
    icon: <Phone className="h-6 w-6 text-primary" />,
    links: [{ text: "+92 331 6246264", url: "tel:+923316246264" }],
  },
  {
    title: "Email Us",
    description: "Send us your inquiries anytime",
    icon: <Mail className="h-6 w-6 text-primary" />,
    links: [
      { text: "<EMAIL>", url: "mailto:<EMAIL>" },
      { text: "<EMAIL>", url: "mailto:<EMAIL>" },
    ],
  },
  {
    title: "Office Hours",
    description: "When you can visit or call us",
    icon: <Clock className="h-6 w-6 text-primary" />,
    links: [
      { text: "Mon-Fri: 10:00 AM - 6:00 PM", url: "#" },
      { text: "24/7: Online Consultation", url: "#" },
    ],
  },
];

const faqs = [
  {
    question: "How do I schedule a consultation?",
    answer:
      "You can schedule a consultation by filling out the contact form on this page, calling our office directly, or sending us an email. We'll get back to you within 24 hours to arrange a convenient time.",
  },
  {
    question: "Are your consultations free?",
    answer:
      "Yes, we offer a free initial consultation to understand your needs and explain how our services can help you achieve your international education goals.",
  },
  {
    question: "How long does the visa application process take?",
    answer:
      "The visa application process varies by country, typically ranging from 2 weeks to 3 months. We recommend starting the process at least 3-4 months before your intended departure date.",
  },
  {
    question: "Do you offer virtual counseling sessions?",
    answer:
      "Yes, we offer virtual consultations via Zoom, Skype, or phone for clients who cannot visit our office in person. These consultations are just as comprehensive as in-person meetings.",
  },
  {
    question: "What documents should I bring to my first consultation?",
    answer:
      "For your first consultation, it's helpful to bring your academic transcripts, standardized test scores (if available), passport, and any university acceptance letters you may have already received.",
  },
];
