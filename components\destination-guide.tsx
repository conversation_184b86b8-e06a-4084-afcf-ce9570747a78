"use client";

import { useState, useRef, useEffect } from "react";
import { destinations } from "@/data/destinations";
import GlobeContainer from "./simplified-globe";
import { Card } from "./ui/card";
import { Badge } from "./ui/badge";

export function DestinationGuide() {
  const [selectedCountry, setSelectedCountry] = useState<string>("usa");
  const destinationRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Handle country selection from globe
  const handleCountrySelect = (countryId: string) => {
    setSelectedCountry(countryId);
    destinationRefs.current[countryId]?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  };

  // Handle scroll sync
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const countryId = entry.target.id;
            setSelectedCountry(countryId);
          }
        });
      },
      { threshold: 0.5 }
    );

    Object.values(destinationRefs.current).forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, []);

  return (
    <div className="flex min-h-screen gap-8 p-8">
      {/* Globe Section */}
      <div className="sticky top-8 h-[calc(100vh-4rem)] w-1/2">
        <GlobeContainer
          selectedCountry={selectedCountry}
          onSelectCountry={handleCountrySelect}
        />
      </div>

      {/* Destinations Section */}
      <div className="w-1/2 space-y-8">
        {destinations.map((destination) => (
          <Card
            key={destination.id}
            id={destination.id}
            ref={(el) => {
              destinationRefs.current[destination.id] = el;
            }}
            className="p-6"
          >
            <h2 className="text-2xl font-bold">{destination.name}</h2>
            <p className="mt-2 text-muted-foreground">
              {destination.description}
            </p>

            <div className="mt-4 space-y-4">
              {/* Visa Types */}
              <div>
                <h3 className="text-lg font-semibold">Available Visa Types</h3>
                <div className="mt-2 space-y-2">
                  {destination.visaTypes.map((visa) => (
                    <div key={visa.type} className="rounded-lg border p-3">
                      <h4 className="font-medium">{visa.type}</h4>
                      <p className="text-sm text-muted-foreground">
                        Duration: {visa.duration}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Purpose: {visa.purpose}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Requirements */}
              <div>
                <h3 className="text-lg font-semibold">Requirements</h3>
                <ul className="mt-2 list-inside list-disc space-y-1">
                  {destination.requirements.map((req) => (
                    <li key={req} className="text-muted-foreground">
                      {req}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Processing Time & Fees */}
              <div className="flex gap-4">
                <div>
                  <h3 className="text-lg font-semibold">Processing Time</h3>
                  <p className="text-muted-foreground">
                    {destination.processingTime}
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold">Visa Fees</h3>
                  <p className="text-muted-foreground">
                    {destination.fees.amount} {destination.fees.currency}
                  </p>
                </div>
              </div>

              {/* Benefits */}
              <div>
                <h3 className="text-lg font-semibold">Benefits</h3>
                <div className="mt-2 flex flex-wrap gap-2">
                  {destination.benefits.map((benefit) => (
                    <Badge key={benefit} variant="secondary">
                      {benefit}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
