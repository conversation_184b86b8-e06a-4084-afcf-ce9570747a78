"use client";

import type React from "react";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface Service {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface ServiceCardProps {
  service: Service;
  isActive: boolean;
  onClick: () => void;
}

export default function ServiceCard({
  service,
  isActive,
  onClick,
}: ServiceCardProps) {
  return (
    <motion.div whileHover={{ y: -10 }} className="h-full">
      <Card
        className={`h-full overflow-hidden cursor-pointer transition-all duration-300 bg-background ${
          isActive ? "border-primary shadow-lg" : "border-border shadow-md"
        }`}
        onClick={onClick}
      >
        <CardContent className="p-6">
          <div
            className={`mb-4 p-3 rounded-full w-fit ${
              isActive ? "bg-primary/20" : "bg-primary/10"
            }`}
          >
            {service.icon}
          </div>
          <h3 className="text-2xl font-bold mb-2">{service.title}</h3>
          <p className="text-muted-foreground mb-4">{service.description}</p>
          <Button variant="link" className="p-0 group">
            Learn more
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
}
