"use client";

import { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CheckCircle } from "lucide-react";
import ServiceCard from "@/components/service-card";
import { services } from "@/data/services";
import { process } from "@/data/process";
import { pricingPlans } from "@/data/pricing";
import Link from "next/link";

export default function ServicesPage() {
  const [activeService, setActiveService] = useState("career-guidance");
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const serviceRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [isInViewArray, setIsInViewArray] = useState<boolean[]>(
    services.map(() => false)
  );

  // Initialize useInView hooks for each service
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsInViewArray((prev) => {
            const next = [...prev];
            const serviceIndex = services.findIndex(
              (service) => service.id === entry.target.id
            );
            if (serviceIndex !== -1) {
              next[serviceIndex] = entry.isIntersecting;
              return next;
            }
            return prev;
          });
        });
      },
      {
        threshold: 0.3,
      }
    );

    serviceRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });

    return () => {
      serviceRefs.current.forEach((ref) => {
        if (ref) {
          observer.unobserve(ref);
        }
      });
    };
  }, []);

  // Update active service based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      if (!scrollContainerRef.current) return;

      const scrollPosition = window.scrollY + window.innerHeight / 2;

      // Find the service that is currently in view
      serviceRefs.current.forEach((ref, index) => {
        if (!ref) return;

        const { top, bottom } = ref.getBoundingClientRect();
        const refTop = top + window.scrollY;
        const refBottom = bottom + window.scrollY;

        if (scrollPosition >= refTop && scrollPosition <= refBottom) {
          setActiveService(services[index].id);
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Scroll to service when clicking on navigation
  const scrollToService = (serviceId: string) => {
    const index = services.findIndex((service) => service.id === serviceId);
    if (index !== -1 && serviceRefs.current[index]) {
      serviceRefs.current[index]?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  };

  return (
    <main className="pt-24 pb-16">
      {/* Hero Section */}
      <section className="container mx-auto px-4 mb-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Our Services
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Comprehensive support for every step of your international
              education journey, from university selection to visa approval and
              beyond.
            </p>
            <div className="flex flex-wrap gap-4">
              <Button
                size="lg"
                onClick={() =>
                  document
                    .getElementById("services-overview")
                    ?.scrollIntoView({ behavior: "smooth" })
                }
              >
                Explore Services
              </Button>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative h-[500px] rounded-lg overflow-hidden"
          >
            <Image
              src="/services-hero.jpg"
              alt="Our services"
              fill
              className="object-cover"
            />
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <section id="services-overview" className="bg-muted/30 py-24 mb-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-4">
              Comprehensive Services
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Everything you need for a successful international education
              journey
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ServiceCard
                  service={service}
                  isActive={activeService === service.id}
                  onClick={() => {
                    setActiveService(service.id);
                    scrollToService(service.id);
                  }}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Details - Vertical Scroll Section */}
      <section className="container mx-auto px-4 mb-24">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-4">
            Service Details
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            In-depth information about our specialized services
          </p>
        </motion.div>

        <div className="flex flex-col lg:flex-row gap-8 max-w-7xl mx-auto">
          {/* Sticky Navigation */}
          <div className="lg:w-1/4">
            <div className="sticky top-24 space-y-2">
              {services.map((service) => (
                <Button
                  key={service.id}
                  variant={activeService === service.id ? "default" : "ghost"}
                  className={`w-full justify-start ${
                    activeService === service.id
                      ? "bg-primary text-primary-foreground"
                      : ""
                  }`}
                  onClick={() => {
                    setActiveService(service.id);
                    scrollToService(service.id);
                  }}
                >
                  {service.icon}
                  <span className="ml-2">
                    {service.shortTitle || service.title}
                  </span>
                </Button>
              ))}
            </div>
          </div>

          {/* Scrollable Content */}
          <div className="lg:w-3/4" ref={scrollContainerRef}>
            <div className="space-y-32">
              {services.map((service, index) => {
                const isInView = isInViewArray[index];

                return (
                  <div
                    key={service.id}
                    id={service.id}
                    ref={(el) => {
                      serviceRefs.current[index] = el;
                    }}
                    className="scroll-mt-24"
                  >
                    <motion.div
                      initial={{ opacity: 0, y: 50 }}
                      animate={
                        isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }
                      }
                      transition={{ duration: 0.7, ease: "easeOut" }}
                    >
                      <Card className="border-none shadow-lg bg-muted/30">
                        <CardContent className="p-6 pt-6">
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                            <div>
                              <div className="mb-6">
                                <div className="bg-primary/10 p-3 rounded-full w-fit mb-4">
                                  {service.icon}
                                </div>
                                <h3 className="text-2xl font-bold mb-2">
                                  {service.title}
                                </h3>
                                <p className="text-muted-foreground">
                                  {service.description}
                                </p>
                              </div>

                              <h4 className="text-xl font-semibold mb-4">
                                What's Included:
                              </h4>
                              <ul className="space-y-3 mb-6">
                                {service.features.map((feature, idx) => (
                                  <li
                                    key={idx}
                                    className="flex items-start gap-2"
                                  >
                                    <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                                    <span>{feature}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <div className="flex justify-end">
                              <Accordion
                                type="single"
                                collapsible
                                className="w-full"
                              >
                                {service.faqs.map((faq, idx) => (
                                  <AccordionItem key={idx} value={`faq-${idx}`}>
                                    <AccordionTrigger className="text-left">
                                      {faq.question}
                                    </AccordionTrigger>
                                    <AccordionContent>
                                      {faq.answer}
                                    </AccordionContent>
                                  </AccordionItem>
                                ))}
                              </Accordion>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="bg-muted/30 py-24 mb-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-4">Our Process</h2>
            <p className="text-xl text-background/80 max-w-2xl mx-auto">
              A streamlined approach to help you achieve your international
              education goals
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative"
              >
                <Card className="h-full bg-background border-none">
                  <CardHeader>
                    <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-background font-bold text-xl mb-4">
                      {index + 1}
                    </div>
                    <CardTitle>{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{step.description}</CardDescription>
                  </CardContent>
                </Card>

                {index < process.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 right-0 w-8 h-0.5 bg-primary translate-x-full">
                    <div className="absolute top-1/2 right-0 w-2 h-2 bg-primary rounded-full -translate-y-1/2"></div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      {/* <section id="pricing" className="container mx-auto px-4 mb-24">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-4">Pricing Plans</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Flexible options to suit your needs and budget
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {pricingPlans.map((plan, index) => (
            <motion.div
              key={plan.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card
                className={`h-full ${
                  plan.featured ? "border-primary shadow-lg" : ""
                }`}
              >
                {plan.featured && (
                  <div className="bg-primary text-background text-center py-2 text-sm font-medium">
                    Most Popular
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.title}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold">${plan.price}</span>
                    {plan.period && (
                      <span className="text-muted-foreground ml-2">
                        {plan.period}
                      </span>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full ${
                      plan.featured ? "bg-primary hover:bg-primary/90" : ""
                    }`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </section> */}

      {/* CTA Section */}
      <section className="bg-primary/10 py-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto text-center"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Ready to Start Your Journey?
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Book a free consultation with our education experts and take the
              first step toward your international education goals.
            </p>
            <Button size="lg">
              <Link
                href={`https://wa.me/message/3OSI3VSB2QT4F1`}
                target="_blank"
                rel="noopener noreferrer"
                className="relative block"
                data-cursor-text="Chat with us"
              >
                Book a Free Consultation
              </Link>
            </Button>
          </motion.div>
        </div>
      </section>
    </main>
  );
}
