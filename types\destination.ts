export interface University {
  name: string;
  location: string;
  tuitionFees: {
    amount: number;
    currency: string;
  };
  programs: string[];
  website: string;
  description: string;
  image?: string;
  admissionRequirementsURL?: string;
  coursesURL?: string;
}

export interface Destination {
  id: string;
  name: string;
  code: string;
  image: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  description: string;
  requirements: string[];
  processingTime: string;
  fees: {
    amount: number;
    currency: string;
  };
  documents: string[];
  benefits: string[];
  eligibility: string[];
  visaTypes: Array<{
    type: string;
    duration: string;
    purpose: string;
  }>;
  universities: University[];
  educationSystem?: string;
  averageCost?: {
    tuition: {
      amount: number;
      currency: string;
    };
    living: {
      amount: number;
      currency: string;
    };
  };
  scholarships?: Array<{
    name: string;
    description: string;
    eligibility: string;
    amount?: {
      value: number;
      currency: string;
    };
    website?: string;
  }>;
}
