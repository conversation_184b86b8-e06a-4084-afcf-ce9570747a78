import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  try {
    const formData = await request.json();
    
    // Create a transporter using Gmail
    const transporter = nodemailer.createTransport({
    host: 'smtp.ethereal.email',
    port: 587,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Format the message
    const serviceInterestMap: Record<string, string> = {
      'visa': 'Visa Consultation',
      'admissions': 'University Admissions',
      'scholarship': 'Scholarship Guidance',
      'test-prep': 'Test Preparation',
      'pre-departure': 'Pre-Departure Briefing',
      'career': 'Career Counseling',
    };

    const serviceInterest = formData.serviceInterest 
      ? serviceInterestMap[formData.serviceInterest] || formData.serviceInterest
      : 'Not specified';

    // Email content
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: 'h<PERSON><PERSON><EMAIL>', // Your email address
      subject: `New Contact Form Submission: ${formData.subject}`,
      html: `
        <h1>New Contact Form Submission</h1>
        <p><strong>Name:</strong> ${formData.name}</p>
        <p><strong>Email:</strong> ${formData.email}</p>
        <p><strong>Phone:</strong> ${formData.phone || 'Not provided'}</p>
        <p><strong>Subject:</strong> ${formData.subject}</p>
        <p><strong>Service Interest:</strong> ${serviceInterest}</p>
        <p><strong>Preferred Contact Method:</strong> ${formData.preferredContact}</p>
        <h2>Message:</h2>
        <p>${formData.message.replace(/\n/g, '<br>')}</p>
      `,
    };

    // Send the email
    await transporter.sendMail(mailOptions);

    return NextResponse.json({ success: true, message: 'Email sent successfully' });
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send email', error: String(error) },
      { status: 500 }
    );
  }
}
