<svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_270_60934)">
<rect width="20" height="15" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0V15H20V0H0Z" fill="#5BA3DA"/>
<mask id="mask0_270_60934" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="15">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0V15H20V0H0Z" fill="white"/>
</mask>
<g mask="url(#mask0_270_60934)">
<g filter="url(#filter0_d_270_60934)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.54581 4.97482L0.807686 4.39687L3.57956 3.84839L4.38134 0.979736L5.01362 3.8251L7.48821 4.39965L5.04271 4.97482L4.33708 7.31451L3.54581 4.97482Z" fill="#EF2929"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.54581 4.97482L0.807686 4.39687L3.57956 3.84839L4.38134 0.979736L5.01362 3.8251L7.48821 4.39965L5.04271 4.97482L4.33708 7.31451L3.54581 4.97482Z" fill="#FF0000"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 9H0V10H20V9ZM20 11H0V12H20V11Z" fill="#FAD615"/>
</g>
</g>
<defs>
<filter id="filter0_d_270_60934" x="-0.192314" y="-0.0202637" width="8.68053" height="8.33478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_270_60934"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_270_60934" result="shape"/>
</filter>
<clipPath id="clip0_270_60934">
<rect width="20" height="15" fill="white"/>
</clipPath>
</defs>
</svg>
