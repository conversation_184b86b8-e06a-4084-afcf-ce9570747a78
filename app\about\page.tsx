"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, Users, Award, Clock } from "lucide-react";
import TeamMember from "@/components/team-member";
import Timeline from "@/components/timeline";
import Link from "next/link";

export default function AboutPage() {
  return (
    <main className="pt-24 pb-16">
      {/* Hero Section */}
      <section className="container mx-auto px-4 mb-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              About Our Consultancy
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              We are a team of dedicated education consultants with a passion
              for helping students achieve their international education goals.
            </p>
            <div className="flex flex-wrap gap-4 mb-8">
              {aboutStats.map((stat, index) => (
                <div key={index} className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-primary" />
                  <span className="font-medium">{stat}</span>
                </div>
              ))}
            </div>
            <p className="text-muted-foreground mb-8">
              Our mission is to provide personalized guidance and support to
              students seeking international education opportunities. We believe
              that education is a transformative experience that opens doors to
              new possibilities and perspectives.
            </p>
            <Button size="lg">Our Services</Button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative h-[500px] rounded-lg overflow-hidden"
          >
            <Image
              src="/about-hero2.jpg"
              alt="Our team"
              fill
              className="object-cover"
            />
          </motion.div>
        </div>
      </section>

      {/* Values Section */}
      <section className="bg-muted/30 py-24 mb-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              The principles that guide our approach to education consultancy
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-none shadow-lg hover:shadow-xl transition-shadow duration-300 bg-background">
                  <CardContent className="p-8">
                    <div className="bg-primary/10 p-3 rounded-full w-fit mb-6">
                      {value.icon}
                    </div>
                    <h3 className="text-2xl font-bold mb-4">{value.title}</h3>
                    <p className="text-muted-foreground">{value.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="container mx-auto px-4 mb-24">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-4">Meet Our Team</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Experienced professionals dedicated to your educational success
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <TeamMember member={member} />
            </motion.div>
          ))}
        </div>
      </section>

      {/* History Timeline */}
      <section className="bg-muted/30 text-foreground py-24 mb-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-4">Our Journey</h2>
            <p className="text-xl text-background/80 max-w-2xl mx-auto">
              From our humble beginnings to becoming a leading education
              consultancy
            </p>
          </motion.div>

          <Timeline events={timelineEvents} />
        </div>
      </section>

      {/* Approach Tabs */}
      <section className="container mx-auto px-4 mb-24">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-4">Our Approach</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            How we help students achieve their international education goals
          </p>
        </motion.div>

        <Tabs defaultValue="personalized" className="max-w-4xl mx-auto ">
          <TabsList className="grid w-full grid-cols-3 bg-muted/30">
            <TabsTrigger value="personalized">
              Personalized Guidance
            </TabsTrigger>
            <TabsTrigger value="comprehensive">
              Comprehensive Support
            </TabsTrigger>
            <TabsTrigger value="transparent">Transparent Process</TabsTrigger>
          </TabsList>

          {approachTabs.map((tab) => (
            <TabsContent key={tab.value} value={tab.value} className="mt-8">
              <Card className="bg-muted/30">
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                    <div>
                      <h3 className="text-2xl font-bold mb-4">{tab.title}</h3>
                      <p className="text-muted-foreground mb-6">
                        {tab.description}
                      </p>
                      <ul className="space-y-2">
                        {tab.points.map((point, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                            <span>{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div className="relative h-[400px] rounded-lg overflow-hidden">
                      <Image
                        src={tab.image || "/placeholder.svg"}
                        alt={tab.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </section>

      {/* CTA Section */}
      <section className="bg-primary/10 py-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto text-center"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Start Your Educational Journey Today
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Contact our team of experts for personalized guidance on your
              international education goals.
            </p>
            <Button size="lg">
              <Link
                href={`https://wa.me/message/3OSI3VSB2QT4F1`}
                target="_blank"
                rel="noopener noreferrer"
                className="relative block"
                data-cursor-text="Chat with us"
              >
                Contact Us
              </Link>
            </Button>
          </motion.div>
        </div>
      </section>
    </main>
  );
}

const aboutStats = [
  "1000+ Students Placed Worldwide",
  "200+ Partner Universities",
  "25+ Countries",
  "100% Student Satisfaction",
];

const values = [
  {
    title: "Student-Centered Approach",
    description:
      "We prioritize the unique needs, goals, and circumstances of each student, providing personalized guidance and support throughout their educational journey.",
    icon: <Users className="h-6 w-6 text-primary" />,
  },
  {
    title: "Excellence and Integrity",
    description:
      "We maintain the highest standards of professionalism and ethical conduct in all our interactions, ensuring transparency and honesty in our advice and services.",
    icon: <Award className="h-6 w-6 text-primary" />,
  },
  {
    title: "Continuous Improvement",
    description:
      "We are committed to staying updated with the latest developments in international education and visa policies to provide the most accurate and relevant guidance.",
    icon: <Clock className="h-6 w-6 text-primary" />,
  },
];

const teamMembers = [
  {
    name: "Mr. Mannan Wasif",
    position: "Linkages & Liaison Manager",
    image: "/team/mannan.jpg",
    bio: `Responsible for establishing and maintaining relationships with partner universities and educational institutions worldwide. Expert in strategic management for building Strong Brands and implementing Change.`,
    specialization: "International University Partnerships",
    education: "Master's in International Relations",
    // certificates: [
    //   {
    //     tag: "Pier Certified Counsellor",
    //     name: "Quality Education Agent Counsellor",
    //     image: "/team/certificates/pier_arslan.png",
    //   },
    // ],
  },
  {
    name: "Mr. Muzaffar Hassan",
    position: "Business Development Manager",
    image: "/team/muzaffar.jpg",
    bio: "Focuses on expanding our services and reaching more students who can benefit from international education opportunities.",
    specialization: "Market Expansion & Strategic Partnerships",
    education: "MBA, Marketing",
  },
  {
    name: "Ms. Friza Riaz",
    position: "Senior Education Counselor & Compliance Office",
    image: "/team/friza.jpg",
    bio: "Provides comprehensive guidance to students while ensuring all processes comply with regulatory requirements.",
    specialization: "Education Counseling & Regulatory Compliance",
    education: "Master's in Education Management",
  },
  {
    name: "Ms. Zahra Jamil",
    position: "Certified English Language Instructor",
    image: "/team/zahra.jpg",
    bio: "Helps students improve their English language skills for academic success abroad and prepare for language proficiency tests.",
    specialization: "IELTS & TOEFL Preparation",
    education: "Master's in TESOL",
    certificates: [
      {
        tag: "Certified English Language Instructor",
        name: "Certified English Language Instructor",
        url: "https://www.credly.com/badges/3d492a78-fe69-4c7e-b545-37b8d0e0aa3f/public_url",
      },
    ],
  },
  {
    name: "Ms. Raafia Anwar",
    position: "Admissions Officer",
    image: "/team/raafia.jpg",
    bio: "Manages the application process and helps students prepare compelling applications to increase their chances of admission.",
    specialization: "University Admissions & Application Strategy",
    education: "Bachelor's in Business Administration",
  },
];

const timelineEvents = [
  {
    year: "2019",
    title: "Foundation",
    description:
      "Established with a mission to provide quality education consultancy services to aspiring international students.",
  },
  {
    year: "2020",
    title: "Virtual Consultations",
    description:
      "Adapted to global challenges by introducing comprehensive virtual consultation services.",
  },
  {
    year: "2021",
    title: "Digital Transformation",
    description:
      "Implemented advanced digital tools and platforms to enhance our service delivery and student experience.",
  },
  {
    year: "2022",
    title: "Official Registration",
    description:
      "Officially registered the company and opened our second office to accommodate growing demand for our services.",
  },
  {
    year: "2022",
    title: "International Partnerships",
    description:
      "Established partnerships with 50+ universities across the US, UK, Canada, and Australia.",
  },
  {
    year: "2023",
    title: "Scholarship Program",
    description:
      "Launched our scholarship guidance program, helping students secure over $1 million in financial aid in the first year.",
  },
  {
    year: "2023",
    title: "Global Expansion",
    description:
      "Expanded our services to cover 25+ countries and established regional offices in key markets.",
  },
];

const approachTabs = [
  {
    value: "personalized",
    title: "Personalized Guidance",
    description:
      "We understand that every student is unique, with different academic backgrounds, career aspirations, and personal circumstances. Our approach is tailored to meet the specific needs of each student.",
    points: [
      "One-on-one consultations to understand your goals and preferences",
      "Customized education plans based on your academic profile and career objectives",
      "Personalized university recommendations that match your requirements",
      "Individual guidance throughout the application and visa process",
    ],
    image: "/about-guidance.jpg",
  },
  {
    value: "comprehensive",
    title: "Comprehensive Support",
    description:
      "Our services cover every aspect of the international education journey, from university selection to pre-departure preparation, ensuring a smooth and successful transition.",
    points: [
      "End-to-end assistance with university applications and admissions",
      "Expert guidance on visa requirements and application procedures",
      "Scholarship and financial aid application support",
      "Pre-departure orientation and cultural preparation",
    ],
    image: "/about-support.jpg",
  },
  {
    value: "transparent",
    title: "Transparent Process",
    description:
      "We believe in maintaining complete transparency in our processes and communications, ensuring that students and parents are well-informed at every stage.",
    points: [
      "Clear explanation of all fees and services upfront",
      "Regular updates on application status and progress",
      "Honest assessment of admission chances and alternatives",
      "Transparent communication about visa requirements and success rates",
    ],
    image: "/about-process.svg",
  },
];
