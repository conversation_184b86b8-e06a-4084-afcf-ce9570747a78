"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { GraduationCap } from "lucide-react";

export default function CustomCursor() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [cursorVariant, setCursorVariant] = useState("default");
  const [cursorText, setCursorText] = useState("");

  useEffect(() => {
    const mouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY,
      });
    };

    window.addEventListener("mousemove", mouseMove);

    const handleMouseOver = () => {
      const elements = document.querySelectorAll("[data-cursor-text]");

      elements.forEach((element) => {
        element.addEventListener("mouseenter", () => {
          setCursorVariant("text");
          setCursorText(element.getAttribute("data-cursor-text") || "");
        });
        element.addEventListener("mouseleave", () => {
          setCursorVariant("default");
          setCursorText("");
        });
      });

      const links = document.querySelectorAll(
        'a[href], button, [role="button"], [role="a"], Link'
      );
      links.forEach((link) => {
        link.addEventListener("mouseenter", () => setCursorVariant("hover"));
        link.addEventListener("mouseleave", () => setCursorVariant("default"));
      });
    };

    handleMouseOver();

    return () => {
      window.removeEventListener("mousemove", mouseMove);
    };
  }, []);

  const variants = {
    default: {
      x: mousePosition.x - 16,
      y: mousePosition.y - 16,
      scale: 1,
      backgroundColor: "rgba(0, 0, 0, 0)",
    },
    hover: {
      x: mousePosition.x - 24,
      y: mousePosition.y - 24,
      scale: 1.5,
      backgroundColor: "rgba(0, 0, 0, 0)",
      mixBlendMode: "difference" as const,
    },
    text: {
      x: mousePosition.x - 48,
      y: mousePosition.y - 48,
      scale: 2,
      backgroundColor: "rgba(0, 0, 0, 0)",
      mixBlendMode: "difference" as const,
    },
  };

  return (
    <motion.div
      className="fixed top-0 left-0 pointer-events-none z-[1002] hidden md:flex items-center justify-center text-xs text-white font-medium"
      variants={variants}
      animate={cursorVariant}
      transition={{ type: "spring", stiffness: 800, damping: 30 }} // Increased speed with higher stiffness and lower damping
    >
      <div className="relative">
        <GraduationCap
          className="text-primary"
          style={{
            width:
              cursorVariant === "text"
                ? "48px"
                : cursorVariant === "hover"
                ? "32px"
                : "24px",
            height:
              cursorVariant === "text"
                ? "48px"
                : cursorVariant === "hover"
                ? "32px"
                : "24px",
            filter: "drop-shadow(0 0 8px rgba(137, 184, 63, 0.5))",
          }}
        />
        {cursorVariant === "text" && (
          <span className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 whitespace-nowrap bg-primary/80 px-2 py-1 rounded text-white">
            {cursorText}
          </span>
        )}
      </div>
    </motion.div>
  );
}
