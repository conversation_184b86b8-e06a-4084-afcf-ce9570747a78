# three-globe - npm

2.42.3 • Public • Published 15 days ago

- [Readme](?activeTab=readme)
- [Code Beta](?activeTab=code)
- [17 Dependencies](?activeTab=dependencies)
- [23 Dependents](?activeTab=dependents)
- [259 Versions](?activeTab=versions)

## ThreeJS Globe Visualization

[![NPM package](https://camo.githubusercontent.com/59f1c5f84223c1df46334a5cfe49258d7d9da9d5e1cd23f71ded41eb4f5941eb/68747470733a2f2f696d672e736869656c64732e696f2f6e706d2f762f74687265652d676c6f6265)](https://npmjs.org/package/three-globe) [![Build Size](https://camo.githubusercontent.com/1ba38ae685eb5c04d0e1284badf6779f0048c538fcad913c04339b37cd587120/68747470733a2f2f696d672e736869656c64732e696f2f62756e646c6570686f6269612f6d696e7a69702f74687265652d676c6f6265)](https://bundlephobia.com/result?p=three-globe) [![NPM Downloads](https://camo.githubusercontent.com/201db9a97d63eae01c4ea6a2611f5f78517715541397a6a136e10af1f788de46/68747470733a2f2f696d672e736869656c64732e696f2f6e706d2f64742f74687265652d676c6f6265)](https://www.npmtrends.com/three-globe)

[![](https://camo.githubusercontent.com/695378f98df630b0727da03e0d9f707cf07b2336733ecf4f803696766699c92c/68747470733a2f2f76617374757269616e6f2e6769746875622e696f2f74687265652d676c6f62652f6578616d706c652f707265766965772e706e67)](//vasturiano.github.io/three-globe/example/basic/)

Largely inspired by [WebGL Globe](https://experiments.withgoogle.com/chrome/globe), this is a [ThreeJS](https://threejs.org/) WebGL class to represent data visualization layers on a globe, using a spherical projection.

See also the [standalone version](https://github.com/vasturiano/globe.gl), and the [react-three-fiber bindings](https://github.com/vasturiano/r3f-globe).

#### Check out the examples:

- [Basic](https://vasturiano.github.io/three-globe/example/basic/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/basic/index.html))
- [Arc Links](https://vasturiano.github.io/three-globe/example/links/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/links/index.html))
- [Country Polygons](https://vasturiano.github.io/three-globe/example/country-polygons/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/country-polygons/index.html))
- [Path lines](https://vasturiano.github.io/three-globe/example/paths/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/paths/index.html))
- [Heatmap](https://vasturiano.github.io/three-globe/example/heatmap/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/heatmap/index.html))
- [Hexagonal Binning](https://vasturiano.github.io/three-globe/example/hexbin/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/hexbin/index.html))
- [Hexed Country Polygons](https://vasturiano.github.io/three-globe/example/hexed-polygons/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/hexed-polygons/index.html))
- [Tiles](https://vasturiano.github.io/three-globe/example/tiles/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/tiles/index.html))
- [Ripple Rings](https://vasturiano.github.io/three-globe/example/ripples/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/ripples/index.html))
- [Clouds](https://vasturiano.github.io/three-globe/example/clouds/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/clouds/index.html))
- [Day/Night Cycle](https://vasturiano.github.io/three-globe/example/day-night-cycle/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/day-night-cycle/index.html))
- [Solar Terminator](https://vasturiano.github.io/three-globe/example/solar-terminator/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/solar-terminator/index.html))
- [Labels](https://vasturiano.github.io/three-globe/example/labels/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/labels/index.html))
- [HTML Markers](https://vasturiano.github.io/three-globe/example/html-markers/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/html-markers/index.html))
- [Satellites](https://vasturiano.github.io/three-globe/example/satellites/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/satellites/index.html))
- [Tiled Map Engine](https://vasturiano.github.io/three-globe/example/tile-engine/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/tile-engine/index.html))
- [Custom Globe Material](https://vasturiano.github.io/three-globe/example/custom-material/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/custom-material/index.html))
- [Custom Layer](https://vasturiano.github.io/three-globe/example/custom/) ([source](https://github.com/vasturiano/three-globe/blob/master/example/custom/index.html))

## Quick start

```
import ThreeGlobe from 'three-globe';
```

or using a _script_ tag

```
<script src="//cdn.jsdelivr.net/npm/three-globe"></script>
```

then

```
const myGlobe = new ThreeGlobe()
  .globeImageUrl(myImageUrl)
  .pointsData(myData);

const myScene = new THREE.Scene();
myScene.add(myGlobe);
```

## API reference

- [Initialisation](#initialisation)
- [Globe Layer](#globe-layer)
- [Points Layer](#points-layer)
- [Arcs Layer](#arcs-layer)
- [Polygons Layer](#polygons-layer)
- [Paths Layer](#paths-layer)
- [Heatmaps Layer](#heatmaps-layer)
- [Hex Bin Layer](#hex-bin-layer)
- [Hexed Polygons Layer](#hexed-polygons-layer)
- [Tiles Layer](#tiles-layer)
- [Particles Layer](#particles-layer)
- [Rings Layer](#rings-layer)
- [Labels Layer](#labels-layer)
- [HTML Elements Layer](#html-elements-layer)
- [3D Objects Layer](#3d-objects-layer)
- [Custom Layer](#custom-layer)
- [Utility](#utility)
- [Render Options](#render-options)

### Initialisation

```
new ThreeGlobe({ configOptions })
```

- Config options: waitForGlobeReady: boolean
  - Description: Whether to wait until the globe wrapping image has been fully loaded before rendering the globe or any of the data layers.
  - Default: true
- Config options: animateIn: boolean
  - Description: Whether to animate the globe initialization, by scaling and rotating the globe into its initial position.
  - Default: true

### Globe Layer

- Method: globeImageUrl([url])
  - Description: Getter/setter for the URL of the image used in the material that wraps the globe. This image should follow an equirectangular projection. If no image is provided, the globe is represented as a black sphere.
  - Default: null
- Method: bumpImageUrl([url])
  - Description: Getter/setter for the URL of the image used to create a bump map in the material, to represent the globe's terrain. This image should follow an equirectangular projection.
  - Default: null
- Method: showGlobe([boolean])
  - Description: Getter/setter for whether to show the globe surface itself.
  - Default: true
- Method: showGraticules([boolean])
  - Description: Getter/setter for whether to show a graticule grid demarking latitude and longitude lines at every 10 degrees.
  - Default: false
- Method: showAtmosphere([boolean])
  - Description: Getter/setter for whether to show a bright halo surrounding the globe, representing the atmosphere.
  - Default: true
- Method: atmosphereColor([str])
  - Description: Getter/setter for the color of the atmosphere.
  - Default: lightskyblue
- Method: atmosphereAltitude([str])
  - Description: Getter/setter for the max altitude of the atmosphere, in terms of globe radius units.
  - Default: 0.15
- Method: globeTileEngineUrl([fn(x, y, l)])
  - Description: Getter/setter for the function that defines the URL of the slippy map tile engine to cover the globe surface. The slippy map coordinates x, y and l (zoom level) are passed as arguments and the function is expected to return a URL string. A falsy value will disable the tiling engine.
  - Default: -
- Method: globeTileEngineMaxZoom([num])
  - Description: Getter/setter for the maximum zoom level of the tile engine.
  - Default: 17
- Method: globeMaterial([material])
  - Description: Getter/setter of the ThreeJS material used to wrap the globe. Can be used for more advanced styling of the globe, like in this example.
  - Default: MeshPhongMaterial
- Method: onGlobeReady(fn)
  - Description: Callback function to invoke immediately after the globe has been initialized and visible on the scene.
  - Default: -

### Points Layer

- Method: pointsData([array])
  - Description: Getter/setter for the list of points to represent in the points map layer. Each point is displayed as a cylindrical 3D object rising perpendicularly from the surface of the globe.
  - Default: []
- Method: pointLat([num, str or fn])
  - Description: Point object accessor function, attribute or a numeric constant for the cylinder's center latitude coordinate.
  - Default: lat
- Method: pointLng([num, str or fn])
  - Description: Point object accessor function, attribute or a numeric constant for the cylinder's center longitude coordinate.
  - Default: lng
- Method: pointColor([str or fn])
  - Description: Point object accessor function or attribute for the cylinder color.
  - Default: () => '#ffffaa'
- Method: pointAltitude([num, str or fn])
  - Description: Point object accessor function, attribute or a numeric constant for the cylinder's altitude in terms of globe radius units (0 = 0 altitude (flat circle), 1 = globe radius).
  - Default: 0.1
- Method: pointRadius([num, str or fn])
  - Description: Point object accessor function, attribute or a numeric constant for the cylinder's radius, in angular degrees.
  - Default: 0.25
- Method: pointResolution([num])
  - Description: Getter/setter for the radial geometric resolution of each cylinder, expressed in how many slice segments to divide the circumference. Higher values yield smoother cylinders.
  - Default: 12
- Method: pointsMerge([boolean])
  - Description: Getter/setter for whether to merge all the point meshes into a single ThreeJS object, for improved rendering performance. Visually both options are equivalent, setting this option only affects the internal organization of the ThreeJS objects.
  - Default: false
- Method: pointsTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate point changes involving geometry modifications. A value of 0 will move the objects immediately to their final position. New objects are animated by scaling them from the ground up. Only works if pointsMerge is disabled.
  - Default: 1000

### Arcs Layer

- Method: arcsData([array])
  - Description: Getter/setter for the list of links to represent in the arcs map layer. Each link is displayed as an arc line that rises from the surface of the globe, connecting the start and end coordinates.
  - Default: []
- Method: arcStartLat([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the line's start latitude coordinate.
  - Default: startLat
- Method: arcStartLng([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the line's start longitude coordinate.
  - Default: startLng
- Method: arcEndLat([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the line's end latitude coordinate.
  - Default: endLat
- Method: arcEndLng([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the line's end longitude coordinate.
  - Default: endLng
- Method: arcColor([str, [str, ...] or fn])
  - Description: Arc object accessor function or attribute for the line's color. Also supports color gradients by passing an array of colors, or a color interpolator function.
  - Default: () => '#ffffaa'
- Method: arcAltitude([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the arc's maximum altitude (ocurring at the half-way distance between the two points) in terms of globe radius units (0 = 0 altitude (ground line), 1 = globe radius). If a value of null or undefined is used, the altitude is automatically set proportionally to the distance between the two points, according to the scale set in arcAltitudeAutoScale.
  - Default: null
- Method: arcAltitudeAutoScale([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the scale of the arc's automatic altitude, in terms of units of the great-arc distance between the two points. A value of 1 indicates the arc should be as high as its length on the ground. Only applicable if arcAltitude is not set.
  - Default: 0.5
- Method: arcStroke([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the line's diameter, in angular degrees. A value of null or undefined will render a ThreeJS Line whose width is constant (1px) regardless of the camera distance. Otherwise, a TubeGeometry is used.
  - Default: null
- Method: arcCurveResolution([num])
  - Description: Getter/setter for the arc's curve resolution, expressed in how many straight line segments to divide the curve by. Higher values yield smoother curves.
  - Default: 64
- Method: arcCircularResolution([num])
  - Description: Getter/setter for the radial geometric resolution of each line, expressed in how many slice segments to divide the tube's circumference. Only applicable when using Tube geometries (defined arcStroke).
  - Default: 6
- Method: arcDashLength([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the length of the dashed segments in the arc, in terms of relative length of the whole line (1 = full line length).
  - Default: 1
- Method: arcDashGap([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the length of the gap between dash segments, in terms of relative line length.
  - Default: 0
- Method: arcDashInitialGap([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the length of the initial gap before the first dash segment, in terms of relative line length.
  - Default: 0
- Method: arcDashAnimateTime([num, str or fn])
  - Description: Arc object accessor function, attribute or a numeric constant for the time duration (in ms) to animate the motion of dash positions from the start to the end point for a full line length. A value of 0 disables the animation.
  - Default: 0
- Method: arcsTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate arc changes involving geometry modifications. A value of 0 will move the arcs immediately to their final position. New arcs are animated by rising them from the ground up.
  - Default: 1000

### Polygons Layer

- Method: polygonsData([array])
  - Description: Getter/setter for the list of polygon shapes to represent in the polygons map layer. Each polygon is displayed as a shaped cone that extrudes from the surface of the globe.
  - Default: []
- Method: polygonGeoJsonGeometry([str or fn])
  - Description: Polygon object accessor function or attribute for the GeoJson geometry specification of the polygon's shape. The returned value should have a minimum of two fields: type and coordinates. Only GeoJson geometries of type Polygon or MultiPolygon are supported, other types will be skipped.
  - Default: geometry
- Method: polygonCapColor([str or fn])
  - Description: Polygon object accessor function or attribute for the color of the top surface.
  - Default: () => '#ffffaa'
- Method: polygonCapMaterial([material, str or fn])
  - Description: Polygon object accessor function, attribute or material object for the ThreeJS material to use in the top surface. This property takes precedence over polygonCapColor, which will be ignored if both are defined.
  - Default: -
- Method: polygonSideColor([str or fn])
  - Description: Polygon object accessor function or attribute for the color of the cone sides.
  - Default: () => '#ffffaa'
- Method: polygonSideMaterial([material, str or fn])
  - Description: Polygon object accessor function, attribute or material object for the ThreeJS material to use in the cone sides. This property takes precedence over polygonSideColor, which will be ignored if both are defined.
  - Default: -
- Method: polygonStrokeColor([str or fn])
  - Description: Polygon object accessor function or attribute for the color to stroke the polygon perimeter. A falsy value will disable the stroking.
  - Default: -
- Method: polygonAltitude([num, str or fn])
  - Description: Polygon object accessor function, attribute or a numeric constant for the polygon cone's altitude in terms of globe radius units (0 = 0 altitude (flat polygon), 1 = globe radius).
  - Default: 0.01
- Method: polygonCapCurvatureResolution([num, str or fn])
  - Description: Polygon object accessor function, attribute or a numeric constant for the resolution (in angular degrees) of the cap surface curvature. The finer the resolution, the more the polygon is fragmented into smaller faces to approximate the spheric surface, at the cost of performance.
  - Default: 5
- Method: polygonsTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate polygon altitude changes. A value of 0 will size the cone immediately to their final altitude. New polygons are animated by rising them from the ground up.
  - Default: 1000

### Paths Layer

- Method: pathsData([array])
  - Description: Getter/setter for the list of lines to represent in the paths map layer. Each path is displayed as a line that connects all the coordinate pairs in the path array.
  - Default: []
- Method: pathPoints([array, str or fn])
  - Description: Path object accessor function, attribute or an array for the set of points that define the path line. By default, each path point is assumed to be a 2-position array ([<lat>, <lon>]). This default behavior can be modified using the pathPointLat and pathPointLng methods.
  - Default: pnts => pnts
- Method: pathPointLat([num, str or fn])
  - Description: Path point object accessor function, attribute or a numeric constant for the latitude coordinate.
  - Default: arr => arr[0]
- Method: pathPointLng([num, str or fn])
  - Description: Path point object accessor function, attribute or a numeric constant for the longitude coordinate.
  - Default: arr => arr[1]
- Method: pathPointAlt([num, str or fn])
  - Description: Path point object accessor function, attribute or a numeric constant for the point altitude, in terms of globe radius units (0 = 0 altitude (ground), 1 = globe radius).
  - Default: 0.001
- Method: pathResolution([num])
  - Description: Getter/setter for the path's angular resolution, in lat/lng degrees. If the ground distance (excluding altitude) between two adjacent path points is larger than this value, the line segment will be interpolated in order to approximate the curvature of the sphere surface. Lower values yield more perfectly curved lines, at the cost of performance.
  - Default: 2
- Method: pathColor([str, [str, ...] or fn])
  - Description: Path object accessor function or attribute for the line's color. Also supports color gradients by passing an array of colors, or a color interpolator function. Transparent colors are not supported in Fat Lines with set width.
  - Default: () => '#ffffaa'
- Method: pathStroke([num, str or fn])
  - Description: Path object accessor function, attribute or a numeric constant for the line's diameter, in angular degrees. A value of null or undefined will render a ThreeJS Line whose width is constant (1px) regardless of the camera distance. Otherwise, a FatLine is used.
  - Default: null
- Method: pathDashLength([num, str or fn])
  - Description: Path object accessor function, attribute or a numeric constant for the length of the dashed segments in the path line, in terms of relative length of the whole line (1 = full line length).
  - Default: 1
- Method: pathDashGap([num, str or fn])
  - Description: Path object accessor function, attribute or a numeric constant for the length of the gap between dash segments, in terms of relative line length.
  - Default: 0
- Method: pathDashInitialGap([num, str or fn])
  - Description: Path object accessor function, attribute or a numeric constant for the length of the initial gap before the first dash segment, in terms of relative line length.
  - Default: 0
- Method: pathDashAnimateTime([num, str or fn])
  - Description: Path object accessor function, attribute or a numeric constant for the time duration (in ms) to animate the motion of dash positions from the start to the end point for a full line length. A value of 0 disables the animation.
  - Default: 0
- Method: pathTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate path changes. A value of 0 will move the paths immediately to their final position. New paths are animated from start to end.
  - Default: 1000

### Heatmaps Layer

- Method: heatmapsData([array])
  - Description: Getter/setter for the list of heatmap datasets to represent in the heatmaps map layer. Each set of points is represented as an individual global heatmap with varying color and/or altitude, according to the point density. It uses a Gaussian KDE to perform the density estimation, based on the great-arc distance between points.
  - Default: []
- Method: heatmapPoints([array, str or fn])
  - Description: Heatmap object accessor function, attribute or an array for the set of points that define the heatmap. By default, each point is assumed to be a 2-position array ([<lat>, <lon>]). This default behavior can be modified using the heatmapPointLat and heatmapPointLng methods.
  - Default: pnts => pnts
- Method: heatmapPointLat([num, str or fn])
  - Description: Heatmap point object accessor function, attribute or a numeric constant for the latitude coordinate.
  - Default: arr => arr[0]
- Method: heatmapPointLng([num, str or fn])
  - Description: Heatmap point object accessor function, attribute or a numeric constant for the longitude coordinate.
  - Default: arr => arr[1]
- Method: heatmapPointWeight([num, str or fn])
  - Description: Heatmap point object accessor function, attribute or a numeric constant for the weight of the point. The weight of a point determines its influence on the density of the surrounding area.
  - Default: 1
- Method: heatmapBandwidth([num, str or fn])
  - Description: Heatmap object accessor function, attribute or a numeric constant for the heatmap bandwidth, in angular degrees. The bandwidth is an internal parameter of the Gaussian kernel function and defines how localized is the influence of a point on distant locations. A narrow bandwidth leads to a more spiky representation, while a broad one has smoother curves.
  - Default: 2.5
- Method: heatmapColorFn([str or fn])
  - Description: Heatmap object accessor function or attribute for the color interpolator function to represent density in the heatmap. This function should receive a number between 0 and 1 (or potentially higher if saturation > 1), and return a color string.
  - Default: Turbo colormap interpolator with fading opacity
- Method: heatmapColorSaturation([num, str or fn])
  - Description: Heatmap object accessor function, attribute or a numeric constant for the color scale saturation. The saturation is a multiplier of the normalized density value ([0,1]) before passing it to the color interpolation function. It can be used to dampen outlier peaks in density and bring the data floor into view.
  - Default: 1.5
- Method: heatmapBaseAltitude([num, str or fn])
  - Description: Heatmap object accessor function, attribute or a numeric constant for the heatmap base floor altitude in terms of globe radius units (0 = 0 altitude, 1 = globe radius).
  - Default: 0.01
- Method: heatmapTopAltitude([num, str or fn])
  - Description: Heatmap object accessor function, attribute or a numeric constant for the heatmap top peak altitude in terms of globe radius units (0 = 0 altitude, 1 = globe radius). An equal value to the base altitude will yield a surface flat heatmap. If a top altitude is set, the variations in density will be used to define the altitude curves between base and top.
  - Default: -
- Method: heatmapsTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate heatmap changes. A value of 0 will set the heatmap colors/altitudes immediately in their final position. New heatmaps are animated by rising them from the ground up and gently fading in through the color scale.
  - Default: 0

### Hex Bin Layer

- Method: hexBinPointsData([array])
  - Description: Getter/setter for the list of points to aggregate using the hex bin map layer. Each point is added to an hexagonal prism 3D object that represents all the points within a tesselated portion of the space.
  - Default: []
- Method: hexBinPointLat([num, str or fn])
  - Description: Point object accessor function, attribute or a numeric constant for the latitude coordinate.
  - Default: lat
- Method: hexBinPointLng([num, str or fn])
  - Description: Point object accessor function, attribute or a numeric constant for the longitude coordinate.
  - Default: lng
- Method: hexBinPointWeight([num, str or fn])
  - Description: Point object accessor function, attribute or a numeric constant for the weight of the point. Weights for points in the same bin are summed and determine the hexagon default altitude.
  - Default: 1
- Method: hexBinResolution([num])
  - Description: The geographic binning resolution as defined by H3. Determines the area of the hexagons that tesselate the globe's surface. Accepts values between 0 and 15. Level 0 partitions the earth in 122 (mostly) hexagonal cells. Each subsequent level sub-divides the previous in roughly 7 hexagons.
  - Default: 4
- Method: hexMargin([num or fn])
  - Description: The radial margin of each hexagon. Margins above 0 will create gaps between adjacent hexagons and serve only a visual purpose, as the data points within the margin still contribute to the hexagon's data. The margin is specified in terms of fraction of the hexagon's surface diameter. Values below 0 or above 1 are disadvised. This property also supports using an accessor method based on the hexagon's aggregated data, following the syntax: hexMargin(({ points, sumWeight, center: { lat, lng }}) => ...). This method should return a numeric constant.
  - Default: 0.2
- Method: hexAltitude([num or fn])
  - Description: The altitude of each hexagon, in terms of globe radius units (0 = 0 altitude (flat hexagon), 1 = globe radius). This property also supports using an accessor method based on the hexagon's aggregated data, following the syntax: hexAltitude(({ points, sumWeight, center: { lat, lng }}) => ...). This method should return a numeric constant.
  - Default: ({ sumWeight }) => sumWeight \* 0.01
- Method: hexTopCurvatureResolution([num])
  - Description: The resolution (in angular degrees) of the top surface curvature. The finer the resolution, the more the top area is fragmented into smaller faces to approximate the spheric surface, at the cost of performance.
  - Default: 5
- Method: hexTopColor([fn])
  - Description: Accessor method for each hexagon's top color. The method should follow the signature: hexTopColor(({ points, sumWeight, center: { lat, lng }}) => ...) and return a color string.
  - Default: () => '#ffffaa'
- Method: hexSideColor([fn])
  - Description: Accessor method for each hexagon's side color. The method should follow the signature: hexSideColor(({ points, sumWeight, center: { lat, lng }}) => ...) and return a color string.
  - Default: () => '#ffffaa'
- Method: hexBinMerge([boolean])
  - Description: Getter/setter for whether to merge all the hexagon meshes into a single ThreeJS object, for improved rendering performance. Visually both options are equivalent, setting this option only affects the internal organization of the ThreeJS objects.
  - Default: false
- Method: hexTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate hexagon changes related to geometry modifications (altitude, radius). A value of 0 will move the hexagons immediately to their final position. New hexagons are animated by scaling them from the ground up. Only works if hexBinMerge is disabled.
  - Default: 1000

### Hexed Polygons Layer

- Method: hexPolygonsData([array])
  - Description: Getter/setter for the list of polygon shapes to represent in the hexed polygons map layer. Each polygon is displayed as a tesselated group of hexagons that approximate the polygons shape according to the resolution specified in hexPolygonResolution.
  - Default: []
- Method: hexPolygonGeoJsonGeometry([str or fn])
  - Description: Hexed polygon object accessor function or attribute for the GeoJson geometry specification of the polygon's shape. The returned value should have a minimum of two fields: type and coordinates. Only GeoJson geometries of type Polygon or MultiPolygon are supported, other types will be skipped.
  - Default: geometry
- Method: hexPolygonColor([str or fn])
  - Description: Hexed polygon object accessor function or attribute for the color of each hexagon in the polygon.
  - Default: () => '#ffffaa'
- Method: hexPolygonAltitude([num, str or fn])
  - Description: Hexed polygon object accessor function, attribute or a numeric constant for the polygon's hexagons altitude in terms of globe radius units (0 = 0 altitude, 1 = globe radius).
  - Default: 0.001
- Method: hexPolygonResolution([num, str or fn])
  - Description: Hexed polygon object accessor function, attribute or a numeric constant for the geographic binning resolution as defined by H3. Determines the area of the hexagons that tesselate the globe's surface. Accepts values between 0 and 15. Level 0 partitions the earth in 122 (mostly) hexagonal cells. Each subsequent level sub-divides the previous in roughly 7 hexagons.
  - Default: 3
- Method: hexPolygonMargin([num, str or fn])
  - Description: Hexed polygon object accessor function, attribute or a numeric constant for the radial margin of each hexagon. Margins above 0 will create gaps between adjacent hexagons within a polygon. The margin is specified in terms of fraction of the hexagon's surface diameter. Values below 0 or above 1 are disadvised.
  - Default: 0.2
- Method: hexPolygonUseDots([boolean, str or fn])
  - Description: Hexed polygon object accessor function, attribute or a boolean constant for whether to represent each polygon point as a circular dot instead of an hexagon.
  - Default: false
- Method: hexPolygonCurvatureResolution([num, str or fn])
  - Description: Hexed polygon object accessor function, attribute or a numeric constant for the resolution (in angular degrees) of each hexed polygon surface curvature. The finer the resolution, the more the polygon hexes are fragmented into smaller faces to approximate the spheric surface, at the cost of performance.
  - Default: 5
- Method: hexPolygonDotResolution([num, str or fn])
  - Description: Hexed polygon object accessor function, attribute or a numeric constant for the resolution of each circular dot, expressed in how many slice segments to divide the circumference. Higher values yield smoother circles, at the cost of performance. This is only applicable in dot representation mode.
  - Default: 12
- Method: hexPolygonsTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate hexed polygons altitude and margin changes. A value of 0 will move the hexagons immediately to their final state. New hexed polygons are animated by sizing each hexagon from 0 radius.
  - Default: 0

### Tiles Layer

- Method: tilesData([array])
  - Description: Getter/setter for the list of tiles to represent in the tiles map layer. Each tile is displayed as a spherical surface segment. The segments can be placed side-by-side for a tiled surface and each can be styled separately.
  - Default: []
- Method: tileLat([num, str or fn])
  - Description: Tile object accessor function, attribute or a numeric constant for the segment's centroid latitude coordinate.
  - Default: lat
- Method: tileLng([num, str or fn])
  - Description: Tile object accessor function, attribute or a numeric constant for the segment's centroid longitude coordinate.
  - Default: lng
- Method: tileAltitude([num, str or fn])
  - Description: Tile object accessor function, attribute or a numeric constant for the segment's altitude in terms of globe radius units.
  - Default: 0.01
- Method: tileWidth([num, str or fn])
  - Description: Tile object accessor function, attribute or a numeric constant for the segment's longitudinal width, in angular degrees.
  - Default: 1
- Method: tileHeight([num, str or fn])
  - Description: Tile object accessor function, attribute or a numeric constant for the segment's latitudinal height, in angular degrees.
  - Default: 1
- Method: tileUseGlobeProjection([boolean, str or fn])
  - Description: Tile object accessor function, attribute or a boolean constant for whether to use the globe's projection to shape the segment to its relative tiled position (true), or break free from this projection and shape the segment as if it would be laying directly on the equatorial perimeter (false).
  - Default: true
- Method: tileMaterial([material, str or fn])
  - Description: Tile object accessor function, attribute or material object for the ThreeJS material used to style the segment's surface.
  - Default: () => new MeshLambertMaterial({ color: '#ffbb88' })
- Method: tileCurvatureResolution([num, str or fn])
  - Description: Tile object accessor function, attribute or a numeric constant for the resolution (in angular degrees) of the surface curvature. The finer the resolution, the more the tile geometry is fragmented into smaller faces to approximate the spheric surface, at the cost of performance.
  - Default: 5
- Method: tilesTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate tile changes involving geometry modifications. A value of 0 will move the tiles immediately to their final position. New tiles are animated by scaling them from the centroid outwards.
  - Default: 1000

### Particles Layer

- Method: particlesData([array])
  - Description: Getter/setter for the list of particle sets to represent in the particles map layer. Each particle set is displayed as a group of Points. Each point in the group is a geometry vertex and can be individually positioned anywhere relative to the globe.
  - Default: []
- Method: particlesList([str or fn])
  - Description: Particle set accessor function or attribute for the list of particles in the set. By default, the data structure is expected to be an array of arrays of individual particle objects.
  - Default: d => d
- Method: particleLat([num, str or fn])
  - Description: Particle object accessor function, attribute or a numeric constant for the latitude coordinate.
  - Default: lat
- Method: particleLng([num, str or fn])
  - Description: Particle object accessor function, attribute or a numeric constant for the longitude coordinate.
  - Default: lng
- Method: particleAltitude([num, str or fn])
  - Description: Particle object accessor function, attribute or a numeric constant for the altitude in terms of globe radius units.
  - Default: 0.01
- Method: particlesSize([num, str or fn])
  - Description: Particle set accessor function, attribute or a numeric constant for the size of all the particles in the group.
  - Default: 0.5
- Method: particlesSizeAttenuation([boolean, str or fn])
  - Description: Particle set accessor function, attribute or a boolean constant for whether the size of each particle on the screen should be attenuated according to the distance to the camera.
  - Default: true
- Method: particlesColor([str or fn])
  - Description: Particle set accessor function or attribute for the color of all the particles in the group. This setting will be ignored if particlesTexture is defined.
  - Default: white
- Method: particlesTexture([str or fn])
  - Description: Particle set accessor function or attribute for the Texture to be applied to all the particles in the group.
  - Default: -

### Rings Layer

- Method: ringsData([array])
  - Description: Getter/setter for the list of self-propagating ripple rings to represent in the rings map layer. Each data point is displayed as an animated set of concentric circles that propagate outwards from (or inwards to) a central point through the spherical surface.
  - Default: []
- Method: ringLat([num, str or fn])
  - Description: Ring object accessor function, attribute or a numeric constant for each circle's center latitude coordinate.
  - Default: lat
- Method: ringLng([num, str or fn])
  - Description: Ring object accessor function, attribute or a numeric constant for each circle's center longitude coordinate.
  - Default: lng
- Method: ringAltitude([num, str or fn])
  - Description: Ring object accessor function, attribute or a numeric constant for the circle's altitude in terms of globe radius units.
  - Default: 0.0015
- Method: ringColor([str, [str, ...] or fn])
  - Description: Ring object accessor function or attribute for the stroke color of each ring. Also supports radial color gradients by passing an array of colors, or a color interpolator function.
  - Default: () => '#ffffaa'
- Method: ringResolution([num])
  - Description: Getter/setter for the geometric resolution of each circle, expressed in how many slice segments to divide the circumference. Higher values yield smoother circles.
  - Default: 64
- Method: ringMaxRadius([num, str or fn])
  - Description: Ring object accessor function, attribute or a numeric constant for the maximum outer radius of the circles, at which the rings stop propagating and are removed. Defined in angular degrees.
  - Default: 2
- Method: ringPropagationSpeed([num, str or fn])
  - Description: Ring object accessor function, attribute or a numeric constant for the propagation velocity of the rings, defined in degrees/second. Setting a negative value will invert the direction and cause the rings to propagate inwards from the maxRadius.
  - Default: 1
- Method: ringRepeatPeriod([num, str or fn])
  - Description: Ring object accessor function, attribute or a numeric constant for the interval of time (in ms) to wait between consecutive auto-generated concentric circles. A value less or equal than 0 will disable the repetition and emit a single ring.
  - Default: 700

### Labels Layer

- Method: labelsData([array])
  - Description: Getter/setter for the list of label objects to represent in the labels map layer.
  - Default: []
- Method: labelLat([num, str or fn])
  - Description: Label object accessor function, attribute or a numeric constant for the latitude coordinate.
  - Default: lat
- Method: labelLng([num, str or fn])
  - Description: Label object accessor function, attribute or a numeric constant for the longitude coordinate.
  - Default: lng
- Method: labelText([str or fn])
  - Description: Label object accessor function or attribute for the label text.
  - Default: text
- Method: labelColor([str or fn])
  - Description: Label object accessor function or attribute for the label color.
  - Default: () => 'lightgrey'
- Method: labelAltitude([num, str or fn])
  - Description: Label object accessor function, attribute or a numeric constant for the label altitude in terms of globe radius units.
  - Default: 0
- Method: labelSize([num, str or fn])
  - Description: Label object accessor function, attribute or a numeric constant for the label text height, in angular degrees.
  - Default: 0.5
- Method: labelTypeFace([typeface ])
  - Description: Getter/setter for the text font typeface JSON object. Supports any typeface font generated by Facetype.js.
  - Default: helvetiker regular
- Method: labelRotation([num, str or fn])
  - Description: Label object accessor function, attribute or a numeric constant for the label rotation in degrees. The rotation is performed clockwise along the axis of its latitude parallel plane.
  - Default: 0
- Method: labelResolution([num])
  - Description: Getter/setter for the text geometric resolution of each label, expressed in how many segments to use in the text curves. Higher values yield smoother labels.
  - Default: 3
- Method: labelIncludeDot([boolean, str or fn])
  - Description: Label object accessor function, attribute or a boolean constant for whether to include a dot marker next to the text indicating the exact lat, lng coordinates of the label. If enabled the text will be rendered offset from the dot.
  - Default: true
- Method: labelDotRadius([num, str or fn])
  - Description: Label object accessor function, attribute or a numeric constant for the radius of the dot marker, in angular degrees.
  - Default: 0.1
- Method: labelDotOrientation([str or fn])
  - Description: Label object accessor function or attribute for the orientation of the label if the dot marker is present. Possible values are right, top and bottom.
  - Default: () => 'bottom'
- Method: labelsTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate label changes involving position modifications (lat, lng, altitude, rotation). A value of 0 will move the labels immediately to their final position. New labels are animated by scaling their size.
  - Default: 1000

### HTML Elements Layer

- Method: htmlElementsData([array])
  - Description: Getter/setter for the list of objects to represent in the HTML elements map layer. Each HTML element is rendered using ThreeJS CSS2DRenderer.
  - Default: []
- Method: htmlLat([num, str or fn])
  - Description: HTML element accessor function, attribute or a numeric constant for the latitude coordinate of the element's central position.
  - Default: lat
- Method: htmlLng([num, str or fn])
  - Description: HTML element accessor function, attribute or a numeric constant for the longitude coordinate of the element's central position.
  - Default: lng
- Method: htmlAltitude([num, str or fn])
  - Description: HTML element accessor function, attribute or a numeric constant for the altitude coordinate of the element's position, in terms of globe radius units.
  - Default: 0
- Method: htmlElement([str or fn])
  - Description: Accessor function or attribute to retrieve the DOM element to use. Should return an instance of HTMLElement.
  - Default: null
- Method: htmlElementVisibilityModifier([fn(elem, isVisible)])
  - Description: Custom function that defines how elements are shown/hidden according to whether they are in front or behind the globe. The function receives two arguments (elem, isVisible), the HTML element and a boolean indicating if the element should be visible. By default the Three object itself is automatically hidden when behind the globe.
  - Default: -
- Method: htmlTransitionDuration([num])
  - Description: Getter/setter for duration (ms) of the transition to animate HTML elements position changes. A value of 0 will move the elements immediately to their final position.
  - Default: 1000

### 3D Objects Layer

- Method: objectsData([array])
  - Description: Getter/setter for the list of custom 3D objects to represent in the objects layer. Each object is rendered according to the objectThreeObject method.
  - Default: []
- Method: objectLat([num, str or fn])
  - Description: Object accessor function, attribute or a numeric constant for the latitude coordinate of the object's position.
  - Default: lat
- Method: objectLng([num, str or fn])
  - Description: Object accessor function, attribute or a numeric constant for the longitude coordinate of the object's position.
  - Default: lng
- Method: objectAltitude([num, str or fn])
  - Description: Object accessor function, attribute or a numeric constant for the altitude coordinate of the object's position, in terms of globe radius units.
  - Default: 0.01
- Method: objectRotation([{[x], [y], [z]}, str or fn])
  - Description: Object accessor function, attribute or a {x, y, z} object for the object's rotation (in degrees). Each dimension is optional, allowing for rotation only in some axes. Rotation is applied in the order X->Y->Z.
  - Default: -
- Method: objectFacesSurface([boolean, str or fn])
  - Description: Object accessor function, attribute or a boolean constant for whether the object should be rotated to face (away from) the globe surface (true), or be left in its original universe orientation (false).
  - Default: true
- Method: objectThreeObject([Object3d, str or fn])
  - Description: Object accessor function or attribute for defining a custom 3d object to render as part of the objects map layer. Should return an instance of ThreeJS Object3d.
  - Default: A yellow sphere

### Custom Layer

- Method: customLayerData([array])
  - Description: Getter/setter for the list of items to represent in the custom map layer. Each item is rendered according to the customThreeObject method.
  - Default: []
- Method: customThreeObject([Object3d, str or fn])
  - Description: Object accessor function or attribute for generating a custom 3d object to render as part of the custom map layer. Should return an instance of ThreeJS Object3d.
  - Default: null
- Method: customThreeObjectUpdate([str or fn])
  - Description: Object accessor function or attribute for updating an existing custom 3d object with new data. This can be used for performance improvement on data updates as the objects don't need to be removed and recreated at each update. The callback method's signature includes the object to be update and its new data: customThreeObjectUpdate((obj, objData) => { ... }).
  - Default: null

### Utility

- Method: getGlobeRadius()
  - Description: Returns the cartesian distance of a globe radius in absolute spatial units.
- Method: getCoords(lat, lng [,altitude])
  - Description: Utility method to translate spherical coordinates. Given a pair of latitude/longitude coordinates and optionally altitude (in terms of globe radius units), returns the equivalent {x, y, z} cartesian spatial coordinates.
- Method: toGeoCoords({ x, y, z })
  - Description: Utility method to translate cartesian coordinates to the geographic domain. Given a set of 3D cartesian coordinates {x, y, z}, returns the equivalent {lat, lng, altitude} spherical coordinates. Altitude is defined in terms of globe radius units.

### Render Options

- Method: rendererSize(Vector2)
  - Description: It's recommended to inject the current renderer size to ensure the object proportions remain constant. This is specially necessary when using path FatLines.
  - Default: Fallback to the full browser window size (THREE.Vector2(window.innerWidth, window.innerHeight))
- Method: setPointOfView(camera)
  - Description: Some layers require knowledge about the location and view direction of the camera in order to behave correctly. Every time the camera position changes (f.e. on the controls change event) it's recommended to invoke this function, passing the current camera as sole argument, in order to keep the layers running optimally and synchronized with the view.
  - Default:
- Method: pauseAnimation()
  - Description: Pauses the animation on all globe layers.
  - Default:
- Method: resumeAnimation()
  - Description: Resumes the animation on all globe layers.
  - Default:
