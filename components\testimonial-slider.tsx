"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Quote } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import type { EmblaCarouselType as CarouselApi } from "embla-carousel";
import { Button } from "./ui/button";

interface Testimonial {
  name: string;
  program?: string;
  image?: string;
  video?: string;
  quote: string;
  googleLink?: string;
}

interface TestimonialCarouselProps {
  testimonials: Testimonial[];
  autoplaySpeed?: number;
}

export default function TestimonialCarousel({
  testimonials,
  autoplaySpeed = 5000,
}: TestimonialCarouselProps) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [autoplay, setAutoplay] = useState(false);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) return;

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  useEffect(() => {
    if (!api || !autoplay) return;

    const interval = setInterval(() => {
      api.scrollNext();
    }, autoplaySpeed);

    return () => clearInterval(interval);
  }, [api, autoplay, autoplaySpeed]);

  return (
    <div
      className="w-full max-w-5xl mx-auto px-4 py-8"
      onMouseEnter={() => setAutoplay(false)}
      onMouseLeave={() => setAutoplay(true)}
    >
      <Carousel
        setApi={setApi}
        opts={{
          align: "center",
          skipSnaps: false,
        }}
        className="w-full"
      >
        <CarouselContent>
          {testimonials.map((testimonial, index) => (
            <CarouselItem key={index} className="basis-full">
              <Card className="border shadow-md bg-background mx-auto w-full max-w-4xl">
                <CardContent className="p-6 md:p-8 flex flex-col md:flex-row gap-8 items-center">
                  <div className="w-full md:w-1/2 flex-shrink-0 flex justify-center">
                    {testimonial.video ? (
                      <div className="relative rounded-lg overflow-hidden aspect-[9/16] w-full max-w-xs shadow-lg border border-muted">
                        <video
                          src={testimonial.video}
                          className="w-full h-full object-cover"
                          controls
                          playsInline
                        />
                      </div>
                    ) : testimonial.image ? (
                      <div className="relative rounded-lg overflow-hidden aspect-square md:aspect-[9/16] w-full max-w-xs shadow-lg border border-muted">
                        <Image
                          src={testimonial.image || "/placeholder.svg"}
                          alt={testimonial.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="relative rounded-lg overflow-hidden aspect-[9/16] w-full max-w-xs bg-muted flex items-center justify-center shadow-lg">
                        <Quote className="h-12 w-12 text-muted-foreground/30" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <Quote className="h-6 w-6 text-primary/30 mb-2" />
                    <p className="italic text-base md:text-lg mb-4 max-w-prose">
                      {testimonial.quote}
                    </p>
                    <div className="mt-auto">
                      <h4 className="font-bold text-lg">{testimonial.name}</h4>
                      {testimonial.program && (
                        <p className="text-sm text-muted-foreground">
                          {testimonial.program}
                        </p>
                      )}
                    </div>
                    {testimonial.googleLink && (
                      <Button variant="outline" asChild className="mt-12">
                        <div className="flex items-center gap-1">
                          <Image
                            src="/google.png"
                            alt="Google Logo"
                            width={20}
                            height={20}
                          />
                          <a
                            href={testimonial.googleLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary"
                          >
                            View on Google Reviews
                          </a>
                        </div>
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="flex items-center justify-center mt-6 gap-2">
          <CarouselPrevious className="static transform-none mx-1" />
          <div className="flex gap-1.5">
            {Array.from({ length: count }).map((_, index) => (
              <button
                key={index}
                onClick={() => api?.scrollTo(index)}
                className={`w-2.5 h-2.5 rounded-full transition-all ${
                  current === index
                    ? "bg-primary w-5"
                    : "bg-primary/30 hover:bg-primary/50"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
          <CarouselNext className="static transform-none mx-1" />
        </div>
      </Carousel>
    </div>
  );
}
