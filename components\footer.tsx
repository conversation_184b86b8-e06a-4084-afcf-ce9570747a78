"use client";

import Link from "next/link";
import { Facebook, Instagram, Phone } from "lucide-react";
import { Logo } from "./logo";
import { MapBackground } from "./map-background";
import Image from "next/image";
import { MarkhorLogo } from "./M";
import { useTheme } from "next-themes";
import localFont from "next/font/local";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

const logoFont = localFont({ src: "../public/Expressa Bold.ttf" });

export default function Footer() {
  const { theme } = useTheme();
  const [hasMounted, setHasMounted] = useState(false);
  useEffect(() => setHasMounted(true), []);

  return (
    <footer className="bg-muted/30 text-foreground relative">
      {/* Map SVG Background */}
      <MapBackground color="currentColor" opacity={0.03} />

      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex flex-col items-center justify-center">
              {/* Logo container with proper centering */}
              <div className="flex justify-center items-center w-full">
                {hasMounted && (
                  <Image
                    src={
                      theme === "dark"
                        ? `/Markhor International - White Logo.png`
                        : `/Markhor International - Color Logo.png`
                    }
                    alt="Markhor International"
                    width={350}
                    height={350}
                    className="object-contain"
                    priority
                  />
                )}
              </div>
            </div>
            <p className="my-6 text-justifys">
              Expert guidance for international admissions and education visas
              to help you achieve your academic dreams.
            </p>
          </div>
          {/* ICEF Badge */}
          <div className="flex justify-center">
            <Image
              src="/icef.png"
              alt="ICEF Badge"
              width={200}
              height={250}
              className="object-contain max-w-[200px] h-auto crisp-image"
              quality={100}
              priority
            />
          </div>
          <div>
            <h3 className="text-lg font-bold mb-4 text-center">Quick Links</h3>
            <ul className="space-y-2 text-center">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.path}
                    className="hover:text-primary transition-colors text-center"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
            <div className="flex space-x-4 justify-center mt-4">
              <a
                href="https://www.instagram.com/markhorinternational/"
                target="_blank"
                rel="noopener noreferrer"
                className="h-12 w-12 flex items-center justify-center rounded-lg transition-colors duration-300 hover:text-primary hover:bg-background/10 border border-primary"
                aria-label="Follow us on Instagram"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href="https://www.facebook.com/markhoriinternational"
                target="_blank"
                rel="noopener noreferrer"
                className="h-12 w-12 flex items-center justify-center rounded-lg transition-colors duration-300 hover:text-primary hover:bg-background/10 border border-primary"
                aria-label="Follow us on Facebook"
              >
                <Facebook className="h-5 w-5" />
              </a>
            </div>
          </div>
          {/* WhatsApp QR Code */}
          <div className="flex flex-col items-center">
            <h3 className="text-lg font-bold mb-4">Connect on WhatsApp</h3>
            <div className="bg-white p-2 rounded-lg shadow-md mb-3">
              <Image
                src="/whatsapp_qr.jpg"
                alt="WhatsApp QR Code"
                width={150}
                height={150}
                className="rounded"
              />
            </div>
            <a
              href="https://wa.me/message/3OSI3VSB2QT4F1"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-sm hover:text-primary transition-colors"
            >
              <Phone className="h-4 w-4" />
              <span>Scan to chat with us</span>
            </a>
            <p className="mt-6 italic text-end self-end">
              A project of Markhor Group of Companies
            </p>
          </div>
          {/* Quick Links */}
        </div>

        <div className="mt-2 pt-8 border-t border-foreground/20 text-center">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="border-foreground/60 text-sm pr-1">
              © {new Date().getFullYear()} Markhor International. All rights
              reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}

const quickLinks = [
  { name: "Home", path: "/" },
  { name: "About Us", path: "/about" },
  { name: "Services", path: "/services" },
  { name: "Contact", path: "/contact" },
];

const services = [
  { name: "Visa Consultation", path: "/services#visa" },
  { name: "University Admissions", path: "/services#admissions" },
  { name: "Scholarship Guidance", path: "/services#scholarship" },
  { name: "Test Preparation", path: "/services#test-prep" },
  { name: "Pre-Departure Briefing", path: "/services#pre-departure" },
  { name: "Career Counseling", path: "/services#career" },
];
