/* eslint-disable */
// @ts-nocheck

"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import {
  ArrowLeft,
  ArrowRight,
  Calendar,
  CreditCard,
  Globe,
  CheckCircle,
  FileText,
  User,
} from "lucide-react";

// Import CarouselApi type from the carousel library
import { CarouselApi } from "@/components/ui/carousel";

// Import your geoJSON data for countries
import countriesData from "@/public/countries.json";
import { destinations } from "@/data/destinations";
import { Destination } from "@/types/destination";
import Flag from "react-flagpack";
import { Button } from "./ui/button";
import Link from "next/link";
// Dynamically import the Map component with SSR disabled
const MapWithNoSSR = dynamic(
  () => import("./mapcomponent"), // Create this component separately
  {
    ssr: false, // Disable SSR
    loading: () => (
      <div className="h-full w-full rounded-xl shadow-lg bg-muted/30 flex items-center justify-center">
        Loading Map...
      </div>
    ),
  }
);

// Feature list component
const FeatureList = ({ items, icon }) => (
  <ul className="space-y-1">
    {items.map((item, index) => (
      <li key={index} className="flex items-start text-sm">
        {icon &&
          React.cloneElement(icon, {
            className: "h-4 w-4 mr-2 mt-0.5 text-primary flex-shrink-0",
          })}
        <span className="">{item}</span>
      </li>
    ))}
  </ul>
);

// Compact visa type card
const VisaTypeCard = ({ visa }) => (
  <div className="border rounded-lg p-3 shadow-sm">
    <h4 className="font-medium text-primary">{visa.type}</h4>
    <div className="mt-2 text-sm text-white flex items-center">
      <Calendar className="h-4 w-4 mr-1" />
      <span>{visa.duration}</span>
    </div>
    <div className="mt-1 text-sm text-white flex items-center">
      <CheckCircle className="h-4 w-4 mr-1" />
      <span>{visa.purpose}</span>
    </div>
  </div>
);

// Main destination card with tabs for organization
const DestinationCard = ({ destination }) => {
  return (
    <Card className="w-full border-primary backdrop-blur-sm bg-muted/30">
      <CardHeader className="pb-8 ">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-2xl font-bold ">
              <div className="w-full flex items-center group">
                <Flag code={destination.code} size="l" />
                <span className="font-medium group-hover:text-primary transition-colors ml-1">
                  {destination.name}
                </span>
              </div>
            </CardTitle>
            <CardDescription className="mt-1 ">
              {destination.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Button>
          <Link href={`/destinations/${destination.id}`}>
            Explore Destination
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
};

// Main Component
const VisaDestinations = ({ className = "" }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [api, setApiState] = useState<CarouselApi | null>(null);

  const setApi = (api: CarouselApi) => {
    setApiState(api);
  };

  const selectedCountry = destinations[activeIndex].name;

  // Update the activeIndex when the carousel slides
  useEffect(() => {
    if (!api) return;

    api.on("select", () => {
      setActiveIndex(api.selectedScrollSnap());
    });

    // Initialize with the first slide
    api.scrollTo(0);
  }, [api]);

  return (
    <div className={`w-full py-8 bg-background/50 rounded-xl ${className}`}>
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Map Column */}
          <div className="hidden md:block lg:col-span-2 rounded-xl overflow-hidden">
            <MapWithNoSSR selectedCountry={selectedCountry} />
          </div>

          {/* Carousel Column */}
          <div className="lg:col-span-1">
            <Carousel
              setApi={setApi}
              className="w-full"
              opts={{
                align: "start",
                loop: true,
              }}
            >
              <CarouselContent>
                {destinations.map((destination, index) => (
                  <CarouselItem key={destination.id}>
                    <DestinationCard destination={destination} />
                  </CarouselItem>
                ))}
              </CarouselContent>

              <div className="hidden md:flex items-center justify-end mt-4 space-x-2">
                <CarouselPrevious className="static transform-none h-8 w-8" />
                <CarouselNext className="static transform-none h-8 w-8" />
              </div>
            </Carousel>

            {/* Destination Indicators */}
            <div className="flex flex-wrap justify-center mt-6 gap-2">
              {destinations.map((dest, index) => (
                <button
                  key={dest.id}
                  onClick={() => api?.scrollTo(index)}
                  className={`px-3 py-1.5 text-xs font-medium rounded-full transition-colors  ${
                    activeIndex === index ? "bg-primary " : "bg-muted/30 "
                  }`}
                >
                  {dest.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VisaDestinations;
