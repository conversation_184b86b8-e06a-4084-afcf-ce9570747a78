"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { ChevronDown, Menu, X } from "lucide-react";
import { useMobile } from "@/hooks/use-mobile";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { destinations } from "@/data/destinations";
import { useRouter } from "next/navigation";
import { Logo } from "@/components/logo";
import Flag from "react-flagpack";

import localFont from "next/font/local";
import { cn } from "@/lib/utils";
import { MarkhorLogo } from "./M";
import Image from "next/image";
import { useTheme } from "next-themes";

const logoFont = localFont({ src: "../public/Expressa Bold.ttf" });

export default function Navbar() {
  const pathname = usePathname();
  const { theme } = useTheme();
  const isMobile = useMobile();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const router = useRouter();
  const [hasMounted, setHasMounted] = useState(false);
  useEffect(() => setHasMounted(true), []);
  const consultation = () => {
    router.push("/contact");
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  const navLinks = [
    { name: "Home", path: "/" },
    { name: "About", path: "/about" },
    { name: "Services", path: "/services" },
    { name: "Destinations", path: "/destinations", hasDropdown: true },
    { name: "Contact", path: "/contact" },
  ];

  const dropdownVariants = {
    hidden: { opacity: 0, y: -5, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.2,
        staggerChildren: 0.05,
        delayChildren: 0.05,
      },
    },
    exit: {
      opacity: 0,
      y: -5,
      scale: 0.95,
      transition: { duration: 0.15 },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { opacity: 1, x: 0 },
    hover: { x: 5 },
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-[1000] transition-all duration-300 bg-background backdrop-blur-md ${
        isScrolled || isMenuOpen ? " backdrop-blur-md shadow-sm py-3" : " py-5"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          <Link href="/" className={cn("flex items-center ")}>
            <div className="relative mt-1">
              {hasMounted && (
                <Image
                  src={
                    theme === "dark"
                      ? "/Markhor International - Flat Logo.png"
                      : "/Markhor International - 2 Color Logo.png"
                  }
                  alt="Markhor International"
                  width={250}
                  height={250}
                  style={{ objectFit: "contain" }}
                />
              )}
            </div>
            {/* <div
              className="flex items-center tracking-widest"
              style={logoFont.style}
            >
              <MarkhorLogo
                // Remove the theme-dependent fill
                fill="currentColor"
                className="text-foreground"
              />
              <span
                className={cn(
                  "text-lg font-bold ",
                  theme === "dark" ? "#252931" : "#ffffff"
                )}
              >
                arkhor
              </span>
              <span
                className={cn(
                  "ml-2 text-lg font-medium leading-tight text-primary"
                )}
              >
                International
              </span>
            </div> */}
          </Link>

          {/* Desktop Navigation */}
          {!isMobile && (
            <nav className="hidden md:flex items-center space-x-8">
              {navLinks.map((link) =>
                link.hasDropdown ? (
                  <DropdownMenu
                    key={link.path}
                    onOpenChange={(open) => {
                      if (open) setActiveDropdown(link.path);
                      else setActiveDropdown(null);
                    }}
                  >
                    <DropdownMenuTrigger
                      className={`relative font-medium transition-colors hover:text-primary flex items-center gap-1 py-2 ${
                        pathname.startsWith(link.path) ||
                        activeDropdown === link.path
                          ? "text-primary"
                          : "text-foreground"
                      }`}
                    >
                      {link.name}
                      <motion.div
                        animate={{
                          rotate: activeDropdown === link.path ? 180 : 0,
                        }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronDown className="h-4 w-4" />
                      </motion.div>
                      {(pathname.startsWith(link.path) ||
                        activeDropdown === link.path) && (
                        <motion.div
                          className="absolute -bottom-1 left-0 right-0 h-0.5 bg-primary"
                          layoutId="navbar-indicator"
                          transition={{ type: "spring", duration: 0.5 }}
                        />
                      )}
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="start"
                      className="w-56 p-2 rounded-xl border border-border/50 bg-background/95 backdrop-blur-md"
                      forceMount
                      asChild
                    >
                      <motion.div
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        variants={dropdownVariants}
                      >
                        {destinations.map((destination) => {
                          return (
                            <DropdownMenuItem
                              key={destination.id}
                              asChild
                              className="px-1 py-2 rounded-lg"
                            >
                              <motion.div
                                variants={itemVariants}
                                whileHover="hover"
                              >
                                <Link
                                  href={`/destinations/${destination.id}`}
                                  className="w-full flex items-center group"
                                >
                                  <Flag code={destination.code} size="s" />
                                  <span className="font-medium group-hover:text-primary transition-colors ml-1">
                                    {destination.name}
                                  </span>
                                </Link>
                              </motion.div>
                            </DropdownMenuItem>
                          );
                        })}
                      </motion.div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Link
                    key={link.path}
                    href={link.path}
                    className={`relative font-medium transition-colors hover:text-primary py-2 ${
                      pathname === link.path
                        ? "text-primary"
                        : "text-foreground"
                    }`}
                  >
                    {link.name}
                    {pathname === link.path && (
                      <motion.div
                        className="absolute -bottom-1 left-0 right-0 h-0.5 bg-primary"
                        layoutId="navbar-indicator"
                        transition={{ type: "spring", duration: 0.5 }}
                      />
                    )}
                  </Link>
                )
              )}
            </nav>
          )}

          <div className="flex items-center space-x-4">
            <ThemeToggle />
            {!isMobile ? (
              <Link
                href={`https://wa.me/message/3OSI3VSB2QT4F1`}
                target="_blank"
                rel="noopener noreferrer"
                className="relative"
                data-cursor-text="Chat with us"
              >
                <Button
                  className="relative overflow-hidden group w-full"
                  size="sm"
                >
                  Book Consultation
                  <motion.div
                    className="absolute inset-0 bg-primary/20 dark:bg-primary/10"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  />
                </Button>
              </Link>
            ) : (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="flex h-10 w-10 items-center justify-center rounded-full bg-muted"
                aria-label="Toggle menu"
              >
                <AnimatePresence mode="wait" initial={false}>
                  <motion.div
                    key={isMenuOpen ? "close" : "open"}
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    {isMenuOpen ? (
                      <X className="h-5 w-5" />
                    ) : (
                      <Menu className="h-5 w-5" />
                    )}
                  </motion.div>
                </AnimatePresence>
              </motion.button>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMobile && isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden overflow-hidden"
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-4">
                {navLinks.map((link) =>
                  link.hasDropdown ? (
                    <div key={link.path} className="space-y-2">
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <button
                          onClick={() =>
                            setActiveDropdown(
                              activeDropdown === link.path ? null : link.path
                            )
                          }
                          className={`text-lg font-medium transition-colors hover:text-primary flex items-center justify-between w-full ${
                            pathname.startsWith(link.path)
                              ? "text-primary"
                              : "text-foreground"
                          }`}
                        >
                          {link.name}
                          <motion.div
                            animate={{
                              rotate: activeDropdown === link.path ? 180 : 0,
                            }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronDown className="h-4 w-4 ml-1" />
                          </motion.div>
                        </button>
                      </motion.div>

                      <AnimatePresence>
                        {activeDropdown === link.path && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="pl-4 space-y-2 border-l border-border"
                          >
                            {destinations.map((destination, index) => {
                              // Get color based on destination ID
                              const iconColor = (() => {
                                switch (destination.id) {
                                  case "usa":
                                    return "text-indigo-600 dark:text-indigo-400";
                                  case "uk":
                                    return "text-red-500 dark:text-red-400";
                                  case "canada":
                                    return "text-blue-500 dark:text-blue-400";
                                  case "australia":
                                    return "text-green-500 dark:text-green-400";
                                  case "germany":
                                    return "text-amber-500 dark:text-amber-400";
                                  default:
                                    return "text-gray-500";
                                }
                              })();

                              return (
                                <motion.div
                                  key={destination.id}
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{
                                    duration: 0.2,
                                    delay: index * 0.05,
                                  }}
                                >
                                  <Link
                                    href={`/destinations/${destination.id}`}
                                    className="text-sm font-medium transition-colors hover:text-primary flex items-center py-1"
                                  >
                                    {destination.name}
                                  </Link>
                                </motion.div>
                              );
                            })}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <motion.div
                      key={link.path}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{
                        duration: 0.3,
                        delay:
                          link.path === "/" ? 0 : 0.1 * navLinks.indexOf(link),
                      }}
                    >
                      <Link
                        href={link.path}
                        className={`text-lg font-medium transition-colors hover:text-primary block ${
                          pathname === link.path
                            ? "text-primary"
                            : "text-foreground"
                        }`}
                      >
                        {link.name}
                      </Link>
                    </motion.div>
                  )
                )}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                >
                  <Link
                    href={`https://wa.me/message/3OSI3VSB2QT4F1`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full"
                    data-cursor-text="Chat with us"
                  >
                    <Button className="mt-2 w-full">Book Consultation</Button>
                  </Link>
                </motion.div>
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
