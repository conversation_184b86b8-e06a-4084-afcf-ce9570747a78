"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { BookOpen, HelpCircle, ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function IELTSBanner() {
  const [mounted, setMounted] = useState(false);
  const [index, setIndex] = useState(0);

  const phrases = [
    "Academic Success Worldwide",
    "Career Advancement Opportunities",
    "Global Education Pathways",
    "Immigration Requirements",
    "Professional Certification",
  ];

  useEffect(() => {
    setMounted(true);
    const interval = setInterval(() => {
      setIndex((prevIndex) => (prevIndex + 1) % phrases.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  if (!mounted) return null;

  return (
    <motion.div
      className="relative w-full overflow-hidden rounded-lg bg-gradient-to-r from-muted/30 to-muted/50 text-foreground"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      {/* Abstract background patterns */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-blue-400/20 rounded-bl-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-blue-500/10 rounded-tr-full blur-3xl"></div>
      </div>

      {/* Subtle grid overlay */}
      <div className="absolute inset-0 z-1 bg-grid-pattern opacity-5"></div>

      <div className="absolute inset-0 bg-[url('/placeholder.svg?height=400&width=1200')] bg-cover bg-center opacity-10 mix-blend-overlay" />

      <div className="container relative z-10 mx-auto px-6 py-20 sm:px-8 lg:px-12">
        <div className="grid gap-16 md:grid-cols-2 md:items-center">
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h2 className="text-4xl font-bold tracking-tight sm:text-5xl">
              Prepare for Success with IELTS
            </h2>

            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 gap-6 pt-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <Link
                href="https://bxsearch.ielts.idp.com/rpt/partner/61a5c2f1-2cf8-46ff-8658-0f5298e4b155"
                target="_blank"
                rel="noopener noreferrer"
                className="w-full"
              >
                <Button size="lg" className="group w-full">
                  <BookOpen className="mr-2 h-5 w-5" />
                  Book Now
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Link
                href="https://wa.me/message/3OSI3VSB2QT4F1"
                target="_blank"
                rel="noopener noreferrer"
                className="w-full"
              >
                <Button size="lg" variant="outline" className="group w-full">
                  <HelpCircle className="mr-2 h-5 w-5" />
                  Enquire Now
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
            </motion.div>
          </motion.div>

          <motion.div
            className="hidden md:block"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <div className="relative h-[400px] w-full overflow-hidden rounded-lg shadow-xl">
              {/* Fallback image in case the actual image is missing */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-50 flex items-center justify-center">
                <div className="text-blue-500 text-lg font-medium">
                  IELTS Preparation
                </div>
              </div>

              {/* The actual image - using position relative with object-cover as a more reliable approach */}
              <Image
                src="/ielts.jpg"
                alt="IELTS Preparation"
                fill
                className="object-cover object-center"
                onError={(e) => {
                  // If image fails to load, we'll show the fallback
                  console.error("Image failed to load:", e);
                }}
              />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Add a global style for the grid pattern */}
      <style jsx global>{`
        .bg-grid-pattern {
          background-image: linear-gradient(
              to right,
              rgba(0, 0, 0, 0.05) 1px,
              transparent 1px
            ),
            linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
          background-size: 40px 40px;
        }
      `}</style>
    </motion.div>
  );
}
