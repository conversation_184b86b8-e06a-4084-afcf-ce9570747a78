"use client";

import { useRef } from "react";
import { motion, useScroll } from "framer-motion";

interface TimelineEvent {
  year: string;
  title: string;
  description: string;
}

interface TimelineProps {
  events: TimelineEvent[];
}

export default function Timeline({ events }: TimelineProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  return (
    <div ref={ref} className="relative max-w-4xl mx-auto py-10">
      {/* Timeline Line */}
      <motion.div
        className="absolute left-9 top-0 bottom-0 w-0.5 bg-primary/30"
        style={{ scaleY: scrollYProgress }}
      />

      {/* Timeline Events */}
      <div className="space-y-12">
        {events.map((event, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true, margin: "-100px" }}
            className="relative pl-20"
          >
            {/* Year Circle */}
            <div className="absolute left-0 top-0 flex items-center justify-center w-[72px] h-[72px] rounded-full bg-primary text-background font-bold text-xl z-10">
              {event.year}
            </div>

            {/* Content */}
            <div className="bg-background/10 backdrop-blur-sm p-6 pt-0 rounded-lg">
              <h3 className="text-xl font-bold mb-2">{event.title}</h3>
              <p className="">{event.description}</p>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
