"use client";

import { useEffect, useState } from "react";
import { useParams, notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  CheckCircle,
  CreditCard,
  ExternalLink,
  FileText,
  GraduationCap,
  MapPin,
  School,
  UniversityIcon,
  User,
  Book,
} from "lucide-react";

import { destinations } from "@/data/destinations";
import {
  universities,
  educationSystems,
  scholarships,
  averageCosts,
} from "@/data/universities";
import type { Destination, University } from "@/types/destination";

export default function DestinationDetail() {
  const params = useParams();
  const id = params.id as string;

  const [destination, setDestination] = useState<Destination | null>(null);
  const [universityList, setUniversityList] = useState<University[]>([]);
  const [educationSystem, setEducationSystem] = useState<string>("");
  const [scholarshipList, setScholarshipList] = useState<any[]>([]);
  const [averageCost, setAverageCost] = useState<any>(null);
  const [showBackToTop, setShowBackToTop] = useState<boolean>(false);

  useEffect(() => {
    // Find the destination by ID
    const foundDestination = destinations.find((dest) => dest.id === id);
    if (!foundDestination) {
      notFound();
      return;
    }

    setDestination(foundDestination);

    // Get universities for this destination
    if (universities[id]) {
      setUniversityList(universities[id]);
    }

    // Get education system description
    if (educationSystems[id]) {
      setEducationSystem(educationSystems[id]);
    }

    // Get scholarships
    if (scholarships[id]) {
      setScholarshipList(scholarships[id]);
    }

    // Get average costs
    if (averageCosts[id]) {
      setAverageCost(averageCosts[id]);
    }
  }, [id]);

  // Handle scroll to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 500);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  if (!destination) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <main className="pt-24 pb-16">
      {/* Hero Section with Monument Background */}
      <section className="relative h-[80vh] mb-16">
        <div className="absolute inset-0 bg-black/50 z-10"></div>
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url(${
              destination.image || `/placeholder.svg?height=800&width=1600`
            })`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            width: "100%",
            height: "100%",
          }}
        ></div>
        <div className="container mx-auto px-4 h-full flex flex-col justify-center relative z-20">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            {destination.name}
          </h1>
          <p className="text-xl text-white/90 max-w-2xl">
            {destination.description}
          </p>
          <div className="flex flex-wrap gap-2 mt-4">
            {destination.visaTypes.map((visa, index) => (
              <Badge key={index} variant="secondary" className="text-sm py-1.5">
                {visa.type}
              </Badge>
            ))}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="container mx-auto px-4">
        <div className="space-y-16">
          {/* Overview Section - Simplified */}
          <div id="overview" className="space-y-8 scroll-mt-24">
            <h2 className="text-3xl font-bold">Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="bg-muted/30">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                    Benefits
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {destination.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="mr-2 h-4 w-4 text-primary mt-1 flex-shrink-0" />
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-muted/30">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="mr-2 h-5 w-5 text-primary" />
                    Eligibility
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {destination.eligibility.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="mr-2 h-4 w-4 text-primary mt-1 flex-shrink-0" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="bg-muted/30">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <GraduationCap className="mr-2 h-5 w-5 text-primary" />
                    Education System
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p>{educationSystem}</p>
                </CardContent>
              </Card>

              <Card className="bg-muted/30">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="mr-2 h-5 w-5 text-primary" />
                    Processing Time & Fees
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Processing Time:
                      </p>
                      <p className="font-medium">
                        {destination.processingTime}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Visa Fees:
                      </p>
                      <p className="font-medium">
                        {destination.fees.amount} {destination.fees.currency}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Universities Section */}
          <div
            id="universities"
            className="space-y-8 pt-8 border-t scroll-mt-24"
          >
            <h2 className="text-3xl font-bold">Universities</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {destination.universities.map((university, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full flex flex-col hover:shadow-lg transition-shadow duration-300 bg-muted/30">
                    <div className="relative h-48 w-full overflow-hidden">
                      <Image
                        src={
                          university.image ||
                          "/placeholder.svg?height=400&width=600" ||
                          "/placeholder.svg"
                        }
                        alt={university.name}
                        fill
                        className="object-cover rounded-t-lg transition-transform duration-300 hover:scale-105"
                      />
                    </div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-xl">
                          {university.name}
                        </CardTitle>
                      </div>
                      <CardDescription className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {university.location}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-grow">
                      <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                        {university.description}
                      </p>
                      <div className="flex justify-start items-center gap-4 mt-auto">
                        {university.admissionRequirementsURL && (
                          <Link
                            href={university.admissionRequirementsURL}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="gap-1"
                            >
                              <UniversityIcon className="h-4 w-4" />
                              Admission
                            </Button>
                          </Link>
                        )}
                        {university.coursesURL && (
                          <Link
                            href={university.coursesURL}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="gap-1"
                            >
                              <Book className="h-4 w-4" />
                              Courses
                            </Button>
                          </Link>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Visa Information Section */}
          <div id="visa" className="space-y-8 pt-8 border-t scroll-mt-24">
            <h2 className="text-3xl font-bold">Visa Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="bg-muted/30">
                <CardHeader>
                  <CardTitle>Visa Types</CardTitle>
                  <CardDescription>
                    Available visa options for {destination.name}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {destination.visaTypes.map((visa, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 shadow-sm"
                    >
                      <h3 className="text-lg font-semibold text-primary mb-2">
                        {visa.type}
                      </h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground">
                            Duration
                          </h4>
                          <p className="flex items-center mt-1">
                            <Calendar className="h-4 w-4 mr-2 text-primary" />
                            {visa.duration}
                          </p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground">
                            Purpose
                          </h4>
                          <p className="flex items-center mt-1">
                            <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                            {visa.purpose}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <div className="space-y-6">
                <Card className="bg-muted/30">
                  <CardHeader>
                    <CardTitle>Requirements</CardTitle>
                    <CardDescription>
                      Documents and conditions needed for visa application
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {destination.requirements.map((req, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="mr-2 h-4 w-4 text-primary mt-1 flex-shrink-0" />
                          <span>{req}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>

                <Card className="bg-muted/30">
                  <CardHeader>
                    <CardTitle>Required Documents</CardTitle>
                    <CardDescription>
                      Documents to prepare for your application
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {destination.documents.map((doc, index) => (
                        <li key={index} className="flex items-start">
                          <FileText className="mr-2 h-4 w-4 text-primary mt-1 flex-shrink-0" />
                          <span>{doc}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>

            <Card className="bg-muted/30">
              <CardHeader>
                <CardTitle>Application Process</CardTitle>
                <CardDescription>
                  Key information about the visa application process
                </CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex flex-col items-center text-center p-4 border rounded-lg">
                  <CreditCard className="h-8 w-8 text-primary mb-2" />
                  <h3 className="font-medium">Application Fee</h3>
                  <p className="text-lg font-semibold mt-1">
                    {destination.fees.amount} {destination.fees.currency}
                  </p>
                </div>

                <div className="flex flex-col items-center text-center p-4 border rounded-lg">
                  <CheckCircle className="h-8 w-8 text-primary mb-2" />
                  <h3 className="font-medium">Success Rate</h3>
                  <p className="text-lg font-semibold mt-1">High</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Scholarships Section */}
          <div
            id="scholarships"
            className="space-y-8 pt-8 border-t scroll-mt-24"
          >
            <h2 className="text-3xl font-bold">Scholarships</h2>
            <Card className="bg-muted/30">
              <CardHeader>
                <CardTitle>Available Scholarships</CardTitle>
                <CardDescription>
                  Financial aid opportunities for international students
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {scholarshipList.map((scholarship, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-6 shadow-sm"
                    >
                      <div className="flex justify-between items-start">
                        <h3 className="text-xl font-semibold text-primary">
                          {scholarship.name}
                        </h3>
                        {scholarship.amount && (
                          <Badge variant="outline">
                            {scholarship.amount.value.toLocaleString()}{" "}
                            {scholarship.amount.currency}
                          </Badge>
                        )}
                      </div>
                      <p className="mt-2 text-muted-foreground">
                        {scholarship.description}
                      </p>

                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-1">
                          Eligibility:
                        </h4>
                        <p>{scholarship.eligibility}</p>
                      </div>

                      {scholarship.website && (
                        <div className="mt-4">
                          <Link
                            href={scholarship.website}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="gap-1"
                            >
                              <ExternalLink className="h-4 w-4" />
                              Visit Website
                            </Button>
                          </Link>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Costs Section */}
          <div id="costs" className="space-y-8 pt-8 border-t scroll-mt-24">
            <h2 className="text-3xl font-bold">Costs</h2>
            {averageCost && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <Card className="bg-muted/30">
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <School className="mr-2 h-5 w-5 text-primary" />
                        Average Tuition Fees
                      </CardTitle>
                      <CardDescription>
                        Annual cost for international students
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-baseline">
                        <span className="text-4xl font-bold">
                          {averageCost.tuition.amount.toLocaleString()}
                        </span>
                        <span className="ml-2 text-xl">
                          {averageCost.tuition.currency}/year
                        </span>
                      </div>
                      <p className="mt-4 text-muted-foreground">
                        This is an average figure. Actual tuition fees vary by
                        university, program, and level of study.
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-muted/30">
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <MapPin className="mr-2 h-5 w-5 text-primary" />
                        Average Living Expenses
                      </CardTitle>
                      <CardDescription>
                        Annual cost of living for students
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-baseline">
                        <span className="text-4xl font-bold">
                          {averageCost.living.amount.toLocaleString()}
                        </span>
                        <span className="ml-2 text-xl">
                          {averageCost.living.currency}/year
                        </span>
                      </div>
                      <p className="mt-4 text-muted-foreground">
                        Includes accommodation, food, transportation, and other
                        daily expenses. Costs may vary by location and
                        lifestyle.
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <Card className="bg-muted/30">
                  <CardHeader>
                    <CardTitle>Breakdown of Living Expenses</CardTitle>
                    <CardDescription>
                      Estimated monthly costs in {destination.name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="font-medium">Accommodation</span>
                          <span className="font-semibold">
                            {Math.round(
                              (averageCost.living.amount * 0.4) / 12
                            ).toLocaleString()}{" "}
                            {averageCost.living.currency}/month
                          </span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{ width: "40%" }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="font-medium">Food</span>
                          <span className="font-semibold">
                            {Math.round(
                              (averageCost.living.amount * 0.3) / 12
                            ).toLocaleString()}{" "}
                            {averageCost.living.currency}/month
                          </span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{ width: "30%" }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="font-medium">Transportation</span>
                          <span className="font-semibold">
                            {Math.round(
                              (averageCost.living.amount * 0.1) / 12
                            ).toLocaleString()}{" "}
                            {averageCost.living.currency}/month
                          </span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{ width: "10%" }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="font-medium">Books & Supplies</span>
                          <span className="font-semibold">
                            {Math.round(
                              (averageCost.living.amount * 0.05) / 12
                            ).toLocaleString()}{" "}
                            {averageCost.living.currency}/month
                          </span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{ width: "5%" }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="font-medium">Personal Expenses</span>
                          <span className="font-semibold">
                            {Math.round(
                              (averageCost.living.amount * 0.15) / 12
                            ).toLocaleString()}{" "}
                            {averageCost.living.currency}/month
                          </span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{ width: "15%" }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 mt-16">
        <Card className="bg-primary/10 border-none overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent"></div>
          <CardContent className="p-8 md:p-12 flex flex-col md:flex-row items-center justify-between relative z-10">
            <div className="mb-6 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">
                Ready to start your journey to {destination.name}?
              </h2>
              <p className="text-muted-foreground max-w-xl">
                Our expert consultants can guide you through the entire process,
                from university selection to visa application.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button className="shadow-lg">
                <Link
                  href={`https://wa.me/message/3OSI3VSB2QT4F1`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="relative block"
                  data-cursor-text="Chat with us"
                >
                  Book a Consultation
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Back to Top Button */}
      <motion.button
        onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
        className={`fixed bottom-8 left-8 bg-primary text-white p-3 rounded-full shadow-lg z-50 ${
          showBackToTop ? "flex" : "hidden"
        } items-center justify-center`}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{
          opacity: showBackToTop ? 1 : 0,
          scale: showBackToTop ? 1 : 0.8,
        }}
        transition={{ duration: 0.3 }}
        aria-label="Back to top"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="m18 15-6-6-6 6" />
        </svg>
      </motion.button>
    </main>
  );
}
