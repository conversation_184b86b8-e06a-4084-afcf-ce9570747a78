<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="2902.18" height="2573.28" viewBox="0 0 2902.18 2573.28">
<defs>
<clipPath id="clip-0">
<path clip-rule="nonzero" d="M 0 0 L 2902.179688 0 L 2902.179688 2573.28125 L 0 2573.28125 Z M 0 0 "/>
</clipPath>
<clipPath id="clip-1">
<path clip-rule="nonzero" d="M 1964 1523 L 1965 1523 L 1965 1617 L 1964 1617 Z M 1964 1523 "/>
</clipPath>
<clipPath id="clip-2">
<path clip-rule="nonzero" d="M 1964.339844 1523.078125 L 1964.070312 1615.289062 C 1964.179688 1628.378906 1956.128906 1641.609375 1955.699219 1642.308594 L 1955.960938 1550.101562 C 1956.390625 1549.398438 1964.441406 1536.171875 1964.339844 1523.078125 "/>
</clipPath>
<clipPath id="clip-3">
<path clip-rule="nonzero" d="M 1963 1524 L 1965 1524 L 1965 1624 L 1963 1624 Z M 1963 1524 "/>
</clipPath>
<clipPath id="clip-4">
<path clip-rule="nonzero" d="M 1964.339844 1523.078125 L 1964.070312 1615.289062 C 1964.179688 1628.378906 1956.128906 1641.609375 1955.699219 1642.308594 L 1955.960938 1550.101562 C 1956.390625 1549.398438 1964.441406 1536.171875 1964.339844 1523.078125 "/>
</clipPath>
<clipPath id="clip-5">
<path clip-rule="nonzero" d="M 1960 1530 L 1964 1530 L 1964 1632 L 1960 1632 Z M 1960 1530 "/>
</clipPath>
<clipPath id="clip-6">
<path clip-rule="nonzero" d="M 1964.339844 1523.078125 L 1964.070312 1615.289062 C 1964.179688 1628.378906 1956.128906 1641.609375 1955.699219 1642.308594 L 1955.960938 1550.101562 C 1956.390625 1549.398438 1964.441406 1536.171875 1964.339844 1523.078125 "/>
</clipPath>
<clipPath id="clip-7">
<path clip-rule="nonzero" d="M 1956 1539 L 1962 1539 L 1962 1642 L 1956 1642 Z M 1956 1539 "/>
</clipPath>
<clipPath id="clip-8">
<path clip-rule="nonzero" d="M 1964.339844 1523.078125 L 1964.070312 1615.289062 C 1964.179688 1628.378906 1956.128906 1641.609375 1955.699219 1642.308594 L 1955.960938 1550.101562 C 1956.390625 1549.398438 1964.441406 1536.171875 1964.339844 1523.078125 "/>
</clipPath>
<clipPath id="clip-9">
<path clip-rule="nonzero" d="M 1955 1548 L 1957 1548 L 1957 1643 L 1955 1643 Z M 1955 1548 "/>
</clipPath>
<clipPath id="clip-10">
<path clip-rule="nonzero" d="M 1964.339844 1523.078125 L 1964.070312 1615.289062 C 1964.179688 1628.378906 1956.128906 1641.609375 1955.699219 1642.308594 L 1955.960938 1550.101562 C 1956.390625 1549.398438 1964.441406 1536.171875 1964.339844 1523.078125 "/>
</clipPath>
<clipPath id="clip-11">
<path clip-rule="nonzero" d="M 1333 1523 L 1343 1523 L 1343 1643 L 1333 1643 Z M 1333 1523 "/>
</clipPath>
<clipPath id="clip-12">
<path clip-rule="nonzero" d="M 1342.210938 1550.101562 L 1341.949219 1642.308594 C 1341.519531 1641.621094 1333.289062 1628.371094 1333.21875 1615.289062 L 1333.480469 1523.078125 C 1333.550781 1536.160156 1341.78125 1549.398438 1342.210938 1550.101562 "/>
</clipPath>
<clipPath id="clip-13">
<path clip-rule="nonzero" d="M 1412 1561 L 1436 1561 L 1436 1686 L 1412 1686 Z M 1412 1561 "/>
</clipPath>
<clipPath id="clip-14">
<path clip-rule="nonzero" d="M 1435.410156 1593.03125 L 1435.148438 1685.25 C 1425.179688 1675.101562 1417.550781 1664.449219 1412.21875 1653.460938 L 1412.480469 1561.238281 C 1417.808594 1572.230469 1425.449219 1582.890625 1435.410156 1593.03125 "/>
</clipPath>
<clipPath id="clip-15">
<path clip-rule="nonzero" d="M 2202 1437 L 2723 1437 L 2723 1734 L 2202 1734 Z M 2202 1437 "/>
</clipPath>
<clipPath id="clip-16">
<path clip-rule="nonzero" d="M 2441.449219 1733.898438 L 2202.988281 1598.398438 L 2484.371094 1437.699219 L 2722.820312 1573.191406 Z M 2441.449219 1733.898438 "/>
</clipPath>
<clipPath id="clip-17">
<path clip-rule="nonzero" d="M 2441.449219 1733.898438 L 2202.988281 1598.398438 L 2484.371094 1437.699219 L 2722.820312 1573.191406 L 2441.449219 1733.898438 "/>
</clipPath>
<linearGradient id="linear-pattern-0" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="1" y2="0" gradientTransform="matrix(519.83, 0, 0, 519.83, 2202.99, 1585.8)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(29.901123%, 43.608093%, 74.938965%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(30.096436%, 43.766785%, 75.015259%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.293274%, 43.927002%, 75.093079%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.488586%, 44.085693%, 75.169373%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.685425%, 44.245911%, 75.245667%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.880737%, 44.404602%, 75.32196%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.077576%, 44.563293%, 75.398254%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.272888%, 44.723511%, 75.476074%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.469727%, 44.882202%, 75.552368%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.665039%, 45.042419%, 75.628662%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.861877%, 45.201111%, 75.704956%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.05719%, 45.359802%, 75.78125%)" stop-opacity="1"/>
<stop offset="0.101562" stop-color="rgb(32.254028%, 45.52002%, 75.85907%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.449341%, 45.678711%, 75.935364%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(32.646179%, 45.838928%, 76.011658%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.841492%, 45.99762%, 76.087952%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.03833%, 46.156311%, 76.164246%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.233643%, 46.316528%, 76.242065%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(33.430481%, 46.47522%, 76.318359%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.625793%, 46.635437%, 76.394653%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(33.822632%, 46.794128%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(34.017944%, 46.95282%, 76.547241%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(34.214783%, 47.113037%, 76.625061%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.410095%, 47.271729%, 76.701355%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(34.606934%, 47.431946%, 76.777649%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.802246%, 47.590637%, 76.853943%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(34.999084%, 47.750854%, 76.930237%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.194397%, 47.909546%, 77.008057%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(35.391235%, 48.068237%, 77.084351%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.586548%, 48.228455%, 77.160645%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.783386%, 48.387146%, 77.236938%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.978699%, 48.547363%, 77.313232%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(36.175537%, 48.706055%, 77.391052%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.37085%, 48.864746%, 77.467346%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(36.567688%, 49.024963%, 77.54364%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.763%, 49.183655%, 77.619934%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(36.959839%, 49.343872%, 77.696228%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.155151%, 49.502563%, 77.774048%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(37.35199%, 49.661255%, 77.850342%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.547302%, 49.821472%, 77.926636%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(37.744141%, 49.980164%, 78.00293%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.939453%, 50.140381%, 78.079224%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(38.136292%, 50.299072%, 78.157043%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.331604%, 50.457764%, 78.233337%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(38.528442%, 50.617981%, 78.309631%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.723755%, 50.776672%, 78.385925%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(38.920593%, 50.93689%, 78.462219%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.115906%, 51.095581%, 78.538513%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(39.312744%, 51.255798%, 78.616333%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.508057%, 51.41449%, 78.692627%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(39.704895%, 51.573181%, 78.768921%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.900208%, 51.733398%, 78.845215%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(40.097046%, 51.89209%, 78.921509%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.292358%, 52.052307%, 78.999329%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(40.489197%, 52.210999%, 79.075623%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.684509%, 52.36969%, 79.151917%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(40.881348%, 52.529907%, 79.22821%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.07666%, 52.688599%, 79.304504%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(41.273499%, 52.848816%, 79.382324%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.468811%, 53.007507%, 79.458618%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(41.665649%, 53.166199%, 79.534912%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.860962%, 53.326416%, 79.611206%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(42.0578%, 53.485107%, 79.6875%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.253113%, 53.645325%, 79.76532%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.449951%, 53.804016%, 79.841614%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.645264%, 53.962708%, 79.917908%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(42.842102%, 54.122925%, 79.994202%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.037415%, 54.281616%, 80.070496%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.234253%, 54.441833%, 80.148315%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.429565%, 54.600525%, 80.224609%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(43.626404%, 54.759216%, 80.300903%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.821716%, 54.919434%, 80.377197%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.018555%, 55.078125%, 80.453491%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.213867%, 55.238342%, 80.531311%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(44.410706%, 55.397034%, 80.607605%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.606018%, 55.557251%, 80.683899%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(44.802856%, 55.715942%, 80.760193%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(44.998169%, 55.874634%, 80.836487%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(45.195007%, 56.034851%, 80.914307%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.39032%, 56.193542%, 80.990601%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.587158%, 56.35376%, 81.066895%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.782471%, 56.512451%, 81.143188%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(45.979309%, 56.671143%, 81.219482%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.174622%, 56.83136%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.37146%, 56.990051%, 81.373596%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.566772%, 57.150269%, 81.44989%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(46.763611%, 57.30896%, 81.526184%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(46.958923%, 57.467651%, 81.602478%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.155762%, 57.627869%, 81.680298%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.351074%, 57.78656%, 81.756592%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(47.547913%, 57.946777%, 81.832886%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.743225%, 58.105469%, 81.90918%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(47.940063%, 58.26416%, 81.985474%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.135376%, 58.424377%, 82.061768%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(48.332214%, 58.583069%, 82.139587%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.527527%, 58.743286%, 82.215881%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(48.724365%, 58.901978%, 82.292175%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.921204%, 59.062195%, 82.368469%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(49.116516%, 59.220886%, 82.444763%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.313354%, 59.379578%, 82.522583%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(49.508667%, 59.539795%, 82.598877%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.705505%, 59.698486%, 82.675171%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(49.900818%, 59.858704%, 82.751465%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.097656%, 60.017395%, 82.827759%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(50.292969%, 60.176086%, 82.905579%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.489807%, 60.336304%, 82.981873%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(50.68512%, 60.494995%, 83.058167%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.881958%, 60.655212%, 83.13446%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(51.077271%, 60.813904%, 83.210754%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.274109%, 60.972595%, 83.288574%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(51.469421%, 61.132812%, 83.364868%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.66626%, 61.291504%, 83.441162%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(51.861572%, 61.451721%, 83.517456%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.058411%, 61.610413%, 83.59375%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(52.253723%, 61.769104%, 83.67157%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.450562%, 61.929321%, 83.747864%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(52.645874%, 62.088013%, 83.824158%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.842712%, 62.24823%, 83.900452%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(53.038025%, 62.406921%, 83.976746%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.234863%, 62.565613%, 84.054565%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(53.430176%, 62.72583%, 84.130859%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.627014%, 62.884521%, 84.207153%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(53.822327%, 63.044739%, 84.283447%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.019165%, 63.20343%, 84.359741%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(54.214478%, 63.363647%, 84.437561%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.411316%, 63.522339%, 84.513855%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(54.606628%, 63.68103%, 84.590149%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.803467%, 63.841248%, 84.666443%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-18">
<path clip-rule="nonzero" d="M 2441 1573 L 2723 1573 L 2723 1799 L 2441 1799 Z M 2441 1573 "/>
</clipPath>
<clipPath id="clip-19">
<path clip-rule="nonzero" d="M 2722.820312 1637.390625 L 2722.820312 1573.191406 L 2441.449219 1733.898438 L 2441.449219 1798.101562 Z M 2722.820312 1637.390625 "/>
</clipPath>
<clipPath id="clip-20">
<path clip-rule="nonzero" d="M 2722.820312 1637.390625 L 2722.820312 1573.191406 L 2441.449219 1733.898438 L 2441.449219 1798.101562 L 2722.820312 1637.390625 "/>
</clipPath>
<linearGradient id="linear-pattern-1" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="0.999993" y2="0" gradientTransform="matrix(281.372, 0, 0, 281.372, 2441.45, 1685.65)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(29.901123%, 43.608093%, 74.938965%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(30.096436%, 43.766785%, 75.015259%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.293274%, 43.927002%, 75.093079%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.488586%, 44.085693%, 75.169373%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.685425%, 44.245911%, 75.245667%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.880737%, 44.404602%, 75.32196%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.077576%, 44.563293%, 75.398254%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.272888%, 44.723511%, 75.476074%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.469727%, 44.882202%, 75.552368%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.665039%, 45.042419%, 75.628662%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.861877%, 45.201111%, 75.704956%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.05719%, 45.359802%, 75.78125%)" stop-opacity="1"/>
<stop offset="0.101562" stop-color="rgb(32.254028%, 45.52002%, 75.85907%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.449341%, 45.678711%, 75.935364%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(32.646179%, 45.838928%, 76.011658%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.841492%, 45.99762%, 76.087952%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.03833%, 46.156311%, 76.164246%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.233643%, 46.316528%, 76.242065%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(33.430481%, 46.47522%, 76.318359%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.625793%, 46.635437%, 76.394653%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(33.822632%, 46.794128%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(34.017944%, 46.95282%, 76.547241%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(34.214783%, 47.113037%, 76.625061%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.410095%, 47.271729%, 76.701355%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(34.606934%, 47.431946%, 76.777649%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.802246%, 47.590637%, 76.853943%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(34.999084%, 47.750854%, 76.930237%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.194397%, 47.909546%, 77.008057%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(35.391235%, 48.068237%, 77.084351%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.586548%, 48.228455%, 77.160645%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.783386%, 48.387146%, 77.236938%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.978699%, 48.547363%, 77.313232%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(36.175537%, 48.706055%, 77.391052%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.37085%, 48.864746%, 77.467346%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(36.567688%, 49.024963%, 77.54364%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.763%, 49.183655%, 77.619934%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(36.959839%, 49.343872%, 77.696228%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.155151%, 49.502563%, 77.774048%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(37.35199%, 49.661255%, 77.850342%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.547302%, 49.821472%, 77.926636%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(37.744141%, 49.980164%, 78.00293%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.939453%, 50.140381%, 78.079224%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(38.136292%, 50.299072%, 78.155518%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.331604%, 50.457764%, 78.233337%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(38.528442%, 50.617981%, 78.309631%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.723755%, 50.776672%, 78.385925%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(38.920593%, 50.93689%, 78.462219%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.115906%, 51.095581%, 78.538513%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(39.312744%, 51.254272%, 78.616333%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.508057%, 51.41449%, 78.692627%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(39.704895%, 51.573181%, 78.768921%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.900208%, 51.733398%, 78.845215%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(40.097046%, 51.89209%, 78.921509%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.292358%, 52.052307%, 78.999329%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(40.489197%, 52.210999%, 79.075623%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.684509%, 52.36969%, 79.151917%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(40.881348%, 52.529907%, 79.22821%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.07666%, 52.688599%, 79.304504%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(41.273499%, 52.848816%, 79.382324%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.468811%, 53.007507%, 79.458618%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(41.665649%, 53.166199%, 79.534912%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.860962%, 53.326416%, 79.611206%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(42.0578%, 53.485107%, 79.6875%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.253113%, 53.645325%, 79.76532%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.449951%, 53.804016%, 79.841614%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.645264%, 53.962708%, 79.917908%)" stop-opacity="1"/>
<stop offset="0.523437" stop-color="rgb(42.842102%, 54.122925%, 79.994202%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.037415%, 54.281616%, 80.070496%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.234253%, 54.441833%, 80.148315%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.429565%, 54.600525%, 80.224609%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(43.626404%, 54.759216%, 80.300903%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.821716%, 54.919434%, 80.377197%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.018555%, 55.078125%, 80.453491%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.213867%, 55.238342%, 80.531311%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(44.410706%, 55.397034%, 80.607605%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.606018%, 55.555725%, 80.683899%)" stop-opacity="1"/>
<stop offset="0.601563" stop-color="rgb(44.802856%, 55.715942%, 80.760193%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(44.998169%, 55.874634%, 80.836487%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(45.195007%, 56.034851%, 80.914307%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.39032%, 56.193542%, 80.990601%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.587158%, 56.35376%, 81.066895%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.782471%, 56.512451%, 81.143188%)" stop-opacity="1"/>
<stop offset="0.648437" stop-color="rgb(45.979309%, 56.671143%, 81.219482%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.174622%, 56.83136%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.37146%, 56.990051%, 81.373596%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.566772%, 57.150269%, 81.44989%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(46.763611%, 57.30896%, 81.526184%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(46.958923%, 57.467651%, 81.602478%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.155762%, 57.627869%, 81.678772%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.351074%, 57.78656%, 81.756592%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(47.547913%, 57.946777%, 81.832886%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.743225%, 58.105469%, 81.90918%)" stop-opacity="1"/>
<stop offset="0.726563" stop-color="rgb(47.940063%, 58.26416%, 81.985474%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.135376%, 58.424377%, 82.061768%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(48.332214%, 58.583069%, 82.139587%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.527527%, 58.743286%, 82.215881%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(48.724365%, 58.901978%, 82.292175%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.919678%, 59.060669%, 82.368469%)" stop-opacity="1"/>
<stop offset="0.773437" stop-color="rgb(49.116516%, 59.220886%, 82.444763%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.311829%, 59.379578%, 82.522583%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(49.508667%, 59.539795%, 82.598877%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.703979%, 59.698486%, 82.675171%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(49.900818%, 59.857178%, 82.751465%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.09613%, 60.017395%, 82.827759%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(50.292969%, 60.176086%, 82.905579%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.488281%, 60.336304%, 82.981873%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(50.68512%, 60.494995%, 83.058167%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.880432%, 60.655212%, 83.13446%)" stop-opacity="1"/>
<stop offset="0.851563" stop-color="rgb(51.077271%, 60.813904%, 83.210754%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.272583%, 60.972595%, 83.288574%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(51.469421%, 61.132812%, 83.364868%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.664734%, 61.291504%, 83.441162%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(51.861572%, 61.451721%, 83.517456%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.056885%, 61.610413%, 83.59375%)" stop-opacity="1"/>
<stop offset="0.898437" stop-color="rgb(52.253723%, 61.769104%, 83.67157%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.449036%, 61.929321%, 83.747864%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(52.645874%, 62.088013%, 83.824158%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.841187%, 62.24823%, 83.900452%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(53.038025%, 62.406921%, 83.976746%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.233337%, 62.565613%, 84.054565%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(53.430176%, 62.72583%, 84.130859%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.625488%, 62.884521%, 84.207153%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(53.822327%, 63.044739%, 84.283447%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.017639%, 63.20343%, 84.359741%)" stop-opacity="1"/>
<stop offset="0.976563" stop-color="rgb(54.214478%, 63.362122%, 84.437561%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.40979%, 63.522339%, 84.513855%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(54.606628%, 63.68103%, 84.590149%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.801941%, 63.841248%, 84.666443%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-21">
<path clip-rule="nonzero" d="M 2202 1598 L 2442 1598 L 2442 1799 L 2202 1799 Z M 2202 1598 "/>
</clipPath>
<clipPath id="clip-22">
<path clip-rule="nonzero" d="M 2202.988281 1598.398438 L 2441.449219 1733.898438 L 2441.449219 1798.101562 L 2202.988281 1662.601562 Z M 2202.988281 1598.398438 "/>
</clipPath>
<clipPath id="clip-23">
<path clip-rule="nonzero" d="M 2202.988281 1598.398438 L 2441.449219 1733.898438 L 2441.449219 1798.101562 L 2202.988281 1662.601562 L 2202.988281 1598.398438 "/>
</clipPath>
<linearGradient id="linear-pattern-2" gradientUnits="userSpaceOnUse" x1="-0.000000000000001776" y1="0" x2="1.000008" y2="0" gradientTransform="matrix(238.458, 0, 0, 238.458, 2202.99, 1698.25)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(29.901123%, 43.608093%, 74.938965%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(30.096436%, 43.766785%, 75.015259%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.293274%, 43.927002%, 75.093079%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.488586%, 44.085693%, 75.169373%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.685425%, 44.245911%, 75.245667%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.880737%, 44.404602%, 75.32196%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.077576%, 44.563293%, 75.398254%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.272888%, 44.723511%, 75.476074%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.469727%, 44.882202%, 75.552368%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.665039%, 45.042419%, 75.628662%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.861877%, 45.201111%, 75.704956%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.05719%, 45.359802%, 75.78125%)" stop-opacity="1"/>
<stop offset="0.101562" stop-color="rgb(32.254028%, 45.52002%, 75.85907%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.449341%, 45.678711%, 75.935364%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(32.646179%, 45.838928%, 76.011658%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.841492%, 45.99762%, 76.087952%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.03833%, 46.156311%, 76.164246%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.233643%, 46.316528%, 76.242065%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(33.430481%, 46.47522%, 76.318359%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.625793%, 46.635437%, 76.394653%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(33.822632%, 46.794128%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(34.017944%, 46.954346%, 76.547241%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(34.214783%, 47.113037%, 76.625061%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.410095%, 47.271729%, 76.701355%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(34.606934%, 47.431946%, 76.777649%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.802246%, 47.590637%, 76.853943%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(34.999084%, 47.750854%, 76.930237%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.194397%, 47.909546%, 77.008057%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(35.391235%, 48.068237%, 77.084351%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.586548%, 48.228455%, 77.160645%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.783386%, 48.387146%, 77.236938%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.978699%, 48.547363%, 77.313232%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(36.175537%, 48.706055%, 77.391052%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.37085%, 48.864746%, 77.467346%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(36.567688%, 49.024963%, 77.54364%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.763%, 49.183655%, 77.619934%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(36.959839%, 49.343872%, 77.696228%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.155151%, 49.502563%, 77.774048%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(37.35199%, 49.661255%, 77.850342%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.547302%, 49.821472%, 77.926636%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(37.744141%, 49.980164%, 78.00293%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.939453%, 50.140381%, 78.079224%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(38.136292%, 50.299072%, 78.157043%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.331604%, 50.45929%, 78.233337%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(38.528442%, 50.617981%, 78.309631%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.723755%, 50.776672%, 78.385925%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(38.920593%, 50.93689%, 78.462219%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.115906%, 51.095581%, 78.540039%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(39.312744%, 51.255798%, 78.616333%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.508057%, 51.41449%, 78.692627%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(39.704895%, 51.573181%, 78.768921%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.900208%, 51.733398%, 78.845215%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(40.097046%, 51.89209%, 78.921509%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.292358%, 52.052307%, 78.999329%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(40.489197%, 52.210999%, 79.075623%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.684509%, 52.36969%, 79.151917%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(40.881348%, 52.529907%, 79.22821%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.07666%, 52.688599%, 79.304504%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(41.273499%, 52.848816%, 79.382324%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.468811%, 53.007507%, 79.458618%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(41.665649%, 53.166199%, 79.534912%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.860962%, 53.326416%, 79.611206%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(42.0578%, 53.485107%, 79.6875%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.253113%, 53.645325%, 79.76532%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.449951%, 53.804016%, 79.841614%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.64679%, 53.964233%, 79.917908%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(42.842102%, 54.122925%, 79.994202%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.03894%, 54.281616%, 80.070496%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.234253%, 54.441833%, 80.148315%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.431091%, 54.600525%, 80.224609%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(43.626404%, 54.760742%, 80.300903%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.823242%, 54.919434%, 80.377197%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.018555%, 55.078125%, 80.453491%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.215393%, 55.238342%, 80.531311%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(44.410706%, 55.397034%, 80.607605%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.607544%, 55.557251%, 80.683899%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(44.802856%, 55.715942%, 80.760193%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(44.999695%, 55.874634%, 80.836487%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(45.195007%, 56.034851%, 80.914307%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.391846%, 56.193542%, 80.990601%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.587158%, 56.35376%, 81.066895%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.783997%, 56.512451%, 81.143188%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(45.979309%, 56.671143%, 81.219482%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.176147%, 56.83136%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.37146%, 56.990051%, 81.373596%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.568298%, 57.150269%, 81.44989%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(46.763611%, 57.30896%, 81.526184%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(46.960449%, 57.469177%, 81.602478%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.155762%, 57.627869%, 81.680298%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.3526%, 57.78656%, 81.756592%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(47.547913%, 57.946777%, 81.832886%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.744751%, 58.105469%, 81.90918%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(47.940063%, 58.265686%, 81.985474%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.136902%, 58.424377%, 82.063293%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(48.332214%, 58.583069%, 82.139587%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.529053%, 58.743286%, 82.215881%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(48.724365%, 58.901978%, 82.292175%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.921204%, 59.062195%, 82.368469%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(49.116516%, 59.220886%, 82.446289%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.313354%, 59.379578%, 82.522583%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(49.508667%, 59.539795%, 82.598877%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.705505%, 59.698486%, 82.675171%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(49.900818%, 59.858704%, 82.751465%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.097656%, 60.017395%, 82.829285%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(50.292969%, 60.176086%, 82.905579%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.489807%, 60.336304%, 82.981873%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(50.68512%, 60.494995%, 83.058167%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.881958%, 60.655212%, 83.13446%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(51.077271%, 60.813904%, 83.210754%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.274109%, 60.974121%, 83.288574%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(51.469421%, 61.132812%, 83.364868%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.66626%, 61.291504%, 83.441162%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(51.861572%, 61.451721%, 83.517456%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.058411%, 61.610413%, 83.59375%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(52.253723%, 61.77063%, 83.67157%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.450562%, 61.929321%, 83.747864%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(52.645874%, 62.088013%, 83.824158%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.842712%, 62.24823%, 83.900452%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(53.038025%, 62.406921%, 83.976746%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.234863%, 62.567139%, 84.054565%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(53.430176%, 62.72583%, 84.130859%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.627014%, 62.884521%, 84.207153%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(53.822327%, 63.044739%, 84.283447%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.019165%, 63.20343%, 84.359741%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(54.214478%, 63.363647%, 84.437561%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.411316%, 63.522339%, 84.513855%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(54.606628%, 63.68103%, 84.590149%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.803467%, 63.841248%, 84.666443%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-24">
<path clip-rule="nonzero" d="M 1593 1090 L 1876 1090 L 1876 1316 L 1593 1316 Z M 1593 1090 "/>
</clipPath>
<clipPath id="clip-25">
<path clip-rule="nonzero" d="M 1875.351562 1154.589844 L 1875.351562 1090.398438 L 1593.980469 1251.101562 L 1593.980469 1315.300781 Z M 1875.351562 1154.589844 "/>
</clipPath>
<clipPath id="clip-26">
<path clip-rule="nonzero" d="M 1875.351562 1154.589844 L 1875.351562 1090.398438 L 1593.980469 1251.101562 L 1593.980469 1315.300781 L 1875.351562 1154.589844 "/>
</clipPath>
<linearGradient id="linear-pattern-3" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="0.999993" y2="0" gradientTransform="matrix(281.372, 0, 0, 281.372, 1593.98, 1202.85)">
<stop offset="0" stop-color="rgb(100%, 67.449951%, 2.352905%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(99.983215%, 67.344666%, 2.392578%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(99.952698%, 67.13562%, 2.471924%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(99.92218%, 66.926575%, 2.55127%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(99.891663%, 66.717529%, 2.632141%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(99.861145%, 66.506958%, 2.711487%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(99.829102%, 66.297913%, 2.792358%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(99.798584%, 66.088867%, 2.871704%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(99.768066%, 65.879822%, 2.952576%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(99.737549%, 65.670776%, 3.031921%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(99.707031%, 65.460205%, 3.112793%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(99.676514%, 65.25116%, 3.192139%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(99.64447%, 65.042114%, 3.27301%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(99.613953%, 64.833069%, 3.352356%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(99.583435%, 64.624023%, 3.431702%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(99.552917%, 64.413452%, 3.512573%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(99.5224%, 64.204407%, 3.591919%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(99.491882%, 63.995361%, 3.672791%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(99.459839%, 63.786316%, 3.752136%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(99.429321%, 63.577271%, 3.833008%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(99.398804%, 63.368225%, 3.912354%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(99.368286%, 63.157654%, 3.993225%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(99.337769%, 62.948608%, 4.072571%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(99.305725%, 62.739563%, 4.153442%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(99.275208%, 62.530518%, 4.232788%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(99.24469%, 62.321472%, 4.312134%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(99.214172%, 62.110901%, 4.393005%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(99.183655%, 61.901855%, 4.472351%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(99.153137%, 61.69281%, 4.553223%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(99.121094%, 61.483765%, 4.632568%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(99.090576%, 61.274719%, 4.71344%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(99.060059%, 61.064148%, 4.792786%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(99.029541%, 60.855103%, 4.873657%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(98.999023%, 60.646057%, 4.953003%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(98.968506%, 60.437012%, 5.033875%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(98.936462%, 60.227966%, 5.11322%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(98.905945%, 60.017395%, 5.192566%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(98.875427%, 59.80835%, 5.273438%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(98.84491%, 59.599304%, 5.352783%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(98.814392%, 59.390259%, 5.433655%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(98.782349%, 59.181213%, 5.513%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(98.751831%, 58.970642%, 5.593872%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(98.721313%, 58.761597%, 5.673218%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(98.690796%, 58.552551%, 5.754089%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(98.660278%, 58.343506%, 5.833435%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(98.629761%, 58.13446%, 5.914307%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(98.597717%, 57.925415%, 5.993652%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(98.5672%, 57.714844%, 6.072998%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(98.536682%, 57.505798%, 6.15387%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(98.506165%, 57.296753%, 6.233215%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(98.475647%, 57.087708%, 6.314087%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(98.445129%, 56.878662%, 6.393433%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(98.413086%, 56.668091%, 6.474304%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(98.382568%, 56.459045%, 6.55365%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(98.352051%, 56.25%, 6.634521%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(98.321533%, 56.040955%, 6.713867%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(98.291016%, 55.831909%, 6.794739%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(98.258972%, 55.621338%, 6.874084%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(98.228455%, 55.412292%, 6.954956%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(98.197937%, 55.203247%, 7.034302%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(98.167419%, 54.994202%, 7.113647%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(98.136902%, 54.785156%, 7.194519%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(98.106384%, 54.574585%, 7.273865%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(98.06366%, 54.293823%, 7.382202%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-27">
<path clip-rule="nonzero" d="M 1368 1918 L 1889 1918 L 1889 2215 L 1368 2215 Z M 1368 1918 "/>
</clipPath>
<clipPath id="clip-28">
<path clip-rule="nonzero" d="M 1606.648438 2214.960938 L 1368.191406 2079.46875 L 1649.558594 1918.761719 L 1888.019531 2054.261719 Z M 1606.648438 2214.960938 "/>
</clipPath>
<clipPath id="clip-29">
<path clip-rule="nonzero" d="M 1606.648438 2214.960938 L 1368.191406 2079.46875 L 1649.558594 1918.761719 L 1888.019531 2054.261719 L 1606.648438 2214.960938 "/>
</clipPath>
<linearGradient id="linear-pattern-4" gradientUnits="userSpaceOnUse" x1="0.000000000000000444" y1="0" x2="1" y2="0" gradientTransform="matrix(519.83, 0, 0, 519.83, 1368.19, 2066.86)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(29.901123%, 43.608093%, 74.938965%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(30.096436%, 43.766785%, 75.015259%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.293274%, 43.927002%, 75.093079%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.488586%, 44.085693%, 75.169373%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.685425%, 44.245911%, 75.245667%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.880737%, 44.404602%, 75.32196%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.077576%, 44.563293%, 75.398254%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.272888%, 44.723511%, 75.476074%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.469727%, 44.882202%, 75.552368%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.665039%, 45.042419%, 75.628662%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.861877%, 45.201111%, 75.704956%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.05719%, 45.359802%, 75.78125%)" stop-opacity="1"/>
<stop offset="0.101563" stop-color="rgb(32.254028%, 45.52002%, 75.85907%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.449341%, 45.678711%, 75.935364%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(32.646179%, 45.838928%, 76.011658%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.841492%, 45.99762%, 76.087952%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.03833%, 46.156311%, 76.164246%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.233643%, 46.316528%, 76.242065%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(33.430481%, 46.47522%, 76.318359%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.625793%, 46.635437%, 76.394653%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(33.822632%, 46.794128%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(34.017944%, 46.95282%, 76.547241%)" stop-opacity="1"/>
<stop offset="0.179687" stop-color="rgb(34.214783%, 47.113037%, 76.625061%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.410095%, 47.271729%, 76.701355%)" stop-opacity="1"/>
<stop offset="0.195313" stop-color="rgb(34.606934%, 47.431946%, 76.777649%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.802246%, 47.590637%, 76.853943%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(34.999084%, 47.750854%, 76.930237%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.194397%, 47.909546%, 77.008057%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(35.391235%, 48.068237%, 77.084351%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.586548%, 48.228455%, 77.160645%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.783386%, 48.387146%, 77.236938%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.978699%, 48.547363%, 77.313232%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(36.175537%, 48.706055%, 77.391052%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.37085%, 48.864746%, 77.467346%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(36.567688%, 49.024963%, 77.54364%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.763%, 49.183655%, 77.619934%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(36.959839%, 49.343872%, 77.696228%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.155151%, 49.502563%, 77.774048%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(37.35199%, 49.661255%, 77.850342%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.547302%, 49.821472%, 77.926636%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(37.744141%, 49.980164%, 78.00293%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.939453%, 50.140381%, 78.079224%)" stop-opacity="1"/>
<stop offset="0.335937" stop-color="rgb(38.136292%, 50.299072%, 78.157043%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.331604%, 50.457764%, 78.233337%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(38.528442%, 50.617981%, 78.309631%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.723755%, 50.776672%, 78.385925%)" stop-opacity="1"/>
<stop offset="0.367187" stop-color="rgb(38.920593%, 50.93689%, 78.462219%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.115906%, 51.095581%, 78.538513%)" stop-opacity="1"/>
<stop offset="0.382813" stop-color="rgb(39.312744%, 51.255798%, 78.616333%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.508057%, 51.41449%, 78.692627%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(39.704895%, 51.573181%, 78.768921%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.900208%, 51.733398%, 78.845215%)" stop-opacity="1"/>
<stop offset="0.414063" stop-color="rgb(40.097046%, 51.89209%, 78.921509%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.292358%, 52.052307%, 78.999329%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(40.489197%, 52.210999%, 79.075623%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.684509%, 52.36969%, 79.151917%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(40.881348%, 52.529907%, 79.22821%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.07666%, 52.688599%, 79.304504%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(41.273499%, 52.848816%, 79.382324%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.468811%, 53.007507%, 79.458618%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(41.665649%, 53.166199%, 79.534912%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.860962%, 53.326416%, 79.611206%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(42.0578%, 53.485107%, 79.6875%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.253113%, 53.645325%, 79.76532%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.449951%, 53.804016%, 79.841614%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.645264%, 53.962708%, 79.917908%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(42.842102%, 54.122925%, 79.994202%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.037415%, 54.281616%, 80.070496%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.234253%, 54.441833%, 80.148315%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.429565%, 54.600525%, 80.224609%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(43.626404%, 54.759216%, 80.300903%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.821716%, 54.919434%, 80.377197%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.018555%, 55.078125%, 80.453491%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.213867%, 55.238342%, 80.531311%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(44.410706%, 55.397034%, 80.607605%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.606018%, 55.557251%, 80.683899%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(44.802856%, 55.715942%, 80.760193%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(44.998169%, 55.874634%, 80.836487%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(45.195007%, 56.034851%, 80.914307%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.39032%, 56.193542%, 80.990601%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.587158%, 56.35376%, 81.066895%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.782471%, 56.512451%, 81.143188%)" stop-opacity="1"/>
<stop offset="0.648437" stop-color="rgb(45.979309%, 56.671143%, 81.219482%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.174622%, 56.83136%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.37146%, 56.990051%, 81.373596%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.566772%, 57.150269%, 81.44989%)" stop-opacity="1"/>
<stop offset="0.679687" stop-color="rgb(46.763611%, 57.30896%, 81.526184%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(46.958923%, 57.467651%, 81.602478%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.155762%, 57.627869%, 81.680298%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.351074%, 57.78656%, 81.756592%)" stop-opacity="1"/>
<stop offset="0.710937" stop-color="rgb(47.547913%, 57.946777%, 81.832886%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.743225%, 58.105469%, 81.90918%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(47.940063%, 58.26416%, 81.985474%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.135376%, 58.424377%, 82.061768%)" stop-opacity="1"/>
<stop offset="0.742187" stop-color="rgb(48.332214%, 58.583069%, 82.139587%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.527527%, 58.743286%, 82.215881%)" stop-opacity="1"/>
<stop offset="0.757813" stop-color="rgb(48.724365%, 58.901978%, 82.292175%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.921204%, 59.062195%, 82.368469%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(49.116516%, 59.220886%, 82.444763%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.313354%, 59.379578%, 82.522583%)" stop-opacity="1"/>
<stop offset="0.789063" stop-color="rgb(49.508667%, 59.539795%, 82.598877%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.705505%, 59.698486%, 82.675171%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(49.900818%, 59.858704%, 82.751465%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.097656%, 60.017395%, 82.827759%)" stop-opacity="1"/>
<stop offset="0.820313" stop-color="rgb(50.292969%, 60.176086%, 82.905579%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.489807%, 60.336304%, 82.981873%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(50.68512%, 60.494995%, 83.058167%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.881958%, 60.655212%, 83.13446%)" stop-opacity="1"/>
<stop offset="0.851563" stop-color="rgb(51.077271%, 60.813904%, 83.210754%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.274109%, 60.972595%, 83.288574%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(51.469421%, 61.132812%, 83.364868%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.66626%, 61.291504%, 83.441162%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(51.861572%, 61.451721%, 83.517456%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.058411%, 61.610413%, 83.59375%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(52.253723%, 61.769104%, 83.67157%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.450562%, 61.929321%, 83.747864%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(52.645874%, 62.088013%, 83.824158%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.842712%, 62.24823%, 83.900452%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(53.038025%, 62.406921%, 83.976746%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.234863%, 62.565613%, 84.054565%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(53.430176%, 62.72583%, 84.130859%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.627014%, 62.884521%, 84.207153%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(53.822327%, 63.044739%, 84.283447%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.019165%, 63.20343%, 84.359741%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(54.214478%, 63.363647%, 84.437561%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.411316%, 63.522339%, 84.513855%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(54.606628%, 63.68103%, 84.590149%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.803467%, 63.841248%, 84.666443%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-30">
<path clip-rule="nonzero" d="M 1606 2054 L 1889 2054 L 1889 2280 L 1606 2280 Z M 1606 2054 "/>
</clipPath>
<clipPath id="clip-31">
<path clip-rule="nonzero" d="M 1888.019531 2118.449219 L 1888.019531 2054.261719 L 1606.648438 2214.960938 L 1606.648438 2279.160156 Z M 1888.019531 2118.449219 "/>
</clipPath>
<clipPath id="clip-32">
<path clip-rule="nonzero" d="M 1888.019531 2118.449219 L 1888.019531 2054.261719 L 1606.648438 2214.960938 L 1606.648438 2279.160156 L 1888.019531 2118.449219 "/>
</clipPath>
<linearGradient id="linear-pattern-5" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="0.999993" y2="0" gradientTransform="matrix(281.372, 0, 0, 281.372, 1606.65, 2166.71)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(29.901123%, 43.608093%, 74.938965%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(30.096436%, 43.766785%, 75.015259%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.293274%, 43.927002%, 75.093079%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.488586%, 44.085693%, 75.169373%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.685425%, 44.245911%, 75.245667%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.880737%, 44.404602%, 75.32196%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.077576%, 44.563293%, 75.398254%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.272888%, 44.723511%, 75.476074%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.469727%, 44.882202%, 75.552368%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.665039%, 45.042419%, 75.628662%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.861877%, 45.201111%, 75.704956%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.05719%, 45.359802%, 75.78125%)" stop-opacity="1"/>
<stop offset="0.101562" stop-color="rgb(32.254028%, 45.52002%, 75.85907%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.449341%, 45.678711%, 75.935364%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(32.646179%, 45.838928%, 76.011658%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.841492%, 45.99762%, 76.087952%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.03833%, 46.156311%, 76.164246%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.233643%, 46.316528%, 76.242065%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(33.430481%, 46.47522%, 76.318359%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.625793%, 46.635437%, 76.394653%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(33.822632%, 46.794128%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(34.017944%, 46.95282%, 76.547241%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(34.214783%, 47.113037%, 76.625061%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.410095%, 47.271729%, 76.701355%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(34.606934%, 47.431946%, 76.777649%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.802246%, 47.590637%, 76.853943%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(34.999084%, 47.750854%, 76.930237%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.194397%, 47.909546%, 77.008057%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(35.391235%, 48.068237%, 77.084351%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.586548%, 48.228455%, 77.160645%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.783386%, 48.387146%, 77.236938%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.978699%, 48.547363%, 77.313232%)" stop-opacity="1"/>
<stop offset="0.257813" stop-color="rgb(36.175537%, 48.706055%, 77.391052%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.37085%, 48.864746%, 77.467346%)" stop-opacity="1"/>
<stop offset="0.273437" stop-color="rgb(36.567688%, 49.024963%, 77.54364%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.763%, 49.183655%, 77.619934%)" stop-opacity="1"/>
<stop offset="0.289063" stop-color="rgb(36.959839%, 49.343872%, 77.696228%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.155151%, 49.502563%, 77.774048%)" stop-opacity="1"/>
<stop offset="0.304687" stop-color="rgb(37.35199%, 49.661255%, 77.850342%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.547302%, 49.821472%, 77.926636%)" stop-opacity="1"/>
<stop offset="0.320313" stop-color="rgb(37.744141%, 49.980164%, 78.00293%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.939453%, 50.140381%, 78.079224%)" stop-opacity="1"/>
<stop offset="0.335937" stop-color="rgb(38.136292%, 50.299072%, 78.155518%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.331604%, 50.457764%, 78.233337%)" stop-opacity="1"/>
<stop offset="0.351563" stop-color="rgb(38.528442%, 50.617981%, 78.309631%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.723755%, 50.776672%, 78.385925%)" stop-opacity="1"/>
<stop offset="0.367187" stop-color="rgb(38.920593%, 50.93689%, 78.462219%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.115906%, 51.095581%, 78.538513%)" stop-opacity="1"/>
<stop offset="0.382813" stop-color="rgb(39.312744%, 51.254272%, 78.616333%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.508057%, 51.41449%, 78.692627%)" stop-opacity="1"/>
<stop offset="0.398437" stop-color="rgb(39.704895%, 51.573181%, 78.768921%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.900208%, 51.733398%, 78.845215%)" stop-opacity="1"/>
<stop offset="0.414063" stop-color="rgb(40.097046%, 51.89209%, 78.921509%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.292358%, 52.052307%, 78.999329%)" stop-opacity="1"/>
<stop offset="0.429687" stop-color="rgb(40.489197%, 52.210999%, 79.075623%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.684509%, 52.36969%, 79.151917%)" stop-opacity="1"/>
<stop offset="0.445313" stop-color="rgb(40.881348%, 52.529907%, 79.22821%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.07666%, 52.688599%, 79.304504%)" stop-opacity="1"/>
<stop offset="0.460937" stop-color="rgb(41.273499%, 52.848816%, 79.382324%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.468811%, 53.007507%, 79.458618%)" stop-opacity="1"/>
<stop offset="0.476563" stop-color="rgb(41.665649%, 53.166199%, 79.534912%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.860962%, 53.326416%, 79.611206%)" stop-opacity="1"/>
<stop offset="0.492187" stop-color="rgb(42.0578%, 53.485107%, 79.6875%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.253113%, 53.645325%, 79.76532%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.449951%, 53.804016%, 79.841614%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.645264%, 53.962708%, 79.917908%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(42.842102%, 54.122925%, 79.994202%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.037415%, 54.281616%, 80.070496%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.234253%, 54.441833%, 80.148315%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.429565%, 54.600525%, 80.224609%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(43.626404%, 54.759216%, 80.300903%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.821716%, 54.919434%, 80.377197%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.018555%, 55.078125%, 80.453491%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.213867%, 55.238342%, 80.531311%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(44.410706%, 55.397034%, 80.607605%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.606018%, 55.555725%, 80.683899%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(44.802856%, 55.715942%, 80.760193%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(44.998169%, 55.874634%, 80.836487%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(45.195007%, 56.034851%, 80.914307%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.39032%, 56.193542%, 80.990601%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.587158%, 56.35376%, 81.066895%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.782471%, 56.512451%, 81.143188%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(45.979309%, 56.671143%, 81.219482%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.174622%, 56.83136%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.37146%, 56.990051%, 81.373596%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.566772%, 57.150269%, 81.44989%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(46.763611%, 57.30896%, 81.526184%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(46.958923%, 57.467651%, 81.602478%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.155762%, 57.627869%, 81.678772%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.351074%, 57.78656%, 81.756592%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(47.547913%, 57.946777%, 81.832886%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.743225%, 58.105469%, 81.90918%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(47.940063%, 58.26416%, 81.985474%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.135376%, 58.424377%, 82.061768%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(48.332214%, 58.583069%, 82.139587%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.527527%, 58.743286%, 82.215881%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(48.724365%, 58.901978%, 82.292175%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.919678%, 59.060669%, 82.368469%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(49.116516%, 59.220886%, 82.444763%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.311829%, 59.379578%, 82.522583%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(49.508667%, 59.539795%, 82.598877%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.703979%, 59.698486%, 82.675171%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(49.900818%, 59.857178%, 82.751465%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.09613%, 60.017395%, 82.827759%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(50.292969%, 60.176086%, 82.905579%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.488281%, 60.336304%, 82.981873%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(50.68512%, 60.494995%, 83.058167%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.880432%, 60.655212%, 83.13446%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(51.077271%, 60.813904%, 83.210754%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.272583%, 60.972595%, 83.288574%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(51.469421%, 61.132812%, 83.364868%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.664734%, 61.291504%, 83.441162%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(51.861572%, 61.451721%, 83.517456%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.056885%, 61.610413%, 83.59375%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(52.253723%, 61.769104%, 83.67157%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.449036%, 61.929321%, 83.747864%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(52.645874%, 62.088013%, 83.824158%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.841187%, 62.24823%, 83.900452%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(53.038025%, 62.406921%, 83.976746%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.233337%, 62.565613%, 84.054565%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(53.430176%, 62.72583%, 84.130859%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.625488%, 62.884521%, 84.207153%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(53.822327%, 63.044739%, 84.283447%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.017639%, 63.20343%, 84.359741%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(54.214478%, 63.362122%, 84.437561%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.40979%, 63.522339%, 84.513855%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(54.606628%, 63.68103%, 84.590149%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.801941%, 63.841248%, 84.666443%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-33">
<path clip-rule="nonzero" d="M 1368 2079 L 1607 2079 L 1607 2280 L 1368 2280 Z M 1368 2079 "/>
</clipPath>
<clipPath id="clip-34">
<path clip-rule="nonzero" d="M 1368.191406 2079.46875 L 1606.648438 2214.960938 L 1606.648438 2279.160156 L 1368.191406 2143.660156 Z M 1368.191406 2079.46875 "/>
</clipPath>
<clipPath id="clip-35">
<path clip-rule="nonzero" d="M 1368.191406 2079.46875 L 1606.648438 2214.960938 L 1606.648438 2279.160156 L 1368.191406 2143.660156 L 1368.191406 2079.46875 "/>
</clipPath>
<linearGradient id="linear-pattern-6" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="1.000008" y2="0" gradientTransform="matrix(238.458, 0, 0, 238.458, 1368.19, 2179.31)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(29.901123%, 43.608093%, 74.938965%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(30.096436%, 43.766785%, 75.015259%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.293274%, 43.927002%, 75.093079%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.488586%, 44.085693%, 75.169373%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.685425%, 44.245911%, 75.245667%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.880737%, 44.404602%, 75.32196%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.077576%, 44.563293%, 75.398254%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.272888%, 44.723511%, 75.476074%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.469727%, 44.882202%, 75.552368%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.665039%, 45.042419%, 75.628662%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.861877%, 45.201111%, 75.704956%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.05719%, 45.359802%, 75.78125%)" stop-opacity="1"/>
<stop offset="0.101562" stop-color="rgb(32.254028%, 45.52002%, 75.85907%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.449341%, 45.678711%, 75.935364%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(32.646179%, 45.838928%, 76.011658%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.841492%, 45.99762%, 76.087952%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.03833%, 46.156311%, 76.164246%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.233643%, 46.316528%, 76.242065%)" stop-opacity="1"/>
<stop offset="0.148437" stop-color="rgb(33.430481%, 46.47522%, 76.318359%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.625793%, 46.635437%, 76.394653%)" stop-opacity="1"/>
<stop offset="0.164063" stop-color="rgb(33.822632%, 46.794128%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(34.017944%, 46.954346%, 76.547241%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(34.214783%, 47.113037%, 76.625061%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.410095%, 47.271729%, 76.701355%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(34.606934%, 47.431946%, 76.777649%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.802246%, 47.590637%, 76.853943%)" stop-opacity="1"/>
<stop offset="0.210937" stop-color="rgb(34.999084%, 47.750854%, 76.930237%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.194397%, 47.909546%, 77.008057%)" stop-opacity="1"/>
<stop offset="0.226563" stop-color="rgb(35.391235%, 48.068237%, 77.084351%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.586548%, 48.228455%, 77.160645%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.783386%, 48.387146%, 77.236938%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.978699%, 48.547363%, 77.313232%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(36.175537%, 48.706055%, 77.391052%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.37085%, 48.864746%, 77.467346%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(36.567688%, 49.024963%, 77.54364%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.763%, 49.183655%, 77.619934%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(36.959839%, 49.343872%, 77.696228%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.155151%, 49.502563%, 77.774048%)" stop-opacity="1"/>
<stop offset="0.304687" stop-color="rgb(37.35199%, 49.661255%, 77.850342%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.547302%, 49.821472%, 77.926636%)" stop-opacity="1"/>
<stop offset="0.320313" stop-color="rgb(37.744141%, 49.980164%, 78.00293%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.939453%, 50.140381%, 78.079224%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(38.136292%, 50.299072%, 78.157043%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.331604%, 50.45929%, 78.233337%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(38.528442%, 50.617981%, 78.309631%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.723755%, 50.776672%, 78.385925%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(38.920593%, 50.93689%, 78.462219%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.115906%, 51.095581%, 78.540039%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(39.312744%, 51.255798%, 78.616333%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.508057%, 51.41449%, 78.692627%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(39.704895%, 51.573181%, 78.768921%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.900208%, 51.733398%, 78.845215%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(40.097046%, 51.89209%, 78.921509%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.292358%, 52.052307%, 78.999329%)" stop-opacity="1"/>
<stop offset="0.429687" stop-color="rgb(40.489197%, 52.210999%, 79.075623%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.684509%, 52.36969%, 79.151917%)" stop-opacity="1"/>
<stop offset="0.445313" stop-color="rgb(40.881348%, 52.529907%, 79.22821%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.07666%, 52.688599%, 79.304504%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(41.273499%, 52.848816%, 79.382324%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.468811%, 53.007507%, 79.458618%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(41.665649%, 53.166199%, 79.534912%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.860962%, 53.326416%, 79.611206%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(42.0578%, 53.485107%, 79.6875%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.253113%, 53.645325%, 79.76532%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.449951%, 53.804016%, 79.841614%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.64679%, 53.964233%, 79.917908%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(42.842102%, 54.122925%, 79.994202%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.03894%, 54.281616%, 80.070496%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.234253%, 54.441833%, 80.148315%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.431091%, 54.600525%, 80.224609%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(43.626404%, 54.760742%, 80.300903%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.823242%, 54.919434%, 80.377197%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.018555%, 55.078125%, 80.453491%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.215393%, 55.238342%, 80.531311%)" stop-opacity="1"/>
<stop offset="0.585937" stop-color="rgb(44.410706%, 55.397034%, 80.607605%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.607544%, 55.557251%, 80.683899%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(44.802856%, 55.715942%, 80.760193%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(44.999695%, 55.874634%, 80.836487%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(45.195007%, 56.034851%, 80.914307%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.391846%, 56.193542%, 80.990601%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.587158%, 56.35376%, 81.066895%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.783997%, 56.512451%, 81.143188%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(45.979309%, 56.671143%, 81.219482%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.176147%, 56.83136%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.664063" stop-color="rgb(46.37146%, 56.990051%, 81.373596%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.568298%, 57.150269%, 81.44989%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(46.763611%, 57.30896%, 81.526184%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(46.960449%, 57.469177%, 81.602478%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.155762%, 57.627869%, 81.680298%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.3526%, 57.78656%, 81.756592%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(47.547913%, 57.946777%, 81.832886%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.744751%, 58.105469%, 81.90918%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(47.940063%, 58.265686%, 81.985474%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.136902%, 58.424377%, 82.063293%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(48.332214%, 58.583069%, 82.139587%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.529053%, 58.743286%, 82.215881%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(48.724365%, 58.901978%, 82.292175%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.921204%, 59.062195%, 82.368469%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(49.116516%, 59.220886%, 82.446289%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.313354%, 59.379578%, 82.522583%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(49.508667%, 59.539795%, 82.598877%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.705505%, 59.698486%, 82.675171%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(49.900818%, 59.858704%, 82.751465%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.097656%, 60.017395%, 82.829285%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(50.292969%, 60.176086%, 82.905579%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.489807%, 60.336304%, 82.981873%)" stop-opacity="1"/>
<stop offset="0.835937" stop-color="rgb(50.68512%, 60.494995%, 83.058167%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.881958%, 60.655212%, 83.13446%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(51.077271%, 60.813904%, 83.210754%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.274109%, 60.974121%, 83.288574%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(51.469421%, 61.132812%, 83.364868%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.66626%, 61.291504%, 83.441162%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(51.861572%, 61.451721%, 83.517456%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.058411%, 61.610413%, 83.59375%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(52.253723%, 61.77063%, 83.67157%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.450562%, 61.929321%, 83.747864%)" stop-opacity="1"/>
<stop offset="0.914063" stop-color="rgb(52.645874%, 62.088013%, 83.824158%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.842712%, 62.24823%, 83.900452%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(53.038025%, 62.406921%, 83.976746%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.234863%, 62.567139%, 84.054565%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(53.430176%, 62.72583%, 84.130859%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.627014%, 62.884521%, 84.207153%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(53.822327%, 63.044739%, 84.283447%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.019165%, 63.20343%, 84.359741%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(54.214478%, 63.363647%, 84.437561%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.411316%, 63.522339%, 84.513855%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(54.606628%, 63.68103%, 84.590149%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.803467%, 63.841248%, 84.666443%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-36">
<path clip-rule="nonzero" d="M 350 1405 L 1005 1405 L 1005 1894 L 350 1894 Z M 350 1405 "/>
</clipPath>
<clipPath id="clip-37">
<path clip-rule="nonzero" d="M 1004.910156 1889 L 1004.429688 1893.511719 L 429.46875 1557.980469 C 385.078125 1544.121094 350.382812 1503.140625 350.382812 1450.898438 L 350.382812 1405.210938 L 549.460938 1519.871094 L 552.535156 1521.648438 L 924.253906 1735.75 L 924.253906 1781.441406 C 924.253906 1834.300781 959.769531 1875.679688 1004.910156 1889 Z M 1004.910156 1889 "/>
</clipPath>
<clipPath id="clip-38">
<path clip-rule="nonzero" d="M 1004.910156 1889 L 1004.429688 1893.511719 L 429.46875 1557.980469 C 385.078125 1544.121094 350.382812 1503.140625 350.382812 1450.898438 L 350.382812 1405.210938 L 549.460938 1519.871094 L 552.535156 1521.648438 L 924.253906 1735.75 L 924.253906 1781.441406 C 924.253906 1834.300781 959.769531 1875.679688 1004.910156 1889 "/>
</clipPath>
<linearGradient id="linear-pattern-7" gradientUnits="userSpaceOnUse" x1="0.00000152782" y1="0" x2="1" y2="0" gradientTransform="matrix(654.527, 0, 0, 654.527, 350.383, 1649.36)">
<stop offset="0" stop-color="rgb(61.567688%, 65.097046%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(61.689758%, 65.203857%, 74.97406%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(61.935425%, 65.419006%, 75.12207%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(62.179565%, 65.632629%, 75.268555%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(62.425232%, 65.847778%, 75.415039%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(62.670898%, 66.061401%, 75.563049%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(62.915039%, 66.27655%, 75.709534%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(63.160706%, 66.490173%, 75.857544%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(63.406372%, 66.705322%, 76.004028%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(63.650513%, 66.920471%, 76.150513%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(63.896179%, 67.134094%, 76.298523%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(64.14032%, 67.349243%, 76.445007%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(64.385986%, 67.562866%, 76.591492%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(64.631653%, 67.778015%, 76.739502%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(64.875793%, 67.991638%, 76.885986%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(65.12146%, 68.206787%, 77.033997%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(65.367126%, 68.42041%, 77.180481%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(65.611267%, 68.635559%, 77.326965%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(65.856934%, 68.849182%, 77.474976%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(66.101074%, 69.064331%, 77.62146%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(66.346741%, 69.27948%, 77.767944%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(66.592407%, 69.493103%, 77.915955%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(66.836548%, 69.708252%, 78.062439%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(67.082214%, 69.921875%, 78.210449%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(67.327881%, 70.137024%, 78.356934%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(67.572021%, 70.350647%, 78.503418%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(67.817688%, 70.565796%, 78.651428%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(68.061829%, 70.779419%, 78.797913%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(68.307495%, 70.994568%, 78.944397%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(68.553162%, 71.209717%, 79.092407%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(68.797302%, 71.42334%, 79.238892%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(69.042969%, 71.638489%, 79.386902%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(69.288635%, 71.852112%, 79.533386%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(69.532776%, 72.067261%, 79.679871%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(69.778442%, 72.280884%, 79.827881%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(70.022583%, 72.496033%, 79.974365%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(70.26825%, 72.709656%, 80.12085%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(70.513916%, 72.924805%, 80.26886%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(70.758057%, 73.138428%, 80.415344%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(71.003723%, 73.353577%, 80.563354%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(71.24939%, 73.568726%, 80.709839%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(71.49353%, 73.782349%, 80.856323%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(71.739197%, 73.997498%, 81.004333%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(71.983337%, 74.211121%, 81.150818%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(72.229004%, 74.42627%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(72.47467%, 74.639893%, 81.445312%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(72.718811%, 74.855042%, 81.591797%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(72.964478%, 75.068665%, 81.739807%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(73.210144%, 75.283813%, 81.886292%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(73.454285%, 75.497437%, 82.032776%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(73.699951%, 75.712585%, 82.180786%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(73.944092%, 75.927734%, 82.327271%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(74.189758%, 76.141357%, 82.473755%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(74.435425%, 76.356506%, 82.621765%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(74.679565%, 76.570129%, 82.76825%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(74.925232%, 76.785278%, 82.91626%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(75.170898%, 76.998901%, 83.062744%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(75.415039%, 77.21405%, 83.209229%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(75.660706%, 77.427673%, 83.357239%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(75.906372%, 77.642822%, 83.503723%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(76.150513%, 77.857971%, 83.650208%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(76.396179%, 78.071594%, 83.798218%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(76.64032%, 78.286743%, 83.944702%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(76.885986%, 78.500366%, 84.092712%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(77.131653%, 78.715515%, 84.239197%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-39">
<path clip-rule="nonzero" d="M 552 467 L 1298 467 L 1298 1894 L 552 1894 Z M 552 467 "/>
</clipPath>
<clipPath id="clip-40">
<path clip-rule="nonzero" d="M 1297.691406 801.898438 C 1284.921875 796.230469 1269.621094 795.820312 1255.480469 802.988281 C 1231.171875 815.28125 1210.820312 833.179688 1195.71875 854.550781 L 1195.660156 854.550781 L 1195.25 854.621094 L 1195.519531 854.828125 C 1193.46875 857.691406 1191.558594 860.628906 1189.710938 863.570312 C 1174.140625 888.96875 1165.398438 918.609375 1165.398438 949.621094 L 1165.398438 1754.601562 C 1165.398438 1809.988281 1133.03125 1860.320312 1082.558594 1883.261719 C 1067.261719 1890.230469 1051.488281 1893.511719 1036.121094 1893.511719 C 1025.398438 1893.511719 1014.949219 1891.941406 1004.910156 1889 C 959.769531 1875.679688 924.253906 1834.300781 924.253906 1781.441406 L 924.253906 1735.75 L 552.535156 1521.648438 C 553.625 1520.488281 554.71875 1519.320312 555.742188 1518.101562 C 560.660156 1512.640625 565.164062 1506.761719 569.128906 1500.539062 C 583.539062 1478.21875 591.597656 1451.710938 591.597656 1424.058594 L 591.597656 619.078125 C 591.597656 557.128906 626.425781 500.449219 681.675781 472.449219 C 688.914062 468.761719 696.492188 467.121094 703.804688 467.121094 C 706.738281 467.121094 709.609375 467.398438 712.40625 467.941406 L 712.542969 467.941406 L 741.230469 484.328125 Z M 1297.691406 801.898438 "/>
</clipPath>
<clipPath id="clip-41">
<path clip-rule="nonzero" d="M 1297.691406 801.898438 C 1284.921875 796.230469 1269.621094 795.820312 1255.480469 802.988281 C 1231.171875 815.28125 1210.820312 833.179688 1195.71875 854.550781 L 1195.660156 854.550781 L 1195.25 854.621094 L 1195.519531 854.828125 C 1193.46875 857.691406 1191.558594 860.628906 1189.710938 863.570312 C 1174.140625 888.96875 1165.398438 918.609375 1165.398438 949.621094 L 1165.398438 1754.601562 C 1165.398438 1809.988281 1133.03125 1860.320312 1082.558594 1883.261719 C 1067.261719 1890.230469 1051.488281 1893.511719 1036.121094 1893.511719 C 1025.398438 1893.511719 1014.949219 1891.941406 1004.910156 1889 C 959.769531 1875.679688 924.253906 1834.300781 924.253906 1781.441406 L 924.253906 1735.75 L 552.535156 1521.648438 C 553.625 1520.488281 554.71875 1519.320312 555.742188 1518.101562 C 560.660156 1512.640625 565.164062 1506.761719 569.128906 1500.539062 C 583.539062 1478.21875 591.597656 1451.710938 591.597656 1424.058594 L 591.597656 619.078125 C 591.597656 557.128906 626.425781 500.449219 681.675781 472.449219 C 688.914062 468.761719 696.492188 467.121094 703.804688 467.121094 C 706.738281 467.121094 709.609375 467.398438 712.40625 467.941406 L 712.542969 467.941406 L 741.230469 484.328125 L 1297.691406 801.898438 "/>
</clipPath>
<linearGradient id="linear-pattern-8" gradientUnits="userSpaceOnUse" x1="-0.12895" y1="0" x2="1.003271" y2="0" gradientTransform="matrix(288.992, -1339.46, 1339.46, 288.992, 755.328, 1756.5)">
<stop offset="0" stop-color="rgb(81.959534%, 82.351685%, 84.312439%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(82.072449%, 82.463074%, 84.411621%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(82.345581%, 82.730103%, 84.648132%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(82.66449%, 83.041382%, 84.925842%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(82.983398%, 83.354187%, 85.203552%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(83.303833%, 83.666992%, 85.481262%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(83.622742%, 83.978271%, 85.758972%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(83.94165%, 84.291077%, 86.036682%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(84.260559%, 84.602356%, 86.314392%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(84.579468%, 84.915161%, 86.592102%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(84.898376%, 85.227966%, 86.868286%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(85.217285%, 85.539246%, 87.145996%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(85.53772%, 85.852051%, 87.423706%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(85.856628%, 86.16333%, 87.701416%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(86.175537%, 86.476135%, 87.979126%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(86.494446%, 86.78894%, 88.256836%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(86.813354%, 87.10022%, 88.534546%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(87.132263%, 87.413025%, 88.81073%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(87.451172%, 87.724304%, 89.08844%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(87.771606%, 88.037109%, 89.36615%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(88.090515%, 88.349915%, 89.64386%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(88.409424%, 88.661194%, 89.92157%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(88.728333%, 88.973999%, 90.19928%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(89.047241%, 89.285278%, 90.47699%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(89.36615%, 89.598083%, 90.7547%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(89.686584%, 89.910889%, 91.030884%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(90.005493%, 90.222168%, 91.308594%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(90.324402%, 90.534973%, 91.586304%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(90.643311%, 90.846252%, 91.864014%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(90.962219%, 91.159058%, 92.141724%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(91.281128%, 91.471863%, 92.419434%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(91.600037%, 91.783142%, 92.697144%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(91.920471%, 92.095947%, 92.973328%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(92.23938%, 92.407227%, 93.251038%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(92.558289%, 92.720032%, 93.528748%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(92.877197%, 93.032837%, 93.806458%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(93.196106%, 93.344116%, 94.084167%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(93.515015%, 93.656921%, 94.361877%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(93.833923%, 93.968201%, 94.639587%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(94.154358%, 94.281006%, 94.917297%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(94.473267%, 94.593811%, 95.193481%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(94.792175%, 94.90509%, 95.471191%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(95.111084%, 95.217896%, 95.748901%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(95.429993%, 95.529175%, 96.026611%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(95.748901%, 95.84198%, 96.304321%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(96.06781%, 96.154785%, 96.582031%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(96.388245%, 96.466064%, 96.859741%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(96.707153%, 96.77887%, 97.135925%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(97.026062%, 97.090149%, 97.413635%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(97.344971%, 97.402954%, 97.691345%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(97.663879%, 97.715759%, 97.969055%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(97.982788%, 98.027039%, 98.246765%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(98.301697%, 98.339844%, 98.524475%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(98.622131%, 98.651123%, 98.802185%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(98.94104%, 98.963928%, 99.079895%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(99.259949%, 99.276733%, 99.356079%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(99.578857%, 99.588013%, 99.633789%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(99.868774%, 99.871826%, 99.885559%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-42">
<path clip-rule="nonzero" d="M 1195 797 L 1327 797 L 1327 931 L 1195 931 Z M 1195 797 "/>
</clipPath>
<clipPath id="clip-43">
<path clip-rule="nonzero" d="M 1326.851562 846.898438 L 1326.851562 930.429688 L 1195.519531 854.828125 C 1195.519531 854.761719 1195.589844 854.691406 1195.660156 854.550781 L 1195.71875 854.550781 C 1210.820312 833.179688 1231.171875 815.28125 1255.480469 802.988281 C 1269.621094 795.820312 1284.921875 796.230469 1297.691406 801.898438 L 1305.398438 806.339844 C 1311.691406 810.710938 1317.078125 816.441406 1320.839844 823.339844 C 1324.660156 830.238281 1326.851562 838.160156 1326.851562 846.898438 Z M 1326.851562 846.898438 "/>
</clipPath>
<clipPath id="clip-44">
<path clip-rule="nonzero" d="M 1326.851562 846.898438 L 1326.851562 930.429688 L 1195.519531 854.828125 C 1195.519531 854.761719 1195.589844 854.691406 1195.660156 854.550781 L 1195.71875 854.550781 C 1210.820312 833.179688 1231.171875 815.28125 1255.480469 802.988281 C 1269.621094 795.820312 1284.921875 796.230469 1297.691406 801.898438 L 1305.398438 806.339844 C 1311.691406 810.710938 1317.078125 816.441406 1320.839844 823.339844 C 1324.660156 830.238281 1326.851562 838.160156 1326.851562 846.898438 "/>
</clipPath>
<linearGradient id="linear-pattern-9" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="1.000008" y2="0" gradientTransform="matrix(131.329, 0, 0, 131.329, 1195.52, 864.03)">
<stop offset="0" stop-color="rgb(61.567688%, 65.097046%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(61.689758%, 65.203857%, 74.97406%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(61.935425%, 65.419006%, 75.12207%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(62.179565%, 65.632629%, 75.268555%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(62.425232%, 65.847778%, 75.415039%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(62.670898%, 66.061401%, 75.563049%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(62.915039%, 66.27655%, 75.709534%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(63.160706%, 66.490173%, 75.857544%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(63.406372%, 66.705322%, 76.004028%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(63.650513%, 66.920471%, 76.150513%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(63.896179%, 67.134094%, 76.298523%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(64.14032%, 67.349243%, 76.445007%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(64.385986%, 67.562866%, 76.591492%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(64.631653%, 67.778015%, 76.739502%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(64.875793%, 67.991638%, 76.885986%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(65.12146%, 68.206787%, 77.033997%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(65.367126%, 68.42041%, 77.180481%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(65.611267%, 68.635559%, 77.326965%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(65.856934%, 68.849182%, 77.474976%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(66.101074%, 69.064331%, 77.62146%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(66.346741%, 69.27948%, 77.767944%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(66.592407%, 69.493103%, 77.915955%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(66.836548%, 69.708252%, 78.062439%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(67.082214%, 69.921875%, 78.210449%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(67.327881%, 70.137024%, 78.356934%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(67.572021%, 70.350647%, 78.503418%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(67.817688%, 70.565796%, 78.651428%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(68.061829%, 70.779419%, 78.797913%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(68.307495%, 70.994568%, 78.944397%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(68.553162%, 71.209717%, 79.092407%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(68.797302%, 71.42334%, 79.238892%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(69.042969%, 71.638489%, 79.386902%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(69.288635%, 71.852112%, 79.533386%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(69.532776%, 72.067261%, 79.679871%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(69.778442%, 72.280884%, 79.827881%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(70.022583%, 72.496033%, 79.974365%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(70.26825%, 72.709656%, 80.12085%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(70.513916%, 72.924805%, 80.26886%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(70.758057%, 73.138428%, 80.415344%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(71.003723%, 73.353577%, 80.563354%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(71.24939%, 73.568726%, 80.709839%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(71.49353%, 73.782349%, 80.856323%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(71.739197%, 73.997498%, 81.004333%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(71.984863%, 74.211121%, 81.150818%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(72.229004%, 74.42627%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(72.47467%, 74.639893%, 81.445312%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(72.718811%, 74.855042%, 81.591797%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(72.964478%, 75.068665%, 81.739807%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(73.210144%, 75.283813%, 81.886292%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(73.454285%, 75.498962%, 82.032776%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(73.699951%, 75.712585%, 82.180786%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(73.945618%, 75.927734%, 82.327271%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(74.189758%, 76.141357%, 82.473755%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(74.435425%, 76.356506%, 82.621765%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(74.679565%, 76.570129%, 82.76825%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(74.925232%, 76.785278%, 82.91626%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(75.170898%, 76.998901%, 83.062744%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(75.415039%, 77.21405%, 83.209229%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(75.660706%, 77.427673%, 83.357239%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(75.906372%, 77.642822%, 83.503723%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(76.150513%, 77.857971%, 83.650208%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(76.396179%, 78.071594%, 83.798218%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(76.64032%, 78.286743%, 83.944702%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(76.885986%, 78.500366%, 84.092712%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(77.131653%, 78.715515%, 84.239197%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-45">
<path clip-rule="nonzero" d="M 1297 801 L 1306 801 L 1306 807 L 1297 807 Z M 1297 801 "/>
</clipPath>
<clipPath id="clip-46">
<path clip-rule="nonzero" d="M 1305.398438 806.339844 L 1297.691406 801.898438 C 1300.421875 803.128906 1302.949219 804.628906 1305.398438 806.339844 Z M 1305.398438 806.339844 "/>
</clipPath>
<clipPath id="clip-47">
<path clip-rule="nonzero" d="M 1305.398438 806.339844 L 1297.691406 801.898438 C 1300.421875 803.128906 1302.949219 804.628906 1305.398438 806.339844 "/>
</clipPath>
<linearGradient id="linear-pattern-10" gradientUnits="userSpaceOnUse" x1="0.000000000000056843" y1="0" x2="0.999171" y2="0" gradientTransform="matrix(7.7164, 0, 0, 7.7164, 1297.69, 804.12)">
<stop offset="0" stop-color="rgb(41.567993%, 79.998779%, 74.508667%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(41.389465%, 79.858398%, 74.3927%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(41.033936%, 79.576111%, 74.159241%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(40.679932%, 79.295349%, 73.927307%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(40.324402%, 79.013062%, 73.693848%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(39.968872%, 78.730774%, 73.461914%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(39.614868%, 78.450012%, 73.228455%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(39.259338%, 78.167725%, 72.996521%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(38.903809%, 77.886963%, 72.763062%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(38.548279%, 77.604675%, 72.531128%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(38.194275%, 77.323914%, 72.299194%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(37.838745%, 77.041626%, 72.065735%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(37.483215%, 76.759338%, 71.833801%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(37.129211%, 76.478577%, 71.600342%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(36.773682%, 76.196289%, 71.368408%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(36.418152%, 75.915527%, 71.134949%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(36.062622%, 75.63324%, 70.903015%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(35.708618%, 75.352478%, 70.669556%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(35.353088%, 75.07019%, 70.437622%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(34.997559%, 74.789429%, 70.204163%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(34.643555%, 74.507141%, 69.972229%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(34.288025%, 74.224854%, 69.73877%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(33.932495%, 73.944092%, 69.506836%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(33.576965%, 73.661804%, 69.273376%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(33.222961%, 73.381042%, 69.041443%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(32.867432%, 73.098755%, 68.807983%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(32.511902%, 72.817993%, 68.57605%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(32.157898%, 72.535706%, 68.344116%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(31.802368%, 72.253418%, 68.110657%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(31.446838%, 71.972656%, 67.878723%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(31.091309%, 71.690369%, 67.645264%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(30.737305%, 71.409607%, 67.41333%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(30.381775%, 71.127319%, 67.179871%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(30.026245%, 70.846558%, 66.947937%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(29.672241%, 70.56427%, 66.714478%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(29.316711%, 70.281982%, 66.482544%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(28.961182%, 70.001221%, 66.249084%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(28.605652%, 69.718933%, 66.017151%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(28.251648%, 69.438171%, 65.783691%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(27.896118%, 69.155884%, 65.551758%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(27.540588%, 68.875122%, 65.318298%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(27.186584%, 68.592834%, 65.086365%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(26.831055%, 68.310547%, 64.854431%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(26.475525%, 68.029785%, 64.620972%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(26.119995%, 67.747498%, 64.389038%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(25.765991%, 67.466736%, 64.155579%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(25.410461%, 67.184448%, 63.923645%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(25.054932%, 66.903687%, 63.690186%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(24.699402%, 66.621399%, 63.458252%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(24.345398%, 66.339111%, 63.224792%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(23.989868%, 66.05835%, 62.992859%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(23.634338%, 65.776062%, 62.759399%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(23.280334%, 65.4953%, 62.527466%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(22.924805%, 65.213013%, 62.294006%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(22.569275%, 64.932251%, 62.062073%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(22.213745%, 64.649963%, 61.828613%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(21.859741%, 64.367676%, 61.59668%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(21.504211%, 64.086914%, 61.36322%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(21.148682%, 63.804626%, 61.131287%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(20.794678%, 63.523865%, 60.899353%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(20.439148%, 63.241577%, 60.665894%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(20.083618%, 62.960815%, 60.43396%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(19.728088%, 62.678528%, 60.2005%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(19.374084%, 62.397766%, 59.968567%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(19.018555%, 62.115479%, 59.735107%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-48">
<path clip-rule="nonzero" d="M 102 905 L 262 905 L 262 1008 L 102 1008 Z M 102 905 "/>
</clipPath>
<clipPath id="clip-49">
<path clip-rule="nonzero" d="M 261.398438 907.519531 L 234.203125 953.160156 C 227.71875 947.128906 217.96875 945.621094 209.726562 949.941406 L 107.386719 1003.828125 C 105.679688 1004.730469 104.171875 1005.839844 102.8125 1007.089844 L 132.769531 968.539062 L 132.820312 968.589844 C 134.730469 965.929688 137.246094 963.609375 140.359375 961.949219 L 242.699219 908.070312 C 248.730469 904.898438 255.617188 904.898438 261.398438 907.519531 Z M 261.398438 907.519531 "/>
</clipPath>
<clipPath id="clip-50">
<path clip-rule="nonzero" d="M 261.398438 907.519531 L 234.203125 953.160156 C 227.71875 947.128906 217.96875 945.621094 209.726562 949.941406 L 107.386719 1003.828125 C 105.679688 1004.730469 104.171875 1005.839844 102.8125 1007.089844 L 132.769531 968.539062 L 132.820312 968.589844 C 134.730469 965.929688 137.246094 963.609375 140.359375 961.949219 L 242.699219 908.070312 C 248.730469 904.898438 255.617188 904.898438 261.398438 907.519531 "/>
</clipPath>
<linearGradient id="linear-pattern-11" gradientUnits="userSpaceOnUse" x1="0.0370536" y1="0" x2="1.367214" y2="0" gradientTransform="matrix(-130.235, 32.0005, -32.0005, -130.235, 273.308, 932.55)">
<stop offset="0" stop-color="rgb(30.732727%, 44.284058%, 75.263977%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(30.862427%, 44.389343%, 75.314331%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(31.123352%, 44.60144%, 75.416565%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(31.384277%, 44.813538%, 75.518799%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(31.645203%, 45.025635%, 75.621033%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(31.906128%, 45.237732%, 75.721741%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(32.167053%, 45.449829%, 75.823975%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(32.427979%, 45.6604%, 75.926208%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(32.688904%, 45.872498%, 76.028442%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(32.949829%, 46.084595%, 76.12915%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(33.210754%, 46.296692%, 76.231384%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(33.47168%, 46.508789%, 76.333618%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(33.731079%, 46.720886%, 76.435852%)" stop-opacity="1"/>
<stop offset="0.101562" stop-color="rgb(33.992004%, 46.932983%, 76.538086%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(34.25293%, 47.145081%, 76.638794%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(34.513855%, 47.355652%, 76.741028%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(34.77478%, 47.567749%, 76.843262%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(35.035706%, 47.779846%, 76.945496%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(35.296631%, 47.991943%, 77.046204%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(35.557556%, 48.204041%, 77.148438%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(35.818481%, 48.416138%, 77.250671%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(36.079407%, 48.628235%, 77.352905%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(36.340332%, 48.840332%, 77.453613%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(36.601257%, 49.050903%, 77.555847%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(36.862183%, 49.263%, 77.658081%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(37.123108%, 49.475098%, 77.760315%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(37.382507%, 49.687195%, 77.862549%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(37.643433%, 49.899292%, 77.963257%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(37.904358%, 50.111389%, 78.065491%)" stop-opacity="1"/>
<stop offset="0.226563" stop-color="rgb(38.165283%, 50.323486%, 78.167725%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(38.426208%, 50.535583%, 78.269958%)" stop-opacity="1"/>
<stop offset="0.242187" stop-color="rgb(38.687134%, 50.747681%, 78.370667%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(38.948059%, 50.958252%, 78.4729%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(39.208984%, 51.170349%, 78.575134%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(39.46991%, 51.382446%, 78.677368%)" stop-opacity="1"/>
<stop offset="0.273437" stop-color="rgb(39.730835%, 51.594543%, 78.778076%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(39.99176%, 51.806641%, 78.88031%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(40.252686%, 52.018738%, 78.982544%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(40.513611%, 52.230835%, 79.084778%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(40.774536%, 52.442932%, 79.187012%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(41.033936%, 52.653503%, 79.28772%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(41.294861%, 52.865601%, 79.389954%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(41.555786%, 53.077698%, 79.492188%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(41.816711%, 53.289795%, 79.594421%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(42.077637%, 53.501892%, 79.695129%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(42.338562%, 53.713989%, 79.797363%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(42.599487%, 53.926086%, 79.899597%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(42.860413%, 54.138184%, 80.001831%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(43.121338%, 54.348755%, 80.102539%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(43.382263%, 54.560852%, 80.204773%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(43.643188%, 54.772949%, 80.307007%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(43.904114%, 54.985046%, 80.409241%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(44.165039%, 55.197144%, 80.511475%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(44.425964%, 55.409241%, 80.612183%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(44.685364%, 55.621338%, 80.714417%)" stop-opacity="1"/>
<stop offset="0.429687" stop-color="rgb(44.946289%, 55.833435%, 80.81665%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(45.207214%, 56.044006%, 80.918884%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(45.46814%, 56.256104%, 81.019592%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(45.729065%, 56.468201%, 81.121826%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(45.98999%, 56.680298%, 81.22406%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(46.250916%, 56.892395%, 81.326294%)" stop-opacity="1"/>
<stop offset="0.476563" stop-color="rgb(46.511841%, 57.104492%, 81.427002%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(46.772766%, 57.316589%, 81.529236%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(47.033691%, 57.528687%, 81.63147%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(47.294617%, 57.740784%, 81.733704%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(47.555542%, 57.951355%, 81.835938%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(47.816467%, 58.163452%, 81.936646%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(48.077393%, 58.375549%, 82.038879%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(48.336792%, 58.587646%, 82.141113%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(48.597717%, 58.799744%, 82.243347%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(48.858643%, 59.011841%, 82.344055%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(49.119568%, 59.223938%, 82.446289%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(49.380493%, 59.436035%, 82.548523%)" stop-opacity="1"/>
<stop offset="0.570313" stop-color="rgb(49.641418%, 59.646606%, 82.650757%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(49.902344%, 59.858704%, 82.751465%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(50.163269%, 60.070801%, 82.853699%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(50.424194%, 60.282898%, 82.955933%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(50.68512%, 60.494995%, 83.058167%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(50.946045%, 60.707092%, 83.1604%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(51.20697%, 60.919189%, 83.261108%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(51.467896%, 61.131287%, 83.363342%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(51.728821%, 61.341858%, 83.465576%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(51.98822%, 61.553955%, 83.56781%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(52.249146%, 61.766052%, 83.668518%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(52.510071%, 61.978149%, 83.770752%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(52.770996%, 62.190247%, 83.872986%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(53.031921%, 62.402344%, 83.97522%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(53.292847%, 62.614441%, 84.075928%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(53.553772%, 62.826538%, 84.178162%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(53.814697%, 63.038635%, 84.280396%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(54.075623%, 63.249207%, 84.382629%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(54.336548%, 63.461304%, 84.484863%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(54.597473%, 63.673401%, 84.585571%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(54.814148%, 63.850403%, 84.671021%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.901123%, 63.920593%, 84.70459%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-51">
<path clip-rule="nonzero" d="M 376 974 L 785 974 L 785 1193 L 376 1193 Z M 376 974 "/>
</clipPath>
<clipPath id="clip-52">
<path clip-rule="nonzero" d="M 781.226562 1150.441406 L 781.375 1150.441406 L 780.972656 1150.949219 C 779.617188 1153.261719 777.957031 1155.46875 775.898438 1157.328125 L 748.503906 1192.109375 C 752.726562 1184.070312 752.625 1174.070312 747.296875 1165.929688 L 658.632812 1029.910156 C 649.082031 1015.28125 629.277344 1011.621094 615.101562 1021.820312 L 389.820312 1183.96875 C 385.699219 1186.941406 380.773438 1187.890625 376.199219 1187.039062 L 402.636719 1142.648438 C 408.617188 1146.46875 416.609375 1146.570312 422.792969 1142.148438 L 648.074219 979.949219 C 662.25 969.75 682.054688 973.46875 691.554688 988.089844 L 780.21875 1124.058594 C 785.597656 1132.300781 785.546875 1142.351562 781.226562 1150.441406 Z M 781.226562 1150.441406 "/>
</clipPath>
<clipPath id="clip-53">
<path clip-rule="nonzero" d="M 781.226562 1150.441406 L 781.375 1150.441406 L 780.972656 1150.949219 C 779.617188 1153.261719 777.957031 1155.46875 775.898438 1157.328125 L 748.503906 1192.109375 C 752.726562 1184.070312 752.625 1174.070312 747.296875 1165.929688 L 658.632812 1029.910156 C 649.082031 1015.28125 629.277344 1011.621094 615.101562 1021.820312 L 389.820312 1183.96875 C 385.699219 1186.941406 380.773438 1187.890625 376.199219 1187.039062 L 402.636719 1142.648438 C 408.617188 1146.46875 416.609375 1146.570312 422.792969 1142.148438 L 648.074219 979.949219 C 662.25 969.75 682.054688 973.46875 691.554688 988.089844 L 780.21875 1124.058594 C 785.597656 1132.300781 785.546875 1142.351562 781.226562 1150.441406 "/>
</clipPath>
<linearGradient id="linear-pattern-12" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="1.003011" y2="0" gradientTransform="matrix(408.17, 0, 0, 408.17, 376.199, 1083.15)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(29.901123%, 43.608093%, 74.938965%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(30.097961%, 43.768311%, 75.016785%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.2948%, 43.927002%, 75.093079%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.490112%, 44.087219%, 75.169373%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.686951%, 44.247437%, 75.247192%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.883789%, 44.407654%, 75.323486%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.080627%, 44.566345%, 75.39978%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.277466%, 44.726562%, 75.4776%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.474304%, 44.88678%, 75.553894%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.671143%, 45.045471%, 75.630188%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.867981%, 45.205688%, 75.708008%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.064819%, 45.365906%, 75.784302%)" stop-opacity="1"/>
<stop offset="0.101562" stop-color="rgb(32.260132%, 45.526123%, 75.860596%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.45697%, 45.684814%, 75.938416%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(32.653809%, 45.845032%, 76.014709%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.850647%, 46.005249%, 76.091003%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.047485%, 46.16394%, 76.168823%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.244324%, 46.324158%, 76.245117%)" stop-opacity="1"/>
<stop offset="0.148437" stop-color="rgb(33.441162%, 46.484375%, 76.322937%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.638%, 46.644592%, 76.399231%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(33.834839%, 46.803284%, 76.475525%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(34.030151%, 46.963501%, 76.553345%)" stop-opacity="1"/>
<stop offset="0.179687" stop-color="rgb(34.22699%, 47.123718%, 76.629639%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.423828%, 47.283936%, 76.705933%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(34.620667%, 47.442627%, 76.783752%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.817505%, 47.602844%, 76.860046%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(35.014343%, 47.763062%, 76.93634%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.211182%, 47.921753%, 77.01416%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(35.40802%, 48.08197%, 77.090454%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.604858%, 48.242188%, 77.166748%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.800171%, 48.402405%, 77.244568%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.997009%, 48.561096%, 77.320862%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(36.193848%, 48.721313%, 77.397156%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.390686%, 48.881531%, 77.474976%)" stop-opacity="1"/>
<stop offset="0.273437" stop-color="rgb(36.587524%, 49.040222%, 77.55127%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.784363%, 49.200439%, 77.627563%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(36.981201%, 49.360657%, 77.705383%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.17804%, 49.520874%, 77.781677%)" stop-opacity="1"/>
<stop offset="0.304687" stop-color="rgb(37.374878%, 49.679565%, 77.857971%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.57019%, 49.839783%, 77.935791%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(37.767029%, 50%, 78.012085%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.963867%, 50.160217%, 78.089905%)" stop-opacity="1"/>
<stop offset="0.335937" stop-color="rgb(38.160706%, 50.318909%, 78.166199%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.357544%, 50.479126%, 78.242493%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(38.554382%, 50.639343%, 78.320312%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.751221%, 50.798035%, 78.396606%)" stop-opacity="1"/>
<stop offset="0.367187" stop-color="rgb(38.948059%, 50.958252%, 78.4729%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.144897%, 51.118469%, 78.55072%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(39.34021%, 51.278687%, 78.627014%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.537048%, 51.437378%, 78.703308%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(39.733887%, 51.597595%, 78.781128%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.930725%, 51.757812%, 78.857422%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(40.127563%, 51.916504%, 78.933716%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.324402%, 52.076721%, 79.011536%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(40.52124%, 52.236938%, 79.08783%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.718079%, 52.397156%, 79.164124%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(40.914917%, 52.555847%, 79.241943%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.110229%, 52.716064%, 79.318237%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(41.307068%, 52.876282%, 79.394531%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.503906%, 53.036499%, 79.472351%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(41.700745%, 53.19519%, 79.548645%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.897583%, 53.355408%, 79.624939%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(42.094421%, 53.515625%, 79.702759%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.29126%, 53.674316%, 79.779053%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.488098%, 53.834534%, 79.855347%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.684937%, 53.994751%, 79.933167%)" stop-opacity="1"/>
<stop offset="0.523437" stop-color="rgb(42.880249%, 54.154968%, 80.00946%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.077087%, 54.31366%, 80.08728%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.273926%, 54.473877%, 80.163574%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.470764%, 54.634094%, 80.239868%)" stop-opacity="1"/>
<stop offset="0.554687" stop-color="rgb(43.667603%, 54.792786%, 80.317688%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.864441%, 54.953003%, 80.393982%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.061279%, 55.11322%, 80.470276%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.258118%, 55.273438%, 80.548096%)" stop-opacity="1"/>
<stop offset="0.585937" stop-color="rgb(44.454956%, 55.432129%, 80.62439%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.650269%, 55.592346%, 80.700684%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(44.847107%, 55.752563%, 80.778503%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(45.043945%, 55.912781%, 80.854797%)" stop-opacity="1"/>
<stop offset="0.617187" stop-color="rgb(45.240784%, 56.071472%, 80.931091%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.437622%, 56.231689%, 81.008911%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.63446%, 56.391907%, 81.085205%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.831299%, 56.550598%, 81.161499%)" stop-opacity="1"/>
<stop offset="0.648437" stop-color="rgb(46.028137%, 56.710815%, 81.239319%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.224976%, 56.871033%, 81.315613%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.420288%, 57.03125%, 81.391907%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.617126%, 57.189941%, 81.469727%)" stop-opacity="1"/>
<stop offset="0.679687" stop-color="rgb(46.813965%, 57.350159%, 81.546021%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(47.010803%, 57.510376%, 81.622314%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.207642%, 57.670593%, 81.700134%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.40448%, 57.829285%, 81.776428%)" stop-opacity="1"/>
<stop offset="0.710937" stop-color="rgb(47.601318%, 57.989502%, 81.854248%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.798157%, 58.149719%, 81.930542%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(47.994995%, 58.308411%, 82.006836%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.190308%, 58.468628%, 82.084656%)" stop-opacity="1"/>
<stop offset="0.742187" stop-color="rgb(48.387146%, 58.628845%, 82.16095%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.583984%, 58.789062%, 82.237244%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(48.780823%, 58.947754%, 82.315063%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.977661%, 59.107971%, 82.391357%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(49.1745%, 59.268188%, 82.467651%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.371338%, 59.42688%, 82.545471%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(49.568176%, 59.587097%, 82.621765%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.765015%, 59.747314%, 82.698059%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(49.960327%, 59.907532%, 82.775879%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.157166%, 60.066223%, 82.852173%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(50.354004%, 60.22644%, 82.928467%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.550842%, 60.386658%, 83.006287%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(50.747681%, 60.546875%, 83.082581%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.944519%, 60.705566%, 83.158875%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(51.141357%, 60.865784%, 83.236694%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.338196%, 61.026001%, 83.312988%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(51.535034%, 61.184692%, 83.389282%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.730347%, 61.34491%, 83.467102%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(51.927185%, 61.505127%, 83.543396%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.124023%, 61.665344%, 83.61969%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(52.320862%, 61.824036%, 83.69751%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.5177%, 61.984253%, 83.773804%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(52.714539%, 62.14447%, 83.851624%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.911377%, 62.303162%, 83.927917%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(53.108215%, 62.463379%, 84.004211%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.305054%, 62.623596%, 84.082031%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(53.500366%, 62.783813%, 84.158325%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.697205%, 62.942505%, 84.234619%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(53.894043%, 63.102722%, 84.312439%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.090881%, 63.262939%, 84.388733%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(54.28772%, 63.423157%, 84.465027%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.484558%, 63.581848%, 84.542847%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.742432%, 63.790894%, 84.642029%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-54">
<path clip-rule="nonzero" d="M 95 947 L 752 947 L 752 1438 L 95 1438 Z M 95 947 "/>
</clipPath>
<clipPath id="clip-55">
<path clip-rule="nonzero" d="M 748.503906 1192.109375 C 746.441406 1196.039062 743.277344 1199.550781 739.203125 1202.070312 L 365.742188 1433.128906 C 353.226562 1440.871094 336.792969 1436.851562 329.304688 1424.179688 L 98.640625 1034.839844 C 94.519531 1027.898438 94.621094 1019.660156 98.191406 1013.019531 C 99.34375 1010.808594 100.902344 1008.800781 102.8125 1007.089844 C 104.171875 1005.839844 105.679688 1004.730469 107.386719 1003.828125 L 209.726562 949.941406 C 219.980469 944.558594 232.59375 948.230469 238.324219 958.289062 L 363.785156 1178.289062 C 369.0625 1187.539062 381.175781 1190.199219 389.820312 1183.96875 L 615.101562 1021.820312 C 629.277344 1011.621094 649.082031 1015.28125 658.632812 1029.910156 L 747.296875 1165.929688 C 752.625 1174.070312 752.726562 1184.070312 748.503906 1192.109375 Z M 748.503906 1192.109375 "/>
</clipPath>
<clipPath id="clip-56">
<path clip-rule="nonzero" d="M 748.503906 1192.109375 C 746.441406 1196.039062 743.277344 1199.550781 739.203125 1202.070312 L 365.742188 1433.128906 C 353.226562 1440.871094 336.792969 1436.851562 329.304688 1424.179688 L 98.640625 1034.839844 C 94.519531 1027.898438 94.621094 1019.660156 98.191406 1013.019531 C 99.34375 1010.808594 100.902344 1008.800781 102.8125 1007.089844 C 104.171875 1005.839844 105.679688 1004.730469 107.386719 1003.828125 L 209.726562 949.941406 C 219.980469 944.558594 232.59375 948.230469 238.324219 958.289062 L 363.785156 1178.289062 C 369.0625 1187.539062 381.175781 1190.199219 389.820312 1183.96875 L 615.101562 1021.820312 C 629.277344 1011.621094 649.082031 1015.28125 658.632812 1029.910156 L 747.296875 1165.929688 C 752.625 1174.070312 752.726562 1184.070312 748.503906 1192.109375 "/>
</clipPath>
<linearGradient id="linear-pattern-13" gradientUnits="userSpaceOnUse" x1="-0.00154017" y1="0" x2="1.001871" y2="0" gradientTransform="matrix(655.968, 0, 0, 655.968, 95.5306, 1192.27)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(29.980469%, 43.67218%, 74.969482%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.255127%, 43.896484%, 75.07782%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.451965%, 44.056702%, 75.154114%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.648804%, 44.215393%, 75.231934%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.845642%, 44.37561%, 75.308228%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.04248%, 44.535828%, 75.386047%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.239319%, 44.696045%, 75.462341%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.436157%, 44.854736%, 75.538635%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.632996%, 45.014954%, 75.616455%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.829834%, 45.175171%, 75.692749%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.026672%, 45.335388%, 75.769043%)" stop-opacity="1"/>
<stop offset="0.101562" stop-color="rgb(32.223511%, 45.495605%, 75.846863%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.420349%, 45.654297%, 75.923157%)" stop-opacity="1"/>
<stop offset="0.117187" stop-color="rgb(32.617188%, 45.814514%, 76.000977%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.814026%, 45.974731%, 76.077271%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.010864%, 46.134949%, 76.153564%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.206177%, 46.29364%, 76.231384%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(33.403015%, 46.453857%, 76.307678%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.599854%, 46.614075%, 76.383972%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(33.796692%, 46.774292%, 76.461792%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(33.99353%, 46.932983%, 76.538086%)" stop-opacity="1"/>
<stop offset="0.179687" stop-color="rgb(34.190369%, 47.093201%, 76.61438%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.387207%, 47.253418%, 76.6922%)" stop-opacity="1"/>
<stop offset="0.195312" stop-color="rgb(34.584045%, 47.413635%, 76.768494%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.780884%, 47.572327%, 76.846313%)" stop-opacity="1"/>
<stop offset="0.210937" stop-color="rgb(34.977722%, 47.732544%, 76.922607%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.174561%, 47.892761%, 76.998901%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(35.371399%, 48.052979%, 77.076721%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.568237%, 48.213196%, 77.153015%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.765076%, 48.371887%, 77.229309%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.961914%, 48.532104%, 77.307129%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(36.158752%, 48.692322%, 77.383423%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.354065%, 48.852539%, 77.459717%)" stop-opacity="1"/>
<stop offset="0.273437" stop-color="rgb(36.550903%, 49.01123%, 77.537537%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.747742%, 49.171448%, 77.613831%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(36.94458%, 49.331665%, 77.69165%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.141418%, 49.491882%, 77.767944%)" stop-opacity="1"/>
<stop offset="0.304687" stop-color="rgb(37.338257%, 49.650574%, 77.844238%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.535095%, 49.810791%, 77.922058%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(37.731934%, 49.971008%, 77.998352%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.928772%, 50.131226%, 78.074646%)" stop-opacity="1"/>
<stop offset="0.335937" stop-color="rgb(38.12561%, 50.289917%, 78.152466%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.322449%, 50.450134%, 78.22876%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(38.519287%, 50.610352%, 78.30658%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.716125%, 50.770569%, 78.382874%)" stop-opacity="1"/>
<stop offset="0.367187" stop-color="rgb(38.912964%, 50.92926%, 78.459167%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.109802%, 51.089478%, 78.536987%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(39.306641%, 51.249695%, 78.613281%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.503479%, 51.409912%, 78.689575%)" stop-opacity="1"/>
<stop offset="0.398437" stop-color="rgb(39.698792%, 51.570129%, 78.767395%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.89563%, 51.728821%, 78.843689%)" stop-opacity="1"/>
<stop offset="0.414062" stop-color="rgb(40.092468%, 51.889038%, 78.919983%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.289307%, 52.049255%, 78.997803%)" stop-opacity="1"/>
<stop offset="0.429687" stop-color="rgb(40.486145%, 52.209473%, 79.074097%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.682983%, 52.368164%, 79.151917%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(40.879822%, 52.528381%, 79.22821%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.07666%, 52.688599%, 79.304504%)" stop-opacity="1"/>
<stop offset="0.460937" stop-color="rgb(41.273499%, 52.848816%, 79.382324%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.470337%, 53.007507%, 79.458618%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(41.667175%, 53.167725%, 79.534912%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.864014%, 53.327942%, 79.612732%)" stop-opacity="1"/>
<stop offset="0.492187" stop-color="rgb(42.060852%, 53.488159%, 79.689026%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.25769%, 53.646851%, 79.766846%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.454529%, 53.807068%, 79.84314%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.651367%, 53.967285%, 79.919434%)" stop-opacity="1"/>
<stop offset="0.523437" stop-color="rgb(42.848206%, 54.127502%, 79.997253%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.043518%, 54.28772%, 80.073547%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.240356%, 54.446411%, 80.149841%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.437195%, 54.606628%, 80.227661%)" stop-opacity="1"/>
<stop offset="0.554687" stop-color="rgb(43.634033%, 54.766846%, 80.303955%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.830872%, 54.927063%, 80.380249%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.02771%, 55.085754%, 80.458069%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.224548%, 55.245972%, 80.534363%)" stop-opacity="1"/>
<stop offset="0.585937" stop-color="rgb(44.421387%, 55.406189%, 80.612183%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.618225%, 55.566406%, 80.688477%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(44.815063%, 55.725098%, 80.764771%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(45.011902%, 55.885315%, 80.84259%)" stop-opacity="1"/>
<stop offset="0.617187" stop-color="rgb(45.20874%, 56.045532%, 80.918884%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.405579%, 56.20575%, 80.995178%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.602417%, 56.364441%, 81.072998%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.799255%, 56.524658%, 81.149292%)" stop-opacity="1"/>
<stop offset="0.648437" stop-color="rgb(45.996094%, 56.684875%, 81.225586%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.192932%, 56.845093%, 81.303406%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.388245%, 57.00531%, 81.3797%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.585083%, 57.164001%, 81.45752%)" stop-opacity="1"/>
<stop offset="0.679687" stop-color="rgb(46.781921%, 57.324219%, 81.533813%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(46.97876%, 57.484436%, 81.610107%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.175598%, 57.644653%, 81.687927%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.372437%, 57.803345%, 81.764221%)" stop-opacity="1"/>
<stop offset="0.710937" stop-color="rgb(47.569275%, 57.963562%, 81.840515%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.766113%, 58.123779%, 81.918335%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(47.962952%, 58.283997%, 81.994629%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.15979%, 58.442688%, 82.072449%)" stop-opacity="1"/>
<stop offset="0.742187" stop-color="rgb(48.356628%, 58.602905%, 82.148743%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.553467%, 58.763123%, 82.225037%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(48.750305%, 58.92334%, 82.302856%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.947144%, 59.082031%, 82.37915%)" stop-opacity="1"/>
<stop offset="0.773437" stop-color="rgb(49.143982%, 59.242249%, 82.455444%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.34082%, 59.402466%, 82.533264%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(49.537659%, 59.562683%, 82.609558%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.732971%, 59.7229%, 82.685852%)" stop-opacity="1"/>
<stop offset="0.804687" stop-color="rgb(49.92981%, 59.881592%, 82.763672%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.126648%, 60.041809%, 82.839966%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(50.323486%, 60.202026%, 82.917786%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.520325%, 60.362244%, 82.99408%)" stop-opacity="1"/>
<stop offset="0.835937" stop-color="rgb(50.717163%, 60.520935%, 83.070374%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.914001%, 60.681152%, 83.148193%)" stop-opacity="1"/>
<stop offset="0.851562" stop-color="rgb(51.11084%, 60.84137%, 83.224487%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.307678%, 61.001587%, 83.300781%)" stop-opacity="1"/>
<stop offset="0.867187" stop-color="rgb(51.504517%, 61.160278%, 83.378601%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.701355%, 61.320496%, 83.454895%)" stop-opacity="1"/>
<stop offset="0.882812" stop-color="rgb(51.898193%, 61.480713%, 83.532715%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.095032%, 61.64093%, 83.609009%)" stop-opacity="1"/>
<stop offset="0.898437" stop-color="rgb(52.29187%, 61.799622%, 83.685303%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.488708%, 61.959839%, 83.763123%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(52.685547%, 62.120056%, 83.839417%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.882385%, 62.280273%, 83.91571%)" stop-opacity="1"/>
<stop offset="0.929687" stop-color="rgb(53.077698%, 62.440491%, 83.99353%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.274536%, 62.599182%, 84.069824%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(53.471375%, 62.759399%, 84.146118%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.668213%, 62.919617%, 84.223938%)" stop-opacity="1"/>
<stop offset="0.960937" stop-color="rgb(53.865051%, 63.079834%, 84.300232%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.06189%, 63.238525%, 84.378052%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(54.258728%, 63.398743%, 84.454346%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.455566%, 63.55896%, 84.53064%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.727173%, 63.780212%, 84.637451%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-57">
<path clip-rule="nonzero" d="M 234 907 L 403 907 L 403 1188 L 234 1188 Z M 234 907 "/>
</clipPath>
<clipPath id="clip-58">
<path clip-rule="nonzero" d="M 402.636719 1142.648438 L 376.199219 1187.039062 C 371.171875 1186.128906 366.546875 1183.121094 363.785156 1178.289062 L 238.324219 958.289062 C 237.21875 956.328125 235.8125 954.570312 234.203125 953.160156 L 261.398438 907.519531 C 265.417969 909.28125 268.9375 912.351562 271.246094 916.421875 L 396.707031 1136.46875 C 398.214844 1139.089844 400.273438 1141.199219 402.636719 1142.648438 Z M 402.636719 1142.648438 "/>
</clipPath>
<clipPath id="clip-59">
<path clip-rule="nonzero" d="M 402.636719 1142.648438 L 376.199219 1187.039062 C 371.171875 1186.128906 366.546875 1183.121094 363.785156 1178.289062 L 238.324219 958.289062 C 237.21875 956.328125 235.8125 954.570312 234.203125 953.160156 L 261.398438 907.519531 C 265.417969 909.28125 268.9375 912.351562 271.246094 916.421875 L 396.707031 1136.46875 C 398.214844 1139.089844 400.273438 1141.199219 402.636719 1142.648438 "/>
</clipPath>
<linearGradient id="linear-pattern-14" gradientUnits="userSpaceOnUse" x1="0.000000000000000222" y1="0" x2="1.000006" y2="0" gradientTransform="matrix(168.434, 0, 0, 168.434, 234.203, 1047.28)">
<stop offset="0" stop-color="rgb(29.803467%, 43.528748%, 74.900818%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(29.901123%, 43.608093%, 74.938965%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(30.096436%, 43.766785%, 75.015259%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(30.293274%, 43.927002%, 75.093079%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(30.488586%, 44.085693%, 75.169373%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(30.685425%, 44.245911%, 75.245667%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(30.880737%, 44.404602%, 75.32196%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(31.077576%, 44.563293%, 75.398254%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(31.272888%, 44.723511%, 75.476074%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(31.469727%, 44.882202%, 75.552368%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(31.665039%, 45.042419%, 75.628662%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(31.861877%, 45.201111%, 75.704956%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(32.05719%, 45.359802%, 75.78125%)" stop-opacity="1"/>
<stop offset="0.101563" stop-color="rgb(32.254028%, 45.52002%, 75.85907%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(32.449341%, 45.678711%, 75.935364%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(32.646179%, 45.838928%, 76.011658%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(32.841492%, 45.99762%, 76.087952%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(33.03833%, 46.156311%, 76.164246%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(33.233643%, 46.316528%, 76.242065%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(33.430481%, 46.47522%, 76.318359%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(33.625793%, 46.635437%, 76.394653%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(33.822632%, 46.794128%, 76.470947%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(34.017944%, 46.954346%, 76.547241%)" stop-opacity="1"/>
<stop offset="0.179687" stop-color="rgb(34.214783%, 47.113037%, 76.625061%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(34.410095%, 47.271729%, 76.701355%)" stop-opacity="1"/>
<stop offset="0.195313" stop-color="rgb(34.606934%, 47.431946%, 76.777649%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(34.802246%, 47.590637%, 76.853943%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(34.999084%, 47.750854%, 76.930237%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(35.194397%, 47.909546%, 77.008057%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(35.391235%, 48.068237%, 77.084351%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(35.586548%, 48.228455%, 77.160645%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(35.783386%, 48.387146%, 77.236938%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(35.978699%, 48.547363%, 77.313232%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(36.175537%, 48.706055%, 77.391052%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(36.37085%, 48.864746%, 77.467346%)" stop-opacity="1"/>
<stop offset="0.273438" stop-color="rgb(36.567688%, 49.024963%, 77.54364%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(36.763%, 49.183655%, 77.619934%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(36.959839%, 49.343872%, 77.696228%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(37.155151%, 49.502563%, 77.774048%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(37.35199%, 49.661255%, 77.850342%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(37.547302%, 49.821472%, 77.926636%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(37.744141%, 49.980164%, 78.00293%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(37.939453%, 50.140381%, 78.079224%)" stop-opacity="1"/>
<stop offset="0.335937" stop-color="rgb(38.136292%, 50.299072%, 78.157043%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(38.331604%, 50.45929%, 78.233337%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(38.528442%, 50.617981%, 78.309631%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(38.723755%, 50.776672%, 78.385925%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(38.920593%, 50.93689%, 78.462219%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(39.115906%, 51.095581%, 78.538513%)" stop-opacity="1"/>
<stop offset="0.382812" stop-color="rgb(39.312744%, 51.255798%, 78.616333%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(39.508057%, 51.41449%, 78.692627%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(39.704895%, 51.573181%, 78.768921%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(39.900208%, 51.733398%, 78.845215%)" stop-opacity="1"/>
<stop offset="0.414063" stop-color="rgb(40.097046%, 51.89209%, 78.921509%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(40.292358%, 52.052307%, 78.999329%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(40.489197%, 52.210999%, 79.075623%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(40.684509%, 52.36969%, 79.151917%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(40.881348%, 52.529907%, 79.22821%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(41.07666%, 52.688599%, 79.304504%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(41.273499%, 52.848816%, 79.382324%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(41.468811%, 53.007507%, 79.458618%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(41.665649%, 53.166199%, 79.534912%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(41.860962%, 53.326416%, 79.611206%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(42.0578%, 53.485107%, 79.6875%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(42.253113%, 53.645325%, 79.76532%)" stop-opacity="1"/>
<stop offset="0.507812" stop-color="rgb(42.449951%, 53.804016%, 79.841614%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(42.645264%, 53.962708%, 79.917908%)" stop-opacity="1"/>
<stop offset="0.523437" stop-color="rgb(42.842102%, 54.122925%, 79.994202%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(43.037415%, 54.281616%, 80.070496%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(43.234253%, 54.441833%, 80.148315%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(43.429565%, 54.600525%, 80.224609%)" stop-opacity="1"/>
<stop offset="0.554687" stop-color="rgb(43.626404%, 54.760742%, 80.300903%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(43.821716%, 54.919434%, 80.377197%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(44.018555%, 55.078125%, 80.453491%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(44.215393%, 55.238342%, 80.531311%)" stop-opacity="1"/>
<stop offset="0.585937" stop-color="rgb(44.410706%, 55.397034%, 80.607605%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(44.607544%, 55.557251%, 80.683899%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(44.802856%, 55.715942%, 80.760193%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(44.999695%, 55.874634%, 80.836487%)" stop-opacity="1"/>
<stop offset="0.617187" stop-color="rgb(45.195007%, 56.034851%, 80.914307%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(45.391846%, 56.193542%, 80.990601%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(45.587158%, 56.35376%, 81.066895%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(45.783997%, 56.512451%, 81.143188%)" stop-opacity="1"/>
<stop offset="0.648437" stop-color="rgb(45.979309%, 56.671143%, 81.219482%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(46.176147%, 56.83136%, 81.297302%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(46.37146%, 56.990051%, 81.373596%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(46.568298%, 57.150269%, 81.44989%)" stop-opacity="1"/>
<stop offset="0.679687" stop-color="rgb(46.763611%, 57.30896%, 81.526184%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(46.960449%, 57.467651%, 81.602478%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(47.155762%, 57.627869%, 81.680298%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(47.3526%, 57.78656%, 81.756592%)" stop-opacity="1"/>
<stop offset="0.710937" stop-color="rgb(47.547913%, 57.946777%, 81.832886%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(47.744751%, 58.105469%, 81.90918%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(47.940063%, 58.265686%, 81.985474%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(48.136902%, 58.424377%, 82.063293%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(48.332214%, 58.583069%, 82.139587%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(48.529053%, 58.743286%, 82.215881%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(48.724365%, 58.901978%, 82.292175%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(48.921204%, 59.062195%, 82.368469%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(49.116516%, 59.220886%, 82.446289%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(49.313354%, 59.379578%, 82.522583%)" stop-opacity="1"/>
<stop offset="0.789063" stop-color="rgb(49.508667%, 59.539795%, 82.598877%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(49.705505%, 59.698486%, 82.675171%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(49.900818%, 59.858704%, 82.751465%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(50.097656%, 60.017395%, 82.827759%)" stop-opacity="1"/>
<stop offset="0.820313" stop-color="rgb(50.292969%, 60.176086%, 82.905579%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(50.489807%, 60.336304%, 82.981873%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(50.68512%, 60.494995%, 83.058167%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(50.881958%, 60.655212%, 83.13446%)" stop-opacity="1"/>
<stop offset="0.851563" stop-color="rgb(51.077271%, 60.813904%, 83.210754%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(51.274109%, 60.972595%, 83.288574%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(51.469421%, 61.132812%, 83.364868%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(51.66626%, 61.291504%, 83.441162%)" stop-opacity="1"/>
<stop offset="0.882813" stop-color="rgb(51.861572%, 61.451721%, 83.517456%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(52.058411%, 61.610413%, 83.59375%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(52.253723%, 61.77063%, 83.67157%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(52.450562%, 61.929321%, 83.747864%)" stop-opacity="1"/>
<stop offset="0.914063" stop-color="rgb(52.645874%, 62.088013%, 83.824158%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.842712%, 62.24823%, 83.900452%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(53.038025%, 62.406921%, 83.976746%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(53.234863%, 62.567139%, 84.054565%)" stop-opacity="1"/>
<stop offset="0.945313" stop-color="rgb(53.430176%, 62.72583%, 84.130859%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(53.627014%, 62.884521%, 84.207153%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(53.822327%, 63.044739%, 84.283447%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(54.019165%, 63.20343%, 84.359741%)" stop-opacity="1"/>
<stop offset="0.976563" stop-color="rgb(54.214478%, 63.363647%, 84.437561%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(54.411316%, 63.522339%, 84.513855%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(54.606628%, 63.68103%, 84.590149%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(54.803467%, 63.841248%, 84.666443%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-60">
<path clip-rule="nonzero" d="M 95 947 L 752 947 L 752 1193 L 95 1193 Z M 95 947 "/>
</clipPath>
<clipPath id="clip-61">
<path clip-rule="nonzero" d="M 97.042969 1017.929688 C 98.199219 1015.71875 99.757812 1013.710938 101.664062 1012 C 103.023438 1010.738281 104.53125 1009.640625 106.238281 1008.730469 L 208.578125 954.851562 C 218.832031 949.46875 231.445312 953.140625 237.175781 963.191406 L 362.636719 1183.199219 C 367.914062 1192.449219 380.027344 1195.109375 388.671875 1188.878906 L 613.953125 1026.730469 C 628.128906 1016.53125 647.933594 1020.191406 657.484375 1034.820312 L 746.148438 1170.828125 C 749.6875 1176.25 750.875 1182.46875 750.066406 1188.398438 C 752.546875 1181.128906 751.832031 1172.859375 747.296875 1165.929688 L 658.632812 1029.910156 C 649.082031 1015.28125 629.277344 1011.621094 615.101562 1021.820312 L 389.820312 1183.96875 C 381.175781 1190.199219 369.0625 1187.539062 363.785156 1178.289062 L 238.324219 958.289062 C 232.59375 948.230469 219.980469 944.558594 209.726562 949.941406 L 107.386719 1003.828125 C 105.679688 1004.730469 104.171875 1005.839844 102.8125 1007.089844 C 100.902344 1008.800781 99.34375 1010.808594 98.191406 1013.019531 C 96.835938 1015.539062 95.988281 1018.289062 95.671875 1021.109375 C 96.054688 1020.019531 96.492188 1018.949219 97.042969 1017.929688 Z M 97.042969 1017.929688 "/>
</clipPath>
<clipPath id="clip-62">
<path clip-rule="nonzero" d="M 97.042969 1017.929688 C 98.199219 1015.71875 99.757812 1013.710938 101.664062 1012 C 103.023438 1010.738281 104.53125 1009.640625 106.238281 1008.730469 L 208.578125 954.851562 C 218.832031 949.46875 231.445312 953.140625 237.175781 963.191406 L 362.636719 1183.199219 C 367.914062 1192.449219 380.027344 1195.109375 388.671875 1188.878906 L 613.953125 1026.730469 C 628.128906 1016.53125 647.933594 1020.191406 657.484375 1034.820312 L 746.148438 1170.828125 C 749.6875 1176.25 750.875 1182.46875 750.066406 1188.398438 C 752.546875 1181.128906 751.832031 1172.859375 747.296875 1165.929688 L 658.632812 1029.910156 C 649.082031 1015.28125 629.277344 1011.621094 615.101562 1021.820312 L 389.820312 1183.96875 C 381.175781 1190.199219 369.0625 1187.539062 363.785156 1178.289062 L 238.324219 958.289062 C 232.59375 948.230469 219.980469 944.558594 209.726562 949.941406 L 107.386719 1003.828125 C 105.679688 1004.730469 104.171875 1005.839844 102.8125 1007.089844 C 100.902344 1008.800781 99.34375 1010.808594 98.191406 1013.019531 C 96.835938 1015.539062 95.988281 1018.289062 95.671875 1021.109375 C 96.054688 1020.019531 96.492188 1018.949219 97.042969 1017.929688 "/>
</clipPath>
<linearGradient id="linear-pattern-15" gradientUnits="userSpaceOnUse" x1="-0.000000152485" y1="0" x2="1.001635" y2="0" gradientTransform="matrix(655.803, 0, 0, 655.803, 95.6728, 1069.87)">
<stop offset="0" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="0.00390625" stop-color="rgb(99.798584%, 99.838257%, 99.92981%)" stop-opacity="1"/>
<stop offset="0.0078125" stop-color="rgb(99.597168%, 99.676514%, 99.861145%)" stop-opacity="1"/>
<stop offset="0.0117188" stop-color="rgb(99.395752%, 99.514771%, 99.790955%)" stop-opacity="1"/>
<stop offset="0.015625" stop-color="rgb(99.195862%, 99.354553%, 99.72229%)" stop-opacity="1"/>
<stop offset="0.0195312" stop-color="rgb(98.994446%, 99.19281%, 99.653625%)" stop-opacity="1"/>
<stop offset="0.0234375" stop-color="rgb(98.79303%, 99.032593%, 99.584961%)" stop-opacity="1"/>
<stop offset="0.0273438" stop-color="rgb(98.591614%, 98.87085%, 99.514771%)" stop-opacity="1"/>
<stop offset="0.03125" stop-color="rgb(98.391724%, 98.710632%, 99.446106%)" stop-opacity="1"/>
<stop offset="0.0351562" stop-color="rgb(98.190308%, 98.548889%, 99.377441%)" stop-opacity="1"/>
<stop offset="0.0390625" stop-color="rgb(97.988892%, 98.388672%, 99.308777%)" stop-opacity="1"/>
<stop offset="0.0429688" stop-color="rgb(97.787476%, 98.226929%, 99.238586%)" stop-opacity="1"/>
<stop offset="0.046875" stop-color="rgb(97.587585%, 98.065186%, 99.169922%)" stop-opacity="1"/>
<stop offset="0.0507813" stop-color="rgb(97.386169%, 97.903442%, 99.101257%)" stop-opacity="1"/>
<stop offset="0.0546875" stop-color="rgb(97.184753%, 97.743225%, 99.032593%)" stop-opacity="1"/>
<stop offset="0.0585938" stop-color="rgb(96.983337%, 97.581482%, 98.962402%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(96.783447%, 97.421265%, 98.893738%)" stop-opacity="1"/>
<stop offset="0.0664062" stop-color="rgb(96.582031%, 97.259521%, 98.825073%)" stop-opacity="1"/>
<stop offset="0.0703125" stop-color="rgb(96.380615%, 97.099304%, 98.756409%)" stop-opacity="1"/>
<stop offset="0.0742188" stop-color="rgb(96.179199%, 96.937561%, 98.686218%)" stop-opacity="1"/>
<stop offset="0.078125" stop-color="rgb(95.979309%, 96.777344%, 98.617554%)" stop-opacity="1"/>
<stop offset="0.0820312" stop-color="rgb(95.777893%, 96.615601%, 98.548889%)" stop-opacity="1"/>
<stop offset="0.0859375" stop-color="rgb(95.576477%, 96.455383%, 98.480225%)" stop-opacity="1"/>
<stop offset="0.0898438" stop-color="rgb(95.375061%, 96.29364%, 98.410034%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(95.175171%, 96.131897%, 98.34137%)" stop-opacity="1"/>
<stop offset="0.0976563" stop-color="rgb(94.973755%, 95.970154%, 98.272705%)" stop-opacity="1"/>
<stop offset="0.101563" stop-color="rgb(94.773865%, 95.809937%, 98.204041%)" stop-opacity="1"/>
<stop offset="0.105469" stop-color="rgb(94.572449%, 95.648193%, 98.13385%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(94.371033%, 95.487976%, 98.065186%)" stop-opacity="1"/>
<stop offset="0.113281" stop-color="rgb(94.169617%, 95.326233%, 97.996521%)" stop-opacity="1"/>
<stop offset="0.117188" stop-color="rgb(93.969727%, 95.166016%, 97.927856%)" stop-opacity="1"/>
<stop offset="0.121094" stop-color="rgb(93.768311%, 95.004272%, 97.857666%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(93.566895%, 94.844055%, 97.789001%)" stop-opacity="1"/>
<stop offset="0.128906" stop-color="rgb(93.365479%, 94.682312%, 97.720337%)" stop-opacity="1"/>
<stop offset="0.132812" stop-color="rgb(93.165588%, 94.522095%, 97.651672%)" stop-opacity="1"/>
<stop offset="0.136719" stop-color="rgb(92.964172%, 94.360352%, 97.581482%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(92.762756%, 94.198608%, 97.512817%)" stop-opacity="1"/>
<stop offset="0.144531" stop-color="rgb(92.56134%, 94.036865%, 97.444153%)" stop-opacity="1"/>
<stop offset="0.148438" stop-color="rgb(92.36145%, 93.876648%, 97.375488%)" stop-opacity="1"/>
<stop offset="0.152344" stop-color="rgb(92.160034%, 93.714905%, 97.305298%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(91.958618%, 93.554688%, 97.236633%)" stop-opacity="1"/>
<stop offset="0.160156" stop-color="rgb(91.757202%, 93.392944%, 97.167969%)" stop-opacity="1"/>
<stop offset="0.164062" stop-color="rgb(91.557312%, 93.232727%, 97.099304%)" stop-opacity="1"/>
<stop offset="0.167969" stop-color="rgb(91.355896%, 93.070984%, 97.029114%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(91.15448%, 92.910767%, 96.960449%)" stop-opacity="1"/>
<stop offset="0.175781" stop-color="rgb(90.953064%, 92.749023%, 96.891785%)" stop-opacity="1"/>
<stop offset="0.179688" stop-color="rgb(90.753174%, 92.588806%, 96.82312%)" stop-opacity="1"/>
<stop offset="0.183594" stop-color="rgb(90.551758%, 92.427063%, 96.75293%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(90.351868%, 92.26532%, 96.684265%)" stop-opacity="1"/>
<stop offset="0.191406" stop-color="rgb(90.150452%, 92.103577%, 96.615601%)" stop-opacity="1"/>
<stop offset="0.195313" stop-color="rgb(89.949036%, 91.943359%, 96.546936%)" stop-opacity="1"/>
<stop offset="0.199219" stop-color="rgb(89.74762%, 91.781616%, 96.476746%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(89.547729%, 91.621399%, 96.408081%)" stop-opacity="1"/>
<stop offset="0.207031" stop-color="rgb(89.346313%, 91.459656%, 96.339417%)" stop-opacity="1"/>
<stop offset="0.210938" stop-color="rgb(89.144897%, 91.299438%, 96.270752%)" stop-opacity="1"/>
<stop offset="0.214844" stop-color="rgb(88.943481%, 91.137695%, 96.200562%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(88.743591%, 90.977478%, 96.131897%)" stop-opacity="1"/>
<stop offset="0.222656" stop-color="rgb(88.542175%, 90.815735%, 96.063232%)" stop-opacity="1"/>
<stop offset="0.226562" stop-color="rgb(88.340759%, 90.655518%, 95.994568%)" stop-opacity="1"/>
<stop offset="0.230469" stop-color="rgb(88.139343%, 90.493774%, 95.924377%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(87.939453%, 90.332031%, 95.855713%)" stop-opacity="1"/>
<stop offset="0.238281" stop-color="rgb(87.738037%, 90.170288%, 95.787048%)" stop-opacity="1"/>
<stop offset="0.242188" stop-color="rgb(87.536621%, 90.010071%, 95.718384%)" stop-opacity="1"/>
<stop offset="0.246094" stop-color="rgb(87.335205%, 89.848328%, 95.648193%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(87.135315%, 89.68811%, 95.579529%)" stop-opacity="1"/>
<stop offset="0.253906" stop-color="rgb(86.933899%, 89.526367%, 95.510864%)" stop-opacity="1"/>
<stop offset="0.257812" stop-color="rgb(86.732483%, 89.36615%, 95.4422%)" stop-opacity="1"/>
<stop offset="0.261719" stop-color="rgb(86.531067%, 89.204407%, 95.372009%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(86.331177%, 89.044189%, 95.303345%)" stop-opacity="1"/>
<stop offset="0.269531" stop-color="rgb(86.129761%, 88.882446%, 95.23468%)" stop-opacity="1"/>
<stop offset="0.273437" stop-color="rgb(85.929871%, 88.722229%, 95.166016%)" stop-opacity="1"/>
<stop offset="0.277344" stop-color="rgb(85.728455%, 88.560486%, 95.095825%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(85.527039%, 88.398743%, 95.027161%)" stop-opacity="1"/>
<stop offset="0.285156" stop-color="rgb(85.325623%, 88.237%, 94.958496%)" stop-opacity="1"/>
<stop offset="0.289062" stop-color="rgb(85.125732%, 88.076782%, 94.889832%)" stop-opacity="1"/>
<stop offset="0.292969" stop-color="rgb(84.924316%, 87.915039%, 94.819641%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(84.7229%, 87.754822%, 94.750977%)" stop-opacity="1"/>
<stop offset="0.300781" stop-color="rgb(84.521484%, 87.593079%, 94.682312%)" stop-opacity="1"/>
<stop offset="0.304688" stop-color="rgb(84.321594%, 87.432861%, 94.613647%)" stop-opacity="1"/>
<stop offset="0.308594" stop-color="rgb(84.120178%, 87.271118%, 94.543457%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(83.918762%, 87.110901%, 94.474792%)" stop-opacity="1"/>
<stop offset="0.316406" stop-color="rgb(83.717346%, 86.949158%, 94.406128%)" stop-opacity="1"/>
<stop offset="0.320312" stop-color="rgb(83.517456%, 86.78894%, 94.337463%)" stop-opacity="1"/>
<stop offset="0.324219" stop-color="rgb(83.31604%, 86.627197%, 94.267273%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(83.114624%, 86.465454%, 94.198608%)" stop-opacity="1"/>
<stop offset="0.332031" stop-color="rgb(82.913208%, 86.303711%, 94.129944%)" stop-opacity="1"/>
<stop offset="0.335938" stop-color="rgb(82.713318%, 86.143494%, 94.061279%)" stop-opacity="1"/>
<stop offset="0.339844" stop-color="rgb(82.511902%, 85.98175%, 93.991089%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(82.310486%, 85.821533%, 93.922424%)" stop-opacity="1"/>
<stop offset="0.347656" stop-color="rgb(82.10907%, 85.65979%, 93.85376%)" stop-opacity="1"/>
<stop offset="0.351562" stop-color="rgb(81.90918%, 85.499573%, 93.785095%)" stop-opacity="1"/>
<stop offset="0.355469" stop-color="rgb(81.707764%, 85.33783%, 93.714905%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(81.506348%, 85.177612%, 93.64624%)" stop-opacity="1"/>
<stop offset="0.363281" stop-color="rgb(81.304932%, 85.015869%, 93.577576%)" stop-opacity="1"/>
<stop offset="0.367188" stop-color="rgb(81.105042%, 84.855652%, 93.508911%)" stop-opacity="1"/>
<stop offset="0.371094" stop-color="rgb(80.903625%, 84.693909%, 93.438721%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(80.703735%, 84.532166%, 93.370056%)" stop-opacity="1"/>
<stop offset="0.378906" stop-color="rgb(80.502319%, 84.370422%, 93.301392%)" stop-opacity="1"/>
<stop offset="0.382813" stop-color="rgb(80.300903%, 84.210205%, 93.232727%)" stop-opacity="1"/>
<stop offset="0.386719" stop-color="rgb(80.099487%, 84.048462%, 93.162537%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(79.899597%, 83.888245%, 93.093872%)" stop-opacity="1"/>
<stop offset="0.394531" stop-color="rgb(79.698181%, 83.726501%, 93.025208%)" stop-opacity="1"/>
<stop offset="0.398438" stop-color="rgb(79.496765%, 83.566284%, 92.956543%)" stop-opacity="1"/>
<stop offset="0.402344" stop-color="rgb(79.295349%, 83.404541%, 92.886353%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(79.095459%, 83.244324%, 92.817688%)" stop-opacity="1"/>
<stop offset="0.410156" stop-color="rgb(78.894043%, 83.082581%, 92.749023%)" stop-opacity="1"/>
<stop offset="0.414063" stop-color="rgb(78.692627%, 82.922363%, 92.680359%)" stop-opacity="1"/>
<stop offset="0.417969" stop-color="rgb(78.491211%, 82.76062%, 92.610168%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(78.291321%, 82.598877%, 92.541504%)" stop-opacity="1"/>
<stop offset="0.425781" stop-color="rgb(78.089905%, 82.437134%, 92.472839%)" stop-opacity="1"/>
<stop offset="0.429688" stop-color="rgb(77.888489%, 82.276917%, 92.404175%)" stop-opacity="1"/>
<stop offset="0.433594" stop-color="rgb(77.687073%, 82.115173%, 92.333984%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(77.487183%, 81.954956%, 92.26532%)" stop-opacity="1"/>
<stop offset="0.441406" stop-color="rgb(77.285767%, 81.793213%, 92.196655%)" stop-opacity="1"/>
<stop offset="0.445312" stop-color="rgb(77.084351%, 81.632996%, 92.127991%)" stop-opacity="1"/>
<stop offset="0.449219" stop-color="rgb(76.882935%, 81.471252%, 92.0578%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(76.683044%, 81.311035%, 91.989136%)" stop-opacity="1"/>
<stop offset="0.457031" stop-color="rgb(76.481628%, 81.149292%, 91.920471%)" stop-opacity="1"/>
<stop offset="0.460938" stop-color="rgb(76.281738%, 80.989075%, 91.851807%)" stop-opacity="1"/>
<stop offset="0.464844" stop-color="rgb(76.080322%, 80.827332%, 91.781616%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(75.878906%, 80.665588%, 91.712952%)" stop-opacity="1"/>
<stop offset="0.472656" stop-color="rgb(75.67749%, 80.503845%, 91.644287%)" stop-opacity="1"/>
<stop offset="0.476562" stop-color="rgb(75.4776%, 80.343628%, 91.575623%)" stop-opacity="1"/>
<stop offset="0.480469" stop-color="rgb(75.276184%, 80.181885%, 91.505432%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(75.074768%, 80.021667%, 91.436768%)" stop-opacity="1"/>
<stop offset="0.488281" stop-color="rgb(74.873352%, 79.859924%, 91.368103%)" stop-opacity="1"/>
<stop offset="0.492188" stop-color="rgb(74.673462%, 79.699707%, 91.299438%)" stop-opacity="1"/>
<stop offset="0.496094" stop-color="rgb(74.472046%, 79.537964%, 91.229248%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(74.27063%, 79.377747%, 91.160583%)" stop-opacity="1"/>
<stop offset="0.503906" stop-color="rgb(74.069214%, 79.216003%, 91.091919%)" stop-opacity="1"/>
<stop offset="0.507813" stop-color="rgb(73.869324%, 79.055786%, 91.023254%)" stop-opacity="1"/>
<stop offset="0.511719" stop-color="rgb(73.667908%, 78.894043%, 90.953064%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(73.466492%, 78.7323%, 90.884399%)" stop-opacity="1"/>
<stop offset="0.519531" stop-color="rgb(73.265076%, 78.570557%, 90.815735%)" stop-opacity="1"/>
<stop offset="0.523438" stop-color="rgb(73.065186%, 78.410339%, 90.74707%)" stop-opacity="1"/>
<stop offset="0.527344" stop-color="rgb(72.86377%, 78.248596%, 90.67688%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(72.662354%, 78.088379%, 90.608215%)" stop-opacity="1"/>
<stop offset="0.535156" stop-color="rgb(72.460938%, 77.926636%, 90.539551%)" stop-opacity="1"/>
<stop offset="0.539062" stop-color="rgb(72.261047%, 77.766418%, 90.470886%)" stop-opacity="1"/>
<stop offset="0.542969" stop-color="rgb(72.059631%, 77.604675%, 90.400696%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(71.858215%, 77.444458%, 90.332031%)" stop-opacity="1"/>
<stop offset="0.550781" stop-color="rgb(71.656799%, 77.282715%, 90.263367%)" stop-opacity="1"/>
<stop offset="0.554688" stop-color="rgb(71.456909%, 77.122498%, 90.194702%)" stop-opacity="1"/>
<stop offset="0.558594" stop-color="rgb(71.255493%, 76.960754%, 90.124512%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(71.055603%, 76.799011%, 90.055847%)" stop-opacity="1"/>
<stop offset="0.566406" stop-color="rgb(70.854187%, 76.637268%, 89.987183%)" stop-opacity="1"/>
<stop offset="0.570312" stop-color="rgb(70.652771%, 76.477051%, 89.918518%)" stop-opacity="1"/>
<stop offset="0.574219" stop-color="rgb(70.451355%, 76.315308%, 89.848328%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(70.251465%, 76.15509%, 89.779663%)" stop-opacity="1"/>
<stop offset="0.582031" stop-color="rgb(70.050049%, 75.993347%, 89.710999%)" stop-opacity="1"/>
<stop offset="0.585938" stop-color="rgb(69.848633%, 75.83313%, 89.642334%)" stop-opacity="1"/>
<stop offset="0.589844" stop-color="rgb(69.647217%, 75.671387%, 89.572144%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(69.447327%, 75.511169%, 89.503479%)" stop-opacity="1"/>
<stop offset="0.597656" stop-color="rgb(69.245911%, 75.349426%, 89.434814%)" stop-opacity="1"/>
<stop offset="0.601562" stop-color="rgb(69.044495%, 75.189209%, 89.36615%)" stop-opacity="1"/>
<stop offset="0.605469" stop-color="rgb(68.843079%, 75.027466%, 89.295959%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(68.643188%, 74.865723%, 89.227295%)" stop-opacity="1"/>
<stop offset="0.613281" stop-color="rgb(68.441772%, 74.703979%, 89.15863%)" stop-opacity="1"/>
<stop offset="0.617188" stop-color="rgb(68.240356%, 74.543762%, 89.089966%)" stop-opacity="1"/>
<stop offset="0.621094" stop-color="rgb(68.03894%, 74.382019%, 89.019775%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(67.83905%, 74.221802%, 88.951111%)" stop-opacity="1"/>
<stop offset="0.628906" stop-color="rgb(67.637634%, 74.060059%, 88.882446%)" stop-opacity="1"/>
<stop offset="0.632812" stop-color="rgb(67.436218%, 73.899841%, 88.813782%)" stop-opacity="1"/>
<stop offset="0.636719" stop-color="rgb(67.234802%, 73.738098%, 88.743591%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(67.034912%, 73.577881%, 88.674927%)" stop-opacity="1"/>
<stop offset="0.644531" stop-color="rgb(66.833496%, 73.416138%, 88.606262%)" stop-opacity="1"/>
<stop offset="0.648438" stop-color="rgb(66.633606%, 73.25592%, 88.537598%)" stop-opacity="1"/>
<stop offset="0.652344" stop-color="rgb(66.43219%, 73.094177%, 88.467407%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(66.230774%, 72.932434%, 88.398743%)" stop-opacity="1"/>
<stop offset="0.660156" stop-color="rgb(66.029358%, 72.770691%, 88.330078%)" stop-opacity="1"/>
<stop offset="0.664062" stop-color="rgb(65.829468%, 72.610474%, 88.261414%)" stop-opacity="1"/>
<stop offset="0.667969" stop-color="rgb(65.628052%, 72.44873%, 88.191223%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(65.426636%, 72.288513%, 88.122559%)" stop-opacity="1"/>
<stop offset="0.675781" stop-color="rgb(65.22522%, 72.12677%, 88.053894%)" stop-opacity="1"/>
<stop offset="0.679688" stop-color="rgb(65.02533%, 71.966553%, 87.985229%)" stop-opacity="1"/>
<stop offset="0.683594" stop-color="rgb(64.823914%, 71.80481%, 87.915039%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(64.622498%, 71.644592%, 87.846375%)" stop-opacity="1"/>
<stop offset="0.691406" stop-color="rgb(64.421082%, 71.482849%, 87.77771%)" stop-opacity="1"/>
<stop offset="0.695312" stop-color="rgb(64.221191%, 71.322632%, 87.709045%)" stop-opacity="1"/>
<stop offset="0.699219" stop-color="rgb(64.019775%, 71.160889%, 87.638855%)" stop-opacity="1"/>
<stop offset="0.703125" stop-color="rgb(63.818359%, 70.999146%, 87.57019%)" stop-opacity="1"/>
<stop offset="0.707031" stop-color="rgb(63.616943%, 70.837402%, 87.501526%)" stop-opacity="1"/>
<stop offset="0.710938" stop-color="rgb(63.417053%, 70.677185%, 87.432861%)" stop-opacity="1"/>
<stop offset="0.714844" stop-color="rgb(63.215637%, 70.515442%, 87.362671%)" stop-opacity="1"/>
<stop offset="0.71875" stop-color="rgb(63.014221%, 70.355225%, 87.294006%)" stop-opacity="1"/>
<stop offset="0.722656" stop-color="rgb(62.812805%, 70.193481%, 87.225342%)" stop-opacity="1"/>
<stop offset="0.726562" stop-color="rgb(62.612915%, 70.033264%, 87.156677%)" stop-opacity="1"/>
<stop offset="0.730469" stop-color="rgb(62.411499%, 69.871521%, 87.086487%)" stop-opacity="1"/>
<stop offset="0.734375" stop-color="rgb(62.211609%, 69.711304%, 87.017822%)" stop-opacity="1"/>
<stop offset="0.738281" stop-color="rgb(62.010193%, 69.549561%, 86.949158%)" stop-opacity="1"/>
<stop offset="0.742188" stop-color="rgb(61.808777%, 69.389343%, 86.880493%)" stop-opacity="1"/>
<stop offset="0.746094" stop-color="rgb(61.607361%, 69.2276%, 86.810303%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(61.407471%, 69.065857%, 86.741638%)" stop-opacity="1"/>
<stop offset="0.753906" stop-color="rgb(61.206055%, 68.904114%, 86.672974%)" stop-opacity="1"/>
<stop offset="0.757812" stop-color="rgb(61.004639%, 68.743896%, 86.604309%)" stop-opacity="1"/>
<stop offset="0.761719" stop-color="rgb(60.803223%, 68.582153%, 86.534119%)" stop-opacity="1"/>
<stop offset="0.765625" stop-color="rgb(60.603333%, 68.421936%, 86.465454%)" stop-opacity="1"/>
<stop offset="0.769531" stop-color="rgb(60.401917%, 68.260193%, 86.39679%)" stop-opacity="1"/>
<stop offset="0.773438" stop-color="rgb(60.2005%, 68.099976%, 86.328125%)" stop-opacity="1"/>
<stop offset="0.777344" stop-color="rgb(59.999084%, 67.938232%, 86.257935%)" stop-opacity="1"/>
<stop offset="0.78125" stop-color="rgb(59.799194%, 67.778015%, 86.18927%)" stop-opacity="1"/>
<stop offset="0.785156" stop-color="rgb(59.597778%, 67.616272%, 86.120605%)" stop-opacity="1"/>
<stop offset="0.789062" stop-color="rgb(59.396362%, 67.454529%, 86.051941%)" stop-opacity="1"/>
<stop offset="0.792969" stop-color="rgb(59.194946%, 67.292786%, 85.98175%)" stop-opacity="1"/>
<stop offset="0.796875" stop-color="rgb(58.995056%, 67.132568%, 85.913086%)" stop-opacity="1"/>
<stop offset="0.800781" stop-color="rgb(58.79364%, 66.970825%, 85.844421%)" stop-opacity="1"/>
<stop offset="0.804688" stop-color="rgb(58.592224%, 66.810608%, 85.775757%)" stop-opacity="1"/>
<stop offset="0.808594" stop-color="rgb(58.390808%, 66.648865%, 85.705566%)" stop-opacity="1"/>
<stop offset="0.8125" stop-color="rgb(58.190918%, 66.488647%, 85.636902%)" stop-opacity="1"/>
<stop offset="0.816406" stop-color="rgb(57.989502%, 66.326904%, 85.568237%)" stop-opacity="1"/>
<stop offset="0.820312" stop-color="rgb(57.788086%, 66.166687%, 85.499573%)" stop-opacity="1"/>
<stop offset="0.824219" stop-color="rgb(57.58667%, 66.004944%, 85.429382%)" stop-opacity="1"/>
<stop offset="0.828125" stop-color="rgb(57.38678%, 65.844727%, 85.360718%)" stop-opacity="1"/>
<stop offset="0.832031" stop-color="rgb(57.185364%, 65.682983%, 85.292053%)" stop-opacity="1"/>
<stop offset="0.835938" stop-color="rgb(56.985474%, 65.52124%, 85.223389%)" stop-opacity="1"/>
<stop offset="0.839844" stop-color="rgb(56.784058%, 65.359497%, 85.153198%)" stop-opacity="1"/>
<stop offset="0.84375" stop-color="rgb(56.582642%, 65.19928%, 85.084534%)" stop-opacity="1"/>
<stop offset="0.847656" stop-color="rgb(56.381226%, 65.037537%, 85.015869%)" stop-opacity="1"/>
<stop offset="0.851563" stop-color="rgb(56.181335%, 64.877319%, 84.947205%)" stop-opacity="1"/>
<stop offset="0.855469" stop-color="rgb(55.979919%, 64.715576%, 84.877014%)" stop-opacity="1"/>
<stop offset="0.859375" stop-color="rgb(55.778503%, 64.555359%, 84.80835%)" stop-opacity="1"/>
<stop offset="0.863281" stop-color="rgb(55.577087%, 64.393616%, 84.739685%)" stop-opacity="1"/>
<stop offset="0.867188" stop-color="rgb(55.377197%, 64.233398%, 84.671021%)" stop-opacity="1"/>
<stop offset="0.871094" stop-color="rgb(55.175781%, 64.071655%, 84.60083%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(54.974365%, 63.911438%, 84.532166%)" stop-opacity="1"/>
<stop offset="0.878906" stop-color="rgb(54.772949%, 63.749695%, 84.463501%)" stop-opacity="1"/>
<stop offset="0.882813" stop-color="rgb(54.573059%, 63.587952%, 84.394836%)" stop-opacity="1"/>
<stop offset="0.886719" stop-color="rgb(54.371643%, 63.426208%, 84.324646%)" stop-opacity="1"/>
<stop offset="0.890625" stop-color="rgb(54.170227%, 63.265991%, 84.255981%)" stop-opacity="1"/>
<stop offset="0.894531" stop-color="rgb(53.968811%, 63.104248%, 84.187317%)" stop-opacity="1"/>
<stop offset="0.898438" stop-color="rgb(53.768921%, 62.944031%, 84.118652%)" stop-opacity="1"/>
<stop offset="0.902344" stop-color="rgb(53.567505%, 62.782288%, 84.048462%)" stop-opacity="1"/>
<stop offset="0.90625" stop-color="rgb(53.366089%, 62.62207%, 83.979797%)" stop-opacity="1"/>
<stop offset="0.910156" stop-color="rgb(53.164673%, 62.460327%, 83.911133%)" stop-opacity="1"/>
<stop offset="0.914062" stop-color="rgb(52.964783%, 62.30011%, 83.842468%)" stop-opacity="1"/>
<stop offset="0.917969" stop-color="rgb(52.763367%, 62.138367%, 83.772278%)" stop-opacity="1"/>
<stop offset="0.921875" stop-color="rgb(52.563477%, 61.978149%, 83.703613%)" stop-opacity="1"/>
<stop offset="0.925781" stop-color="rgb(52.362061%, 61.816406%, 83.634949%)" stop-opacity="1"/>
<stop offset="0.929688" stop-color="rgb(52.160645%, 61.654663%, 83.566284%)" stop-opacity="1"/>
<stop offset="0.933594" stop-color="rgb(51.959229%, 61.49292%, 83.496094%)" stop-opacity="1"/>
<stop offset="0.9375" stop-color="rgb(51.759338%, 61.332703%, 83.427429%)" stop-opacity="1"/>
<stop offset="0.941406" stop-color="rgb(51.557922%, 61.170959%, 83.358765%)" stop-opacity="1"/>
<stop offset="0.945312" stop-color="rgb(51.356506%, 61.010742%, 83.2901%)" stop-opacity="1"/>
<stop offset="0.949219" stop-color="rgb(51.15509%, 60.848999%, 83.21991%)" stop-opacity="1"/>
<stop offset="0.953125" stop-color="rgb(50.9552%, 60.688782%, 83.151245%)" stop-opacity="1"/>
<stop offset="0.957031" stop-color="rgb(50.753784%, 60.527039%, 83.082581%)" stop-opacity="1"/>
<stop offset="0.960938" stop-color="rgb(50.552368%, 60.366821%, 83.013916%)" stop-opacity="1"/>
<stop offset="0.964844" stop-color="rgb(50.350952%, 60.205078%, 82.943726%)" stop-opacity="1"/>
<stop offset="0.96875" stop-color="rgb(50.151062%, 60.044861%, 82.875061%)" stop-opacity="1"/>
<stop offset="0.972656" stop-color="rgb(49.949646%, 59.883118%, 82.806396%)" stop-opacity="1"/>
<stop offset="0.976562" stop-color="rgb(49.74823%, 59.721375%, 82.737732%)" stop-opacity="1"/>
<stop offset="0.980469" stop-color="rgb(49.546814%, 59.559631%, 82.667542%)" stop-opacity="1"/>
<stop offset="0.984375" stop-color="rgb(49.346924%, 59.399414%, 82.598877%)" stop-opacity="1"/>
<stop offset="0.988281" stop-color="rgb(49.145508%, 59.237671%, 82.530212%)" stop-opacity="1"/>
<stop offset="0.992188" stop-color="rgb(48.944092%, 59.077454%, 82.461548%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(48.7854%, 58.94928%, 82.406616%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-63">
<path clip-rule="nonzero" d="M 2014 605 L 2368 605 L 2368 1234 L 2014 1234 Z M 2014 605 "/>
</clipPath>
<clipPath id="clip-64">
<path clip-rule="nonzero" d="M 2347.628906 1233.011719 C 2346.839844 1233.011719 2346.03125 1232.960938 2345.230469 1232.859375 C 2343.75 1232.671875 2342.261719 1232.300781 2340.800781 1231.738281 C 2339.828125 1231.371094 2338.859375 1230.910156 2337.910156 1230.351562 L 2336.738281 1229.660156 L 2332.691406 1227.261719 L 2179.328125 1136.328125 L 2175.289062 1133.941406 L 2171.351562 1131.601562 L 2167.46875 1129.300781 L 2163.671875 1127.050781 L 2159.921875 1124.820312 L 2156.21875 1122.628906 L 2152.570312 1120.46875 L 2148.960938 1118.328125 L 2145.390625 1116.210938 L 2141.851562 1114.109375 L 2138.339844 1112.03125 L 2134.871094 1109.96875 L 2131.410156 1107.921875 L 2127.988281 1105.890625 L 2124.578125 1103.871094 L 2121.191406 1101.859375 L 2117.828125 1099.871094 L 2114.480469 1097.878906 L 2111.140625 1095.898438 L 2107.828125 1093.941406 L 2104.519531 1091.980469 L 2101.238281 1090.03125 L 2097.960938 1088.089844 L 2094.691406 1086.148438 L 2091.441406 1084.21875 L 2088.199219 1082.300781 L 2084.960938 1080.378906 L 2029.609375 1047.558594 C 2020.230469 1042.101562 2014.460938 1032.070312 2014.460938 1021.210938 L 2014.460938 620.601562 C 2014.460938 618.488281 2014.878906 616.539062 2015.621094 614.78125 C 2017.949219 609.21875 2023.511719 605.621094 2029.441406 605.621094 C 2031.941406 605.621094 2034.519531 606.269531 2036.941406 607.679688 L 2349.628906 793.019531 C 2360.410156 799.300781 2367.039062 810.828125 2367.039062 823.308594 L 2367.039062 1213.609375 C 2367.039062 1220.71875 2363.371094 1226.550781 2358.140625 1229.910156 C 2357.359375 1230.410156 2356.539062 1230.859375 2355.699219 1231.238281 C 2353.949219 1232.039062 2352.078125 1232.589844 2350.148438 1232.851562 C 2349.320312 1232.960938 2348.480469 1233.011719 2347.628906 1233.011719 M 2092.859375 947.5 L 2092.859375 969.691406 L 2288.640625 1083.179688 L 2288.640625 1060.988281 L 2092.859375 947.5 M 2092.859375 883.488281 L 2092.859375 905.679688 L 2288.640625 1019.160156 L 2288.640625 996.980469 L 2092.859375 883.488281 M 2092.859375 819.46875 L 2092.859375 841.660156 L 2288.640625 955.148438 L 2288.640625 932.960938 L 2092.859375 819.46875 M 2092.859375 755.460938 L 2092.859375 777.648438 L 2288.640625 891.949219 L 2288.640625 869.761719 L 2092.859375 755.460938 "/>
</clipPath>
<clipPath id="clip-65">
<path clip-rule="nonzero" d="M 2347.628906 1233.011719 C 2346.839844 1233.011719 2346.03125 1232.960938 2345.230469 1232.859375 C 2343.75 1232.671875 2342.261719 1232.300781 2340.800781 1231.738281 C 2339.828125 1231.371094 2338.859375 1230.910156 2337.910156 1230.351562 L 2336.738281 1229.660156 L 2332.691406 1227.261719 L 2179.328125 1136.328125 L 2175.289062 1133.941406 L 2171.351562 1131.601562 L 2167.46875 1129.300781 L 2163.671875 1127.050781 L 2159.921875 1124.820312 L 2156.21875 1122.628906 L 2152.570312 1120.46875 L 2148.960938 1118.328125 L 2145.390625 1116.210938 L 2141.851562 1114.109375 L 2138.339844 1112.03125 L 2134.871094 1109.96875 L 2131.410156 1107.921875 L 2127.988281 1105.890625 L 2124.578125 1103.871094 L 2121.191406 1101.859375 L 2117.828125 1099.871094 L 2114.480469 1097.878906 L 2111.140625 1095.898438 L 2107.828125 1093.941406 L 2104.519531 1091.980469 L 2101.238281 1090.03125 L 2097.960938 1088.089844 L 2094.691406 1086.148438 L 2091.441406 1084.21875 L 2088.199219 1082.300781 L 2084.960938 1080.378906 L 2029.609375 1047.558594 C 2020.230469 1042.101562 2014.460938 1032.070312 2014.460938 1021.210938 L 2014.460938 620.601562 C 2014.460938 618.488281 2014.878906 616.539062 2015.621094 614.78125 C 2017.949219 609.21875 2023.511719 605.621094 2029.441406 605.621094 C 2031.941406 605.621094 2034.519531 606.269531 2036.941406 607.679688 L 2349.628906 793.019531 C 2360.410156 799.300781 2367.039062 810.828125 2367.039062 823.308594 L 2367.039062 1213.609375 C 2367.039062 1220.71875 2363.371094 1226.550781 2358.140625 1229.910156 C 2357.359375 1230.410156 2356.539062 1230.859375 2355.699219 1231.238281 C 2353.949219 1232.039062 2352.078125 1232.589844 2350.148438 1232.851562 C 2349.320312 1232.960938 2348.480469 1233.011719 2347.628906 1233.011719 M 2092.859375 947.5 L 2092.859375 969.691406 L 2288.640625 1083.179688 L 2288.640625 1060.988281 L 2092.859375 947.5 M 2092.859375 883.488281 L 2092.859375 905.679688 L 2288.640625 1019.160156 L 2288.640625 996.980469 L 2092.859375 883.488281 M 2092.859375 819.46875 L 2092.859375 841.660156 L 2288.640625 955.148438 L 2288.640625 932.960938 L 2092.859375 819.46875 M 2092.859375 755.460938 L 2092.859375 777.648438 L 2288.640625 891.949219 L 2288.640625 869.761719 L 2092.859375 755.460938 "/>
</clipPath>
<linearGradient id="linear-pattern-16" gradientUnits="userSpaceOnUse" x1="-0.178108" y1="0" x2="1.540931" y2="0" gradientTransform="matrix(-130, 390, -390, -130, 2278.78, 653.38)">
<stop offset="0" stop-color="rgb(85.488892%, 85.881042%, 87.449646%)" stop-opacity="1"/>
<stop offset="0.0625" stop-color="rgb(85.488892%, 85.881042%, 87.449646%)" stop-opacity="1"/>
<stop offset="0.09375" stop-color="rgb(85.488892%, 85.881042%, 87.449646%)" stop-opacity="1"/>
<stop offset="0.109375" stop-color="rgb(85.560608%, 85.951233%, 87.512207%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(85.827637%, 86.210632%, 87.742615%)" stop-opacity="1"/>
<stop offset="0.140625" stop-color="rgb(86.216736%, 86.590576%, 88.079834%)" stop-opacity="1"/>
<stop offset="0.15625" stop-color="rgb(86.607361%, 86.968994%, 88.417053%)" stop-opacity="1"/>
<stop offset="0.171875" stop-color="rgb(86.99646%, 87.348938%, 88.754272%)" stop-opacity="1"/>
<stop offset="0.1875" stop-color="rgb(87.387085%, 87.727356%, 89.091492%)" stop-opacity="1"/>
<stop offset="0.203125" stop-color="rgb(87.776184%, 88.1073%, 89.427185%)" stop-opacity="1"/>
<stop offset="0.21875" stop-color="rgb(88.166809%, 88.485718%, 89.764404%)" stop-opacity="1"/>
<stop offset="0.234375" stop-color="rgb(88.555908%, 88.865662%, 90.101624%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(88.945007%, 89.24408%, 90.438843%)" stop-opacity="1"/>
<stop offset="0.265625" stop-color="rgb(89.335632%, 89.624023%, 90.776062%)" stop-opacity="1"/>
<stop offset="0.28125" stop-color="rgb(89.724731%, 90.002441%, 91.113281%)" stop-opacity="1"/>
<stop offset="0.296875" stop-color="rgb(90.115356%, 90.382385%, 91.4505%)" stop-opacity="1"/>
<stop offset="0.3125" stop-color="rgb(90.504456%, 90.760803%, 91.78772%)" stop-opacity="1"/>
<stop offset="0.328125" stop-color="rgb(90.893555%, 91.140747%, 92.124939%)" stop-opacity="1"/>
<stop offset="0.34375" stop-color="rgb(91.28418%, 91.519165%, 92.460632%)" stop-opacity="1"/>
<stop offset="0.359375" stop-color="rgb(91.673279%, 91.899109%, 92.797852%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(92.063904%, 92.277527%, 93.135071%)" stop-opacity="1"/>
<stop offset="0.390625" stop-color="rgb(92.453003%, 92.657471%, 93.47229%)" stop-opacity="1"/>
<stop offset="0.40625" stop-color="rgb(92.843628%, 93.035889%, 93.809509%)" stop-opacity="1"/>
<stop offset="0.421875" stop-color="rgb(93.232727%, 93.415833%, 94.146729%)" stop-opacity="1"/>
<stop offset="0.4375" stop-color="rgb(93.621826%, 93.79425%, 94.483948%)" stop-opacity="1"/>
<stop offset="0.453125" stop-color="rgb(94.012451%, 94.174194%, 94.821167%)" stop-opacity="1"/>
<stop offset="0.46875" stop-color="rgb(94.40155%, 94.552612%, 95.158386%)" stop-opacity="1"/>
<stop offset="0.484375" stop-color="rgb(94.792175%, 94.932556%, 95.495605%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(95.181274%, 95.310974%, 95.831299%)" stop-opacity="1"/>
<stop offset="0.515625" stop-color="rgb(95.570374%, 95.690918%, 96.168518%)" stop-opacity="1"/>
<stop offset="0.53125" stop-color="rgb(95.960999%, 96.069336%, 96.505737%)" stop-opacity="1"/>
<stop offset="0.546875" stop-color="rgb(96.350098%, 96.44928%, 96.842957%)" stop-opacity="1"/>
<stop offset="0.5625" stop-color="rgb(96.740723%, 96.827698%, 97.180176%)" stop-opacity="1"/>
<stop offset="0.578125" stop-color="rgb(97.129822%, 97.207642%, 97.517395%)" stop-opacity="1"/>
<stop offset="0.59375" stop-color="rgb(97.518921%, 97.58606%, 97.854614%)" stop-opacity="1"/>
<stop offset="0.609375" stop-color="rgb(97.909546%, 97.966003%, 98.191833%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(98.298645%, 98.344421%, 98.529053%)" stop-opacity="1"/>
<stop offset="0.640625" stop-color="rgb(98.68927%, 98.724365%, 98.866272%)" stop-opacity="1"/>
<stop offset="0.65625" stop-color="rgb(99.078369%, 99.102783%, 99.201965%)" stop-opacity="1"/>
<stop offset="0.671875" stop-color="rgb(99.468994%, 99.482727%, 99.539185%)" stop-opacity="1"/>
<stop offset="0.6875" stop-color="rgb(99.832153%, 99.836731%, 99.853516%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(100%, 100%, 100%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-66">
<path clip-rule="nonzero" d="M 2092 755 L 2289 755 L 2289 892 L 2092 892 Z M 2092 755 "/>
</clipPath>
<clipPath id="clip-67">
<path clip-rule="nonzero" d="M 2288.640625 891.949219 L 2092.859375 777.648438 L 2092.859375 755.460938 L 2288.640625 869.761719 L 2288.640625 891.949219 "/>
</clipPath>
<clipPath id="clip-68">
<path clip-rule="nonzero" d="M 2288.640625 891.949219 L 2092.859375 777.648438 L 2092.859375 755.460938 L 2288.640625 869.761719 L 2288.640625 891.949219 "/>
</clipPath>
<linearGradient id="linear-pattern-17" gradientUnits="userSpaceOnUse" x1="0.227985" y1="0" x2="0.693562" y2="0" gradientTransform="matrix(-130, 390, -390, -130, 2278.78, 653.38)">
<stop offset="0" stop-color="rgb(72.177124%, 73.353577%, 73.655701%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(72.279358%, 73.455811%, 73.747253%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(72.483826%, 73.660278%, 73.928833%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(72.689819%, 73.866272%, 74.111938%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(72.894287%, 74.07074%, 74.295044%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(73.100281%, 74.276733%, 74.476624%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(73.306274%, 74.482727%, 74.659729%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(73.510742%, 74.687195%, 74.842834%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(73.716736%, 74.893188%, 75.024414%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-69">
<path clip-rule="nonzero" d="M 2092 819 L 2289 819 L 2289 956 L 2092 956 Z M 2092 819 "/>
</clipPath>
<clipPath id="clip-70">
<path clip-rule="nonzero" d="M 2288.640625 955.148438 L 2092.859375 841.660156 L 2092.859375 819.46875 L 2288.640625 932.960938 L 2288.640625 955.148438 "/>
</clipPath>
<clipPath id="clip-71">
<path clip-rule="nonzero" d="M 2288.640625 955.148438 L 2092.859375 841.660156 L 2092.859375 819.46875 L 2288.640625 932.960938 L 2288.640625 955.148438 "/>
</clipPath>
<linearGradient id="linear-pattern-18" gradientUnits="userSpaceOnUse" x1="0.3757" y1="0" x2="0.839408" y2="0" gradientTransform="matrix(-130, 390, -390, -130, 2278.78, 653.38)">
<stop offset="0" stop-color="rgb(72.697449%, 73.873901%, 74.119568%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(72.799683%, 73.976135%, 74.209595%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(73.00415%, 74.180603%, 74.391174%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(73.208618%, 74.385071%, 74.572754%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(73.413086%, 74.589539%, 74.754333%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(73.61908%, 74.795532%, 74.937439%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(73.823547%, 75%, 75.119019%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(74.028015%, 75.204468%, 75.300598%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(74.232483%, 75.408936%, 75.482178%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-72">
<path clip-rule="nonzero" d="M 2092 883 L 2289 883 L 2289 1020 L 2092 1020 Z M 2092 883 "/>
</clipPath>
<clipPath id="clip-73">
<path clip-rule="nonzero" d="M 2288.640625 1019.160156 L 2092.859375 905.679688 L 2092.859375 883.488281 L 2288.640625 996.980469 L 2288.640625 1019.160156 "/>
</clipPath>
<clipPath id="clip-74">
<path clip-rule="nonzero" d="M 2288.640625 1019.160156 L 2092.859375 905.679688 L 2092.859375 883.488281 L 2288.640625 996.980469 L 2288.640625 1019.160156 "/>
</clipPath>
<linearGradient id="linear-pattern-19" gradientUnits="userSpaceOnUse" x1="0.523438" y1="0" x2="0.987123" y2="0" gradientTransform="matrix(-130, 390, -390, -130, 2278.78, 653.38)">
<stop offset="0" stop-color="rgb(73.219299%, 74.395752%, 74.581909%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(73.321533%, 74.497986%, 74.673462%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(73.526001%, 74.702454%, 74.855042%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(73.730469%, 74.906921%, 75.036621%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(73.934937%, 75.111389%, 75.218201%)" stop-opacity="1"/>
<stop offset="0.625" stop-color="rgb(74.139404%, 75.315857%, 75.39978%)" stop-opacity="1"/>
<stop offset="0.75" stop-color="rgb(74.343872%, 75.520325%, 75.58136%)" stop-opacity="1"/>
<stop offset="0.875" stop-color="rgb(74.54834%, 75.724792%, 75.764465%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(74.752808%, 75.92926%, 75.946045%)" stop-opacity="1"/>
</linearGradient>
<clipPath id="clip-75">
<path clip-rule="nonzero" d="M 2092 947 L 2289 947 L 2289 1084 L 2092 1084 Z M 2092 947 "/>
</clipPath>
<clipPath id="clip-76">
<path clip-rule="nonzero" d="M 2288.640625 1083.179688 L 2092.859375 969.691406 L 2092.859375 947.5 L 2288.640625 1060.988281 L 2288.640625 1083.179688 "/>
</clipPath>
<clipPath id="clip-77">
<path clip-rule="nonzero" d="M 2288.640625 1083.179688 L 2092.859375 969.691406 L 2092.859375 947.5 L 2288.640625 1060.988281 L 2288.640625 1083.179688 "/>
</clipPath>
<linearGradient id="linear-pattern-20" gradientUnits="userSpaceOnUse" x1="0.671154" y1="0" x2="1.134862" y2="0" gradientTransform="matrix(-130, 390, -390, -130, 2278.78, 653.38)">
<stop offset="0" stop-color="rgb(73.74115%, 74.917603%, 75.045776%)" stop-opacity="1"/>
<stop offset="0.125" stop-color="rgb(73.843384%, 75.019836%, 75.135803%)" stop-opacity="1"/>
<stop offset="0.25" stop-color="rgb(74.047852%, 75.224304%, 75.317383%)" stop-opacity="1"/>
<stop offset="0.375" stop-color="rgb(74.252319%, 75.428772%, 75.500488%)" stop-opacity="1"/>
<stop offset="0.5" stop-color="rgb(74.456787%, 75.63324%, 75.682068%)" stop-opacity="1"/>
<stop offset="1" stop-color="rgb(74.729919%, 75.906372%, 75.924683%)" stop-opacity="1"/>
</linearGradient>
</defs>
<g clip-path="url(#clip-0)">
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 0 2573.28125 L 2902.179688 2573.28125 L 2902.179688 0 L 0 0 Z M 0 2573.28125 "/>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 1614.75 2386.988281 C 1448.621094 2386.988281 1313.949219 2324.339844 1313.949219 2247.058594 C 1313.949219 2169.78125 1448.621094 2107.140625 1614.75 2107.140625 C 1780.878906 2107.140625 1915.558594 2169.78125 1915.558594 2247.058594 C 1915.558594 2324.339844 1780.878906 2386.988281 1614.75 2386.988281 M 1614.75 2109.378906 C 1451.28125 2109.378906 1318.761719 2171.019531 1318.761719 2247.058594 C 1318.761719 2323.101562 1451.28125 2384.75 1614.75 2384.75 C 1778.21875 2384.75 1910.738281 2323.101562 1910.738281 2247.058594 C 1910.738281 2171.019531 1778.21875 2109.378906 1614.75 2109.378906 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 99.609375%, 99.609375%)" fill-opacity="1" d="M 1614.75 2384.75 C 1451.28125 2384.75 1318.761719 2323.101562 1318.761719 2247.058594 C 1318.761719 2171.019531 1451.28125 2109.378906 1614.75 2109.378906 C 1778.21875 2109.378906 1910.738281 2171.019531 1910.738281 2247.058594 C 1910.738281 2323.101562 1778.21875 2384.75 1614.75 2384.75 M 1614.75 2111.621094 C 1453.941406 2111.621094 1323.578125 2172.261719 1323.578125 2247.058594 C 1323.578125 2321.871094 1453.941406 2382.511719 1614.75 2382.511719 C 1775.558594 2382.511719 1905.929688 2321.871094 1905.929688 2247.058594 C 1905.929688 2172.261719 1775.558594 2111.621094 1614.75 2111.621094 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.609375%)" fill-opacity="1" d="M 1614.75 2382.511719 C 1453.941406 2382.511719 1323.578125 2321.871094 1323.578125 2247.058594 C 1323.578125 2172.261719 1453.941406 2111.621094 1614.75 2111.621094 C 1775.558594 2111.621094 1905.929688 2172.261719 1905.929688 2247.058594 C 1905.929688 2321.871094 1775.558594 2382.511719 1614.75 2382.511719 M 1614.75 2113.859375 C 1456.601562 2113.859375 1328.390625 2173.5 1328.390625 2247.058594 C 1328.390625 2320.628906 1456.601562 2380.269531 1614.75 2380.269531 C 1772.898438 2380.269531 1901.109375 2320.628906 1901.109375 2247.058594 C 1901.109375 2173.5 1772.898438 2113.859375 1614.75 2113.859375 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 1614.75 2380.269531 C 1456.601562 2380.269531 1328.390625 2320.628906 1328.390625 2247.058594 C 1328.390625 2173.5 1456.601562 2113.859375 1614.75 2113.859375 C 1772.898438 2113.859375 1901.109375 2173.5 1901.109375 2247.058594 C 1901.109375 2320.628906 1772.898438 2380.269531 1614.75 2380.269531 M 1614.75 2116.101562 C 1459.261719 2116.101562 1333.210938 2174.730469 1333.210938 2247.058594 C 1333.210938 2319.390625 1459.261719 2378.03125 1614.75 2378.03125 C 1770.238281 2378.03125 1896.300781 2319.390625 1896.300781 2247.058594 C 1896.300781 2174.730469 1770.238281 2116.101562 1614.75 2116.101562 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 1614.75 2378.03125 C 1459.261719 2378.03125 1333.210938 2319.390625 1333.210938 2247.058594 C 1333.210938 2174.730469 1459.261719 2116.101562 1614.75 2116.101562 C 1770.238281 2116.101562 1896.300781 2174.730469 1896.300781 2247.058594 C 1896.300781 2319.390625 1770.238281 2378.03125 1614.75 2378.03125 M 1614.75 2118.339844 C 1461.921875 2118.339844 1338.019531 2175.96875 1338.019531 2247.058594 C 1338.019531 2318.148438 1461.921875 2375.789062 1614.75 2375.789062 C 1767.578125 2375.789062 1891.480469 2318.148438 1891.480469 2247.058594 C 1891.480469 2175.96875 1767.578125 2118.339844 1614.75 2118.339844 "/>
<path fill-rule="nonzero" fill="rgb(98.4375%, 98.4375%, 98.4375%)" fill-opacity="1" d="M 1614.75 2375.789062 C 1461.921875 2375.789062 1338.019531 2318.148438 1338.019531 2247.058594 C 1338.019531 2175.96875 1461.921875 2118.339844 1614.75 2118.339844 C 1767.578125 2118.339844 1891.480469 2175.96875 1891.480469 2247.058594 C 1891.480469 2318.148438 1767.578125 2375.789062 1614.75 2375.789062 M 1614.75 2120.578125 C 1464.578125 2120.578125 1342.839844 2177.210938 1342.839844 2247.058594 C 1342.839844 2316.921875 1464.578125 2373.550781 1614.75 2373.550781 C 1764.921875 2373.550781 1886.671875 2316.921875 1886.671875 2247.058594 C 1886.671875 2177.210938 1764.921875 2120.578125 1614.75 2120.578125 "/>
<path fill-rule="nonzero" fill="rgb(98.046875%, 98.046875%, 98.046875%)" fill-opacity="1" d="M 1614.75 2373.550781 C 1464.578125 2373.550781 1342.839844 2316.921875 1342.839844 2247.058594 C 1342.839844 2177.210938 1464.578125 2120.578125 1614.75 2120.578125 C 1764.921875 2120.578125 1886.671875 2177.210938 1886.671875 2247.058594 C 1886.671875 2316.921875 1764.921875 2373.550781 1614.75 2373.550781 M 1614.75 2122.820312 C 1467.238281 2122.820312 1347.648438 2178.441406 1347.648438 2247.058594 C 1347.648438 2315.679688 1467.238281 2371.308594 1614.75 2371.308594 C 1762.269531 2371.308594 1881.851562 2315.679688 1881.851562 2247.058594 C 1881.851562 2178.441406 1762.269531 2122.820312 1614.75 2122.820312 "/>
<path fill-rule="nonzero" fill="rgb(97.65625%, 97.65625%, 98.046875%)" fill-opacity="1" d="M 1614.75 2371.308594 C 1467.238281 2371.308594 1347.648438 2315.679688 1347.648438 2247.058594 C 1347.648438 2178.441406 1467.238281 2122.820312 1614.75 2122.820312 C 1762.269531 2122.820312 1881.851562 2178.441406 1881.851562 2247.058594 C 1881.851562 2315.679688 1762.269531 2371.308594 1614.75 2371.308594 M 1614.75 2125.058594 C 1469.898438 2125.058594 1352.46875 2179.679688 1352.46875 2247.058594 C 1352.46875 2314.441406 1469.898438 2369.070312 1614.75 2369.070312 C 1759.609375 2369.070312 1877.039062 2314.441406 1877.039062 2247.058594 C 1877.039062 2179.679688 1759.609375 2125.058594 1614.75 2125.058594 "/>
<path fill-rule="nonzero" fill="rgb(97.264099%, 97.264099%, 97.65625%)" fill-opacity="1" d="M 1614.75 2369.070312 C 1469.898438 2369.070312 1352.46875 2314.441406 1352.46875 2247.058594 C 1352.46875 2179.679688 1469.898438 2125.058594 1614.75 2125.058594 C 1759.609375 2125.058594 1877.039062 2179.679688 1877.039062 2247.058594 C 1877.039062 2314.441406 1759.609375 2369.070312 1614.75 2369.070312 M 1614.75 2127.300781 C 1472.550781 2127.300781 1357.28125 2180.921875 1357.28125 2247.058594 C 1357.28125 2313.210938 1472.550781 2366.828125 1614.75 2366.828125 C 1756.949219 2366.828125 1872.21875 2313.210938 1872.21875 2247.058594 C 1872.21875 2180.921875 1756.949219 2127.300781 1614.75 2127.300781 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 97.264099%, 97.264099%)" fill-opacity="1" d="M 1614.75 2366.828125 C 1472.550781 2366.828125 1357.28125 2313.210938 1357.28125 2247.058594 C 1357.28125 2180.921875 1472.550781 2127.300781 1614.75 2127.300781 C 1756.949219 2127.300781 1872.21875 2180.921875 1872.21875 2247.058594 C 1872.21875 2313.210938 1756.949219 2366.828125 1614.75 2366.828125 M 1614.75 2129.539062 C 1475.210938 2129.539062 1362.101562 2182.148438 1362.101562 2247.058594 C 1362.101562 2311.96875 1475.210938 2364.589844 1614.75 2364.589844 C 1754.289062 2364.589844 1867.410156 2311.96875 1867.410156 2247.058594 C 1867.410156 2182.148438 1754.289062 2129.539062 1614.75 2129.539062 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 96.875%, 96.875%)" fill-opacity="1" d="M 1614.75 2364.589844 C 1475.210938 2364.589844 1362.101562 2311.96875 1362.101562 2247.058594 C 1362.101562 2182.148438 1475.210938 2129.539062 1614.75 2129.539062 C 1754.289062 2129.539062 1867.410156 2182.148438 1867.410156 2247.058594 C 1867.410156 2311.96875 1754.289062 2364.589844 1614.75 2364.589844 M 1614.75 2131.78125 C 1477.871094 2131.78125 1366.910156 2183.390625 1366.910156 2247.058594 C 1366.910156 2310.730469 1477.871094 2362.351562 1614.75 2362.351562 C 1751.628906 2362.351562 1862.589844 2310.730469 1862.589844 2247.058594 C 1862.589844 2183.390625 1751.628906 2131.78125 1614.75 2131.78125 "/>
<path fill-rule="nonzero" fill="rgb(96.484375%, 96.484375%, 96.875%)" fill-opacity="1" d="M 1614.75 2362.351562 C 1477.871094 2362.351562 1366.910156 2310.730469 1366.910156 2247.058594 C 1366.910156 2183.390625 1477.871094 2131.78125 1614.75 2131.78125 C 1751.628906 2131.78125 1862.589844 2183.390625 1862.589844 2247.058594 C 1862.589844 2310.730469 1751.628906 2362.351562 1614.75 2362.351562 M 1614.75 2134.019531 C 1480.53125 2134.019531 1371.730469 2184.628906 1371.730469 2247.058594 C 1371.730469 2309.5 1480.53125 2360.109375 1614.75 2360.109375 C 1748.96875 2360.109375 1857.769531 2309.5 1857.769531 2247.058594 C 1857.769531 2184.628906 1748.96875 2134.019531 1614.75 2134.019531 "/>
<path fill-rule="nonzero" fill="rgb(96.09375%, 96.09375%, 96.484375%)" fill-opacity="1" d="M 1614.75 2360.109375 C 1480.53125 2360.109375 1371.730469 2309.5 1371.730469 2247.058594 C 1371.730469 2184.628906 1480.53125 2134.019531 1614.75 2134.019531 C 1748.96875 2134.019531 1857.769531 2184.628906 1857.769531 2247.058594 C 1857.769531 2309.5 1748.96875 2360.109375 1614.75 2360.109375 M 1614.75 2136.261719 C 1483.191406 2136.261719 1376.539062 2185.871094 1376.539062 2247.058594 C 1376.539062 2308.261719 1483.191406 2357.871094 1614.75 2357.871094 C 1746.308594 2357.871094 1852.960938 2308.261719 1852.960938 2247.058594 C 1852.960938 2185.871094 1746.308594 2136.261719 1614.75 2136.261719 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 1614.75 2357.871094 C 1483.191406 2357.871094 1376.539062 2308.261719 1376.539062 2247.058594 C 1376.539062 2185.871094 1483.191406 2136.261719 1614.75 2136.261719 C 1746.308594 2136.261719 1852.960938 2185.871094 1852.960938 2247.058594 C 1852.960938 2308.261719 1746.308594 2357.871094 1614.75 2357.871094 M 1614.75 2138.5 C 1485.851562 2138.5 1381.359375 2187.101562 1381.359375 2247.058594 C 1381.359375 2307.019531 1485.851562 2355.628906 1614.75 2355.628906 C 1743.648438 2355.628906 1848.140625 2307.019531 1848.140625 2247.058594 C 1848.140625 2187.101562 1743.648438 2138.5 1614.75 2138.5 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 1614.75 2355.628906 C 1485.851562 2355.628906 1381.359375 2307.019531 1381.359375 2247.058594 C 1381.359375 2187.101562 1485.851562 2138.5 1614.75 2138.5 C 1743.648438 2138.5 1848.140625 2187.101562 1848.140625 2247.058594 C 1848.140625 2307.019531 1743.648438 2355.628906 1614.75 2355.628906 M 1614.75 2140.738281 C 1488.511719 2140.738281 1386.171875 2188.339844 1386.171875 2247.058594 C 1386.171875 2305.78125 1488.511719 2353.390625 1614.75 2353.390625 C 1740.988281 2353.390625 1843.328125 2305.78125 1843.328125 2247.058594 C 1843.328125 2188.339844 1740.988281 2140.738281 1614.75 2140.738281 "/>
<path fill-rule="nonzero" fill="rgb(95.3125%, 95.701599%, 95.701599%)" fill-opacity="1" d="M 1614.75 2353.390625 C 1488.511719 2353.390625 1386.171875 2305.78125 1386.171875 2247.058594 C 1386.171875 2188.339844 1488.511719 2140.738281 1614.75 2140.738281 C 1740.988281 2140.738281 1843.328125 2188.339844 1843.328125 2247.058594 C 1843.328125 2305.78125 1740.988281 2353.390625 1614.75 2353.390625 M 1614.75 2142.980469 C 1491.171875 2142.980469 1390.988281 2189.578125 1390.988281 2247.058594 C 1390.988281 2304.550781 1491.171875 2351.148438 1614.75 2351.148438 C 1738.328125 2351.148438 1838.511719 2304.550781 1838.511719 2247.058594 C 1838.511719 2189.578125 1738.328125 2142.980469 1614.75 2142.980469 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 95.3125%, 95.701599%)" fill-opacity="1" d="M 1614.75 2351.148438 C 1491.171875 2351.148438 1390.988281 2304.550781 1390.988281 2247.058594 C 1390.988281 2189.578125 1491.171875 2142.980469 1614.75 2142.980469 C 1738.328125 2142.980469 1838.511719 2189.578125 1838.511719 2247.058594 C 1838.511719 2304.550781 1738.328125 2351.148438 1614.75 2351.148438 M 1614.75 2145.21875 C 1493.828125 2145.21875 1395.800781 2190.808594 1395.800781 2247.058594 C 1395.800781 2303.308594 1493.828125 2348.910156 1614.75 2348.910156 C 1735.671875 2348.910156 1833.699219 2303.308594 1833.699219 2247.058594 C 1833.699219 2190.808594 1735.671875 2145.21875 1614.75 2145.21875 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 94.921875%, 95.3125%)" fill-opacity="1" d="M 1614.75 2348.910156 C 1493.828125 2348.910156 1395.800781 2303.308594 1395.800781 2247.058594 C 1395.800781 2190.808594 1493.828125 2145.21875 1614.75 2145.21875 C 1735.671875 2145.21875 1833.699219 2190.808594 1833.699219 2247.058594 C 1833.699219 2303.308594 1735.671875 2348.910156 1614.75 2348.910156 M 1614.75 2147.460938 C 1496.488281 2147.460938 1400.621094 2192.050781 1400.621094 2247.058594 C 1400.621094 2302.070312 1496.488281 2346.671875 1614.75 2346.671875 C 1733.011719 2346.671875 1828.878906 2302.070312 1828.878906 2247.058594 C 1828.878906 2192.050781 1733.011719 2147.460938 1614.75 2147.460938 "/>
<path fill-rule="nonzero" fill="rgb(94.53125%, 94.53125%, 94.921875%)" fill-opacity="1" d="M 1614.75 2346.671875 C 1496.488281 2346.671875 1400.621094 2302.070312 1400.621094 2247.058594 C 1400.621094 2192.050781 1496.488281 2147.460938 1614.75 2147.460938 C 1733.011719 2147.460938 1828.878906 2192.050781 1828.878906 2247.058594 C 1828.878906 2302.070312 1733.011719 2346.671875 1614.75 2346.671875 M 1614.75 2149.699219 C 1499.148438 2149.699219 1405.429688 2193.289062 1405.429688 2247.058594 C 1405.429688 2300.839844 1499.148438 2344.429688 1614.75 2344.429688 C 1730.351562 2344.429688 1824.070312 2300.839844 1824.070312 2247.058594 C 1824.070312 2193.289062 1730.351562 2149.699219 1614.75 2149.699219 "/>
<path fill-rule="nonzero" fill="rgb(94.139099%, 94.139099%, 94.53125%)" fill-opacity="1" d="M 1614.75 2344.429688 C 1499.148438 2344.429688 1405.429688 2300.839844 1405.429688 2247.058594 C 1405.429688 2193.289062 1499.148438 2149.699219 1614.75 2149.699219 C 1730.351562 2149.699219 1824.070312 2193.289062 1824.070312 2247.058594 C 1824.070312 2300.839844 1730.351562 2344.429688 1614.75 2344.429688 M 1614.75 2151.941406 C 1501.808594 2151.941406 1410.25 2194.53125 1410.25 2247.058594 C 1410.25 2299.601562 1501.808594 2342.191406 1614.75 2342.191406 C 1727.691406 2342.191406 1819.25 2299.601562 1819.25 2247.058594 C 1819.25 2194.53125 1727.691406 2151.941406 1614.75 2151.941406 "/>
<path fill-rule="nonzero" fill="rgb(93.75%, 93.75%, 94.139099%)" fill-opacity="1" d="M 1614.75 2342.191406 C 1501.808594 2342.191406 1410.25 2299.601562 1410.25 2247.058594 C 1410.25 2194.53125 1501.808594 2151.941406 1614.75 2151.941406 C 1727.691406 2151.941406 1819.25 2194.53125 1819.25 2247.058594 C 1819.25 2299.601562 1727.691406 2342.191406 1614.75 2342.191406 M 1614.75 2154.179688 C 1504.46875 2154.179688 1415.058594 2195.761719 1415.058594 2247.058594 C 1415.058594 2298.359375 1504.46875 2339.949219 1614.75 2339.949219 C 1725.039062 2339.949219 1814.441406 2298.359375 1814.441406 2247.058594 C 1814.441406 2195.761719 1725.039062 2154.179688 1614.75 2154.179688 "/>
<path fill-rule="nonzero" fill="rgb(93.359375%, 93.75%, 94.139099%)" fill-opacity="1" d="M 1614.75 2339.949219 C 1504.46875 2339.949219 1415.058594 2298.359375 1415.058594 2247.058594 C 1415.058594 2195.761719 1504.46875 2154.179688 1614.75 2154.179688 C 1725.039062 2154.179688 1814.441406 2195.761719 1814.441406 2247.058594 C 1814.441406 2298.359375 1725.039062 2339.949219 1614.75 2339.949219 M 1614.75 2156.410156 C 1507.128906 2156.410156 1419.878906 2197 1419.878906 2247.058594 C 1419.878906 2297.128906 1507.128906 2337.710938 1614.75 2337.710938 C 1722.378906 2337.710938 1809.621094 2297.128906 1809.621094 2247.058594 C 1809.621094 2197 1722.378906 2156.410156 1614.75 2156.410156 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 93.359375%, 93.75%)" fill-opacity="1" d="M 1614.75 2337.710938 C 1507.128906 2337.710938 1419.878906 2297.128906 1419.878906 2247.058594 C 1419.878906 2197 1507.128906 2156.410156 1614.75 2156.410156 C 1722.378906 2156.410156 1809.621094 2197 1809.621094 2247.058594 C 1809.621094 2297.128906 1722.378906 2337.710938 1614.75 2337.710938 M 1614.75 2158.648438 C 1509.78125 2158.648438 1424.691406 2198.238281 1424.691406 2247.058594 C 1424.691406 2295.890625 1509.78125 2335.46875 1614.75 2335.46875 C 1719.71875 2335.46875 1804.808594 2295.890625 1804.808594 2247.058594 C 1804.808594 2198.238281 1719.71875 2158.648438 1614.75 2158.648438 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 92.96875%, 93.359375%)" fill-opacity="1" d="M 1614.75 2335.46875 C 1509.78125 2335.46875 1424.691406 2295.890625 1424.691406 2247.058594 C 1424.691406 2198.238281 1509.78125 2158.648438 1614.75 2158.648438 C 1719.71875 2158.648438 1804.808594 2198.238281 1804.808594 2247.058594 C 1804.808594 2295.890625 1719.71875 2335.46875 1614.75 2335.46875 M 1614.75 2160.890625 C 1512.441406 2160.890625 1429.511719 2199.46875 1429.511719 2247.058594 C 1429.511719 2294.648438 1512.441406 2333.230469 1614.75 2333.230469 C 1717.058594 2333.230469 1799.988281 2294.648438 1799.988281 2247.058594 C 1799.988281 2199.46875 1717.058594 2160.890625 1614.75 2160.890625 "/>
<path fill-rule="nonzero" fill="rgb(92.576599%, 92.576599%, 92.96875%)" fill-opacity="1" d="M 1614.75 2333.230469 C 1512.441406 2333.230469 1429.511719 2294.648438 1429.511719 2247.058594 C 1429.511719 2199.46875 1512.441406 2160.890625 1614.75 2160.890625 C 1717.058594 2160.890625 1799.988281 2199.46875 1799.988281 2247.058594 C 1799.988281 2294.648438 1717.058594 2333.230469 1614.75 2333.230469 M 1614.75 2163.128906 C 1515.101562 2163.128906 1434.320312 2200.710938 1434.320312 2247.058594 C 1434.320312 2293.410156 1515.101562 2330.988281 1614.75 2330.988281 C 1714.398438 2330.988281 1795.179688 2293.410156 1795.179688 2247.058594 C 1795.179688 2200.710938 1714.398438 2163.128906 1614.75 2163.128906 "/>
<path fill-rule="nonzero" fill="rgb(92.1875%, 92.1875%, 92.96875%)" fill-opacity="1" d="M 1614.75 2330.988281 C 1515.101562 2330.988281 1434.320312 2293.410156 1434.320312 2247.058594 C 1434.320312 2200.710938 1515.101562 2163.128906 1614.75 2163.128906 C 1714.398438 2163.128906 1795.179688 2200.710938 1795.179688 2247.058594 C 1795.179688 2293.410156 1714.398438 2330.988281 1614.75 2330.988281 M 1614.75 2165.371094 C 1517.761719 2165.371094 1439.140625 2201.949219 1439.140625 2247.058594 C 1439.140625 2292.179688 1517.761719 2328.75 1614.75 2328.75 C 1711.738281 2328.75 1790.359375 2292.179688 1790.359375 2247.058594 C 1790.359375 2201.949219 1711.738281 2165.371094 1614.75 2165.371094 "/>
<path fill-rule="nonzero" fill="rgb(91.796875%, 91.796875%, 92.576599%)" fill-opacity="1" d="M 1614.75 2328.75 C 1517.761719 2328.75 1439.140625 2292.179688 1439.140625 2247.058594 C 1439.140625 2201.949219 1517.761719 2165.371094 1614.75 2165.371094 C 1711.738281 2165.371094 1790.359375 2201.949219 1790.359375 2247.058594 C 1790.359375 2292.179688 1711.738281 2328.75 1614.75 2328.75 M 1614.75 2167.609375 C 1520.421875 2167.609375 1443.949219 2203.179688 1443.949219 2247.058594 C 1443.949219 2290.941406 1520.421875 2326.511719 1614.75 2326.511719 C 1709.078125 2326.511719 1785.550781 2290.941406 1785.550781 2247.058594 C 1785.550781 2203.179688 1709.078125 2167.609375 1614.75 2167.609375 "/>
<path fill-rule="nonzero" fill="rgb(91.40625%, 91.796875%, 92.1875%)" fill-opacity="1" d="M 1614.75 2326.511719 C 1520.421875 2326.511719 1443.949219 2290.941406 1443.949219 2247.058594 C 1443.949219 2203.179688 1520.421875 2167.609375 1614.75 2167.609375 C 1709.078125 2167.609375 1785.550781 2203.179688 1785.550781 2247.058594 C 1785.550781 2290.941406 1709.078125 2326.511719 1614.75 2326.511719 "/>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 2505.851562 1873.820312 C 2339.71875 1873.820312 2205.039062 1811.179688 2205.039062 1733.898438 C 2205.039062 1656.621094 2339.71875 1593.980469 2505.851562 1593.980469 C 2671.980469 1593.980469 2806.648438 1656.621094 2806.648438 1733.898438 C 2806.648438 1811.179688 2671.980469 1873.820312 2505.851562 1873.820312 M 2505.851562 1596.21875 C 2342.378906 1596.21875 2209.859375 1657.859375 2209.859375 1733.898438 C 2209.859375 1809.941406 2342.378906 1871.578125 2505.851562 1871.578125 C 2669.320312 1871.578125 2801.839844 1809.941406 2801.839844 1733.898438 C 2801.839844 1657.859375 2669.320312 1596.21875 2505.851562 1596.21875 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 99.609375%, 99.609375%)" fill-opacity="1" d="M 2505.851562 1871.578125 C 2342.378906 1871.578125 2209.859375 1809.941406 2209.859375 1733.898438 C 2209.859375 1657.859375 2342.378906 1596.21875 2505.851562 1596.21875 C 2669.320312 1596.21875 2801.839844 1657.859375 2801.839844 1733.898438 C 2801.839844 1809.941406 2669.320312 1871.578125 2505.851562 1871.578125 M 2505.851562 1598.460938 C 2345.039062 1598.460938 2214.671875 1659.101562 2214.671875 1733.898438 C 2214.671875 1808.699219 2345.039062 1869.339844 2505.851562 1869.339844 C 2666.660156 1869.339844 2797.019531 1808.699219 2797.019531 1733.898438 C 2797.019531 1659.101562 2666.660156 1598.460938 2505.851562 1598.460938 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.609375%)" fill-opacity="1" d="M 2505.851562 1869.339844 C 2345.039062 1869.339844 2214.671875 1808.699219 2214.671875 1733.898438 C 2214.671875 1659.101562 2345.039062 1598.460938 2505.851562 1598.460938 C 2666.660156 1598.460938 2797.019531 1659.101562 2797.019531 1733.898438 C 2797.019531 1808.699219 2666.660156 1869.339844 2505.851562 1869.339844 M 2505.851562 1600.699219 C 2347.699219 1600.699219 2219.488281 1660.328125 2219.488281 1733.898438 C 2219.488281 1807.46875 2347.699219 1867.101562 2505.851562 1867.101562 C 2664 1867.101562 2792.210938 1807.46875 2792.210938 1733.898438 C 2792.210938 1660.328125 2664 1600.699219 2505.851562 1600.699219 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 2505.851562 1867.101562 C 2347.699219 1867.101562 2219.488281 1807.46875 2219.488281 1733.898438 C 2219.488281 1660.328125 2347.699219 1600.699219 2505.851562 1600.699219 C 2664 1600.699219 2792.210938 1660.328125 2792.210938 1733.898438 C 2792.210938 1807.46875 2664 1867.101562 2505.851562 1867.101562 M 2505.851562 1602.941406 C 2350.351562 1602.941406 2224.300781 1661.570312 2224.300781 1733.898438 C 2224.300781 1806.230469 2350.351562 1864.859375 2505.851562 1864.859375 C 2661.339844 1864.859375 2787.390625 1806.230469 2787.390625 1733.898438 C 2787.390625 1661.570312 2661.339844 1602.941406 2505.851562 1602.941406 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 2505.851562 1864.859375 C 2350.351562 1864.859375 2224.300781 1806.230469 2224.300781 1733.898438 C 2224.300781 1661.570312 2350.351562 1602.941406 2505.851562 1602.941406 C 2661.339844 1602.941406 2787.390625 1661.570312 2787.390625 1733.898438 C 2787.390625 1806.230469 2661.339844 1864.859375 2505.851562 1864.859375 M 2505.851562 1605.179688 C 2353.019531 1605.179688 2229.121094 1662.808594 2229.121094 1733.898438 C 2229.121094 1804.988281 2353.019531 1862.621094 2505.851562 1862.621094 C 2658.679688 1862.621094 2782.578125 1804.988281 2782.578125 1733.898438 C 2782.578125 1662.808594 2658.679688 1605.179688 2505.851562 1605.179688 "/>
<path fill-rule="nonzero" fill="rgb(98.4375%, 98.4375%, 98.4375%)" fill-opacity="1" d="M 2505.851562 1862.621094 C 2353.019531 1862.621094 2229.121094 1804.988281 2229.121094 1733.898438 C 2229.121094 1662.808594 2353.019531 1605.179688 2505.851562 1605.179688 C 2658.679688 1605.179688 2782.578125 1662.808594 2782.578125 1733.898438 C 2782.578125 1804.988281 2658.679688 1862.621094 2505.851562 1862.621094 M 2505.851562 1607.421875 C 2355.671875 1607.421875 2233.929688 1664.050781 2233.929688 1733.898438 C 2233.929688 1803.761719 2355.671875 1860.390625 2505.851562 1860.390625 C 2656.019531 1860.390625 2777.761719 1803.761719 2777.761719 1733.898438 C 2777.761719 1664.050781 2656.019531 1607.421875 2505.851562 1607.421875 "/>
<path fill-rule="nonzero" fill="rgb(98.046875%, 98.046875%, 98.046875%)" fill-opacity="1" d="M 2505.851562 1860.390625 C 2355.671875 1860.390625 2233.929688 1803.761719 2233.929688 1733.898438 C 2233.929688 1664.050781 2355.671875 1607.421875 2505.851562 1607.421875 C 2656.019531 1607.421875 2777.761719 1664.050781 2777.761719 1733.898438 C 2777.761719 1803.761719 2656.019531 1860.390625 2505.851562 1860.390625 M 2505.851562 1609.660156 C 2358.328125 1609.660156 2238.75 1665.28125 2238.75 1733.898438 C 2238.75 1802.519531 2358.328125 1858.140625 2505.851562 1858.140625 C 2653.359375 1858.140625 2772.949219 1802.519531 2772.949219 1733.898438 C 2772.949219 1665.28125 2653.359375 1609.660156 2505.851562 1609.660156 "/>
<path fill-rule="nonzero" fill="rgb(97.65625%, 97.65625%, 98.046875%)" fill-opacity="1" d="M 2505.851562 1858.140625 C 2358.328125 1858.140625 2238.75 1802.519531 2238.75 1733.898438 C 2238.75 1665.28125 2358.328125 1609.660156 2505.851562 1609.660156 C 2653.359375 1609.660156 2772.949219 1665.28125 2772.949219 1733.898438 C 2772.949219 1802.519531 2653.359375 1858.140625 2505.851562 1858.140625 M 2505.851562 1611.898438 C 2360.988281 1611.898438 2243.558594 1666.519531 2243.558594 1733.898438 C 2243.558594 1801.28125 2360.988281 1855.898438 2505.851562 1855.898438 C 2650.699219 1855.898438 2768.128906 1801.28125 2768.128906 1733.898438 C 2768.128906 1666.519531 2650.699219 1611.898438 2505.851562 1611.898438 "/>
<path fill-rule="nonzero" fill="rgb(97.264099%, 97.264099%, 97.65625%)" fill-opacity="1" d="M 2505.851562 1855.898438 C 2360.988281 1855.898438 2243.558594 1801.28125 2243.558594 1733.898438 C 2243.558594 1666.519531 2360.988281 1611.898438 2505.851562 1611.898438 C 2650.699219 1611.898438 2768.128906 1666.519531 2768.128906 1733.898438 C 2768.128906 1801.28125 2650.699219 1855.898438 2505.851562 1855.898438 M 2505.851562 1614.140625 C 2363.648438 1614.140625 2248.378906 1667.761719 2248.378906 1733.898438 C 2248.378906 1800.039062 2363.648438 1853.671875 2505.851562 1853.671875 C 2648.039062 1853.671875 2763.320312 1800.039062 2763.320312 1733.898438 C 2763.320312 1667.761719 2648.039062 1614.140625 2505.851562 1614.140625 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 97.264099%, 97.264099%)" fill-opacity="1" d="M 2505.851562 1853.671875 C 2363.648438 1853.671875 2248.378906 1800.039062 2248.378906 1733.898438 C 2248.378906 1667.761719 2363.648438 1614.140625 2505.851562 1614.140625 C 2648.039062 1614.140625 2763.320312 1667.761719 2763.320312 1733.898438 C 2763.320312 1800.039062 2648.039062 1853.671875 2505.851562 1853.671875 M 2505.851562 1616.378906 C 2366.308594 1616.378906 2253.191406 1668.988281 2253.191406 1733.898438 C 2253.191406 1798.808594 2366.308594 1851.429688 2505.851562 1851.429688 C 2645.390625 1851.429688 2758.5 1798.808594 2758.5 1733.898438 C 2758.5 1668.988281 2645.390625 1616.378906 2505.851562 1616.378906 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 96.875%, 96.875%)" fill-opacity="1" d="M 2505.851562 1851.429688 C 2366.308594 1851.429688 2253.191406 1798.808594 2253.191406 1733.898438 C 2253.191406 1668.988281 2366.308594 1616.378906 2505.851562 1616.378906 C 2645.390625 1616.378906 2758.5 1668.988281 2758.5 1733.898438 C 2758.5 1798.808594 2645.390625 1851.429688 2505.851562 1851.429688 M 2505.851562 1618.609375 C 2368.96875 1618.609375 2258.011719 1670.230469 2258.011719 1733.898438 C 2258.011719 1797.570312 2368.96875 1849.191406 2505.851562 1849.191406 C 2642.730469 1849.191406 2753.691406 1797.570312 2753.691406 1733.898438 C 2753.691406 1670.230469 2642.730469 1618.609375 2505.851562 1618.609375 "/>
<path fill-rule="nonzero" fill="rgb(96.484375%, 96.484375%, 96.875%)" fill-opacity="1" d="M 2505.851562 1849.191406 C 2368.96875 1849.191406 2258.011719 1797.570312 2258.011719 1733.898438 C 2258.011719 1670.230469 2368.96875 1618.609375 2505.851562 1618.609375 C 2642.730469 1618.609375 2753.691406 1670.230469 2753.691406 1733.898438 C 2753.691406 1797.570312 2642.730469 1849.191406 2505.851562 1849.191406 M 2505.851562 1620.859375 C 2371.628906 1620.859375 2262.820312 1671.46875 2262.820312 1733.898438 C 2262.820312 1796.328125 2371.628906 1846.949219 2505.851562 1846.949219 C 2640.070312 1846.949219 2748.871094 1796.328125 2748.871094 1733.898438 C 2748.871094 1671.46875 2640.070312 1620.859375 2505.851562 1620.859375 "/>
<path fill-rule="nonzero" fill="rgb(96.09375%, 96.09375%, 96.484375%)" fill-opacity="1" d="M 2505.851562 1846.949219 C 2371.628906 1846.949219 2262.820312 1796.328125 2262.820312 1733.898438 C 2262.820312 1671.46875 2371.628906 1620.859375 2505.851562 1620.859375 C 2640.070312 1620.859375 2748.871094 1671.46875 2748.871094 1733.898438 C 2748.871094 1796.328125 2640.070312 1846.949219 2505.851562 1846.949219 M 2505.851562 1623.101562 C 2374.289062 1623.101562 2267.640625 1672.699219 2267.640625 1733.898438 C 2267.640625 1795.101562 2374.289062 1844.710938 2505.851562 1844.710938 C 2637.410156 1844.710938 2744.058594 1795.101562 2744.058594 1733.898438 C 2744.058594 1672.699219 2637.410156 1623.101562 2505.851562 1623.101562 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 2505.851562 1844.710938 C 2374.289062 1844.710938 2267.640625 1795.101562 2267.640625 1733.898438 C 2267.640625 1672.699219 2374.289062 1623.101562 2505.851562 1623.101562 C 2637.410156 1623.101562 2744.058594 1672.699219 2744.058594 1733.898438 C 2744.058594 1795.101562 2637.410156 1844.710938 2505.851562 1844.710938 M 2505.851562 1625.328125 C 2376.949219 1625.328125 2272.449219 1673.941406 2272.449219 1733.898438 C 2272.449219 1793.859375 2376.949219 1842.46875 2505.851562 1842.46875 C 2634.75 1842.46875 2739.238281 1793.859375 2739.238281 1733.898438 C 2739.238281 1673.941406 2634.75 1625.328125 2505.851562 1625.328125 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 2505.851562 1842.46875 C 2376.949219 1842.46875 2272.449219 1793.859375 2272.449219 1733.898438 C 2272.449219 1673.941406 2376.949219 1625.328125 2505.851562 1625.328125 C 2634.75 1625.328125 2739.238281 1673.941406 2739.238281 1733.898438 C 2739.238281 1793.859375 2634.75 1842.46875 2505.851562 1842.46875 M 2505.851562 1627.570312 C 2379.609375 1627.570312 2277.269531 1675.179688 2277.269531 1733.898438 C 2277.269531 1792.621094 2379.609375 1840.230469 2505.851562 1840.230469 C 2632.089844 1840.230469 2734.429688 1792.621094 2734.429688 1733.898438 C 2734.429688 1675.179688 2632.089844 1627.570312 2505.851562 1627.570312 "/>
<path fill-rule="nonzero" fill="rgb(95.3125%, 95.701599%, 95.701599%)" fill-opacity="1" d="M 2505.851562 1840.230469 C 2379.609375 1840.230469 2277.269531 1792.621094 2277.269531 1733.898438 C 2277.269531 1675.179688 2379.609375 1627.570312 2505.851562 1627.570312 C 2632.089844 1627.570312 2734.429688 1675.179688 2734.429688 1733.898438 C 2734.429688 1792.621094 2632.089844 1840.230469 2505.851562 1840.230469 M 2505.851562 1629.808594 C 2382.269531 1629.808594 2282.078125 1676.421875 2282.078125 1733.898438 C 2282.078125 1791.390625 2382.269531 1837.988281 2505.851562 1837.988281 C 2629.429688 1837.988281 2729.609375 1791.390625 2729.609375 1733.898438 C 2729.609375 1676.421875 2629.429688 1629.808594 2505.851562 1629.808594 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 95.3125%, 95.701599%)" fill-opacity="1" d="M 2505.851562 1837.988281 C 2382.269531 1837.988281 2282.078125 1791.390625 2282.078125 1733.898438 C 2282.078125 1676.421875 2382.269531 1629.808594 2505.851562 1629.808594 C 2629.429688 1629.808594 2729.609375 1676.421875 2729.609375 1733.898438 C 2729.609375 1791.390625 2629.429688 1837.988281 2505.851562 1837.988281 M 2505.851562 1632.050781 C 2384.929688 1632.050781 2286.898438 1677.648438 2286.898438 1733.898438 C 2286.898438 1790.148438 2384.929688 1835.75 2505.851562 1835.75 C 2626.769531 1835.75 2724.800781 1790.148438 2724.800781 1733.898438 C 2724.800781 1677.648438 2626.769531 1632.050781 2505.851562 1632.050781 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 94.921875%, 95.3125%)" fill-opacity="1" d="M 2505.851562 1835.75 C 2384.929688 1835.75 2286.898438 1790.148438 2286.898438 1733.898438 C 2286.898438 1677.648438 2384.929688 1632.050781 2505.851562 1632.050781 C 2626.769531 1632.050781 2724.800781 1677.648438 2724.800781 1733.898438 C 2724.800781 1790.148438 2626.769531 1835.75 2505.851562 1835.75 M 2505.851562 1634.289062 C 2387.578125 1634.289062 2291.710938 1678.890625 2291.710938 1733.898438 C 2291.710938 1788.910156 2387.578125 1833.511719 2505.851562 1833.511719 C 2624.109375 1833.511719 2719.980469 1788.910156 2719.980469 1733.898438 C 2719.980469 1678.890625 2624.109375 1634.289062 2505.851562 1634.289062 "/>
<path fill-rule="nonzero" fill="rgb(94.53125%, 94.53125%, 94.921875%)" fill-opacity="1" d="M 2505.851562 1833.511719 C 2387.578125 1833.511719 2291.710938 1788.910156 2291.710938 1733.898438 C 2291.710938 1678.890625 2387.578125 1634.289062 2505.851562 1634.289062 C 2624.109375 1634.289062 2719.980469 1678.890625 2719.980469 1733.898438 C 2719.980469 1788.910156 2624.109375 1833.511719 2505.851562 1833.511719 M 2505.851562 1636.53125 C 2390.238281 1636.53125 2296.53125 1680.128906 2296.53125 1733.898438 C 2296.53125 1787.671875 2390.238281 1831.269531 2505.851562 1831.269531 C 2621.449219 1831.269531 2715.171875 1787.671875 2715.171875 1733.898438 C 2715.171875 1680.128906 2621.449219 1636.53125 2505.851562 1636.53125 "/>
<path fill-rule="nonzero" fill="rgb(94.139099%, 94.139099%, 94.53125%)" fill-opacity="1" d="M 2505.851562 1831.269531 C 2390.238281 1831.269531 2296.53125 1787.671875 2296.53125 1733.898438 C 2296.53125 1680.128906 2390.238281 1636.53125 2505.851562 1636.53125 C 2621.449219 1636.53125 2715.171875 1680.128906 2715.171875 1733.898438 C 2715.171875 1787.671875 2621.449219 1831.269531 2505.851562 1831.269531 M 2505.851562 1638.769531 C 2392.898438 1638.769531 2301.339844 1681.359375 2301.339844 1733.898438 C 2301.339844 1786.441406 2392.898438 1829.03125 2505.851562 1829.03125 C 2618.789062 1829.03125 2710.351562 1786.441406 2710.351562 1733.898438 C 2710.351562 1681.359375 2618.789062 1638.769531 2505.851562 1638.769531 "/>
<path fill-rule="nonzero" fill="rgb(93.75%, 93.75%, 94.139099%)" fill-opacity="1" d="M 2505.851562 1829.03125 C 2392.898438 1829.03125 2301.339844 1786.441406 2301.339844 1733.898438 C 2301.339844 1681.359375 2392.898438 1638.769531 2505.851562 1638.769531 C 2618.789062 1638.769531 2710.351562 1681.359375 2710.351562 1733.898438 C 2710.351562 1786.441406 2618.789062 1829.03125 2505.851562 1829.03125 M 2505.851562 1641.011719 C 2395.558594 1641.011719 2306.160156 1682.601562 2306.160156 1733.898438 C 2306.160156 1785.199219 2395.558594 1826.789062 2505.851562 1826.789062 C 2616.128906 1826.789062 2705.539062 1785.199219 2705.539062 1733.898438 C 2705.539062 1682.601562 2616.128906 1641.011719 2505.851562 1641.011719 "/>
<path fill-rule="nonzero" fill="rgb(93.359375%, 93.75%, 94.139099%)" fill-opacity="1" d="M 2505.851562 1826.789062 C 2395.558594 1826.789062 2306.160156 1785.199219 2306.160156 1733.898438 C 2306.160156 1682.601562 2395.558594 1641.011719 2505.851562 1641.011719 C 2616.128906 1641.011719 2705.539062 1682.601562 2705.539062 1733.898438 C 2705.539062 1785.199219 2616.128906 1826.789062 2505.851562 1826.789062 M 2505.851562 1643.25 C 2398.21875 1643.25 2310.980469 1683.839844 2310.980469 1733.898438 C 2310.980469 1783.960938 2398.21875 1824.550781 2505.851562 1824.550781 C 2613.46875 1824.550781 2700.71875 1783.960938 2700.71875 1733.898438 C 2700.71875 1683.839844 2613.46875 1643.25 2505.851562 1643.25 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 93.359375%, 93.75%)" fill-opacity="1" d="M 2505.851562 1824.550781 C 2398.21875 1824.550781 2310.980469 1783.960938 2310.980469 1733.898438 C 2310.980469 1683.839844 2398.21875 1643.25 2505.851562 1643.25 C 2613.46875 1643.25 2700.71875 1683.839844 2700.71875 1733.898438 C 2700.71875 1783.960938 2613.46875 1824.550781 2505.851562 1824.550781 M 2505.851562 1645.488281 C 2400.878906 1645.488281 2315.789062 1685.070312 2315.789062 1733.898438 C 2315.789062 1782.730469 2400.878906 1822.308594 2505.851562 1822.308594 C 2610.808594 1822.308594 2695.910156 1782.730469 2695.910156 1733.898438 C 2695.910156 1685.070312 2610.808594 1645.488281 2505.851562 1645.488281 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 92.96875%, 93.359375%)" fill-opacity="1" d="M 2505.851562 1822.308594 C 2400.878906 1822.308594 2315.789062 1782.730469 2315.789062 1733.898438 C 2315.789062 1685.070312 2400.878906 1645.488281 2505.851562 1645.488281 C 2610.808594 1645.488281 2695.910156 1685.070312 2695.910156 1733.898438 C 2695.910156 1782.730469 2610.808594 1822.308594 2505.851562 1822.308594 M 2505.851562 1647.730469 C 2403.539062 1647.730469 2320.609375 1686.308594 2320.609375 1733.898438 C 2320.609375 1781.488281 2403.539062 1820.070312 2505.851562 1820.070312 C 2608.148438 1820.070312 2691.089844 1781.488281 2691.089844 1733.898438 C 2691.089844 1686.308594 2608.148438 1647.730469 2505.851562 1647.730469 "/>
<path fill-rule="nonzero" fill="rgb(92.576599%, 92.576599%, 92.96875%)" fill-opacity="1" d="M 2505.851562 1820.070312 C 2403.539062 1820.070312 2320.609375 1781.488281 2320.609375 1733.898438 C 2320.609375 1686.308594 2403.539062 1647.730469 2505.851562 1647.730469 C 2608.148438 1647.730469 2691.089844 1686.308594 2691.089844 1733.898438 C 2691.089844 1781.488281 2608.148438 1820.070312 2505.851562 1820.070312 M 2505.851562 1649.96875 C 2406.199219 1649.96875 2325.421875 1687.550781 2325.421875 1733.898438 C 2325.421875 1780.25 2406.199219 1817.828125 2505.851562 1817.828125 C 2605.5 1817.828125 2686.28125 1780.25 2686.28125 1733.898438 C 2686.28125 1687.550781 2605.5 1649.96875 2505.851562 1649.96875 "/>
<path fill-rule="nonzero" fill="rgb(92.1875%, 92.1875%, 92.96875%)" fill-opacity="1" d="M 2505.851562 1817.828125 C 2406.199219 1817.828125 2325.421875 1780.25 2325.421875 1733.898438 C 2325.421875 1687.550781 2406.199219 1649.96875 2505.851562 1649.96875 C 2605.5 1649.96875 2686.28125 1687.550781 2686.28125 1733.898438 C 2686.28125 1780.25 2605.5 1817.828125 2505.851562 1817.828125 M 2505.851562 1652.210938 C 2408.859375 1652.210938 2330.238281 1688.789062 2330.238281 1733.898438 C 2330.238281 1779.019531 2408.859375 1815.589844 2505.851562 1815.589844 C 2602.839844 1815.589844 2681.460938 1779.019531 2681.460938 1733.898438 C 2681.460938 1688.789062 2602.839844 1652.210938 2505.851562 1652.210938 "/>
<path fill-rule="nonzero" fill="rgb(91.796875%, 91.796875%, 92.576599%)" fill-opacity="1" d="M 2505.851562 1815.589844 C 2408.859375 1815.589844 2330.238281 1779.019531 2330.238281 1733.898438 C 2330.238281 1688.789062 2408.859375 1652.210938 2505.851562 1652.210938 C 2602.839844 1652.210938 2681.460938 1688.789062 2681.460938 1733.898438 C 2681.460938 1779.019531 2602.839844 1815.589844 2505.851562 1815.589844 M 2505.851562 1654.449219 C 2411.519531 1654.449219 2335.050781 1690.019531 2335.050781 1733.898438 C 2335.050781 1777.78125 2411.519531 1813.351562 2505.851562 1813.351562 C 2600.179688 1813.351562 2676.648438 1777.78125 2676.648438 1733.898438 C 2676.648438 1690.019531 2600.179688 1654.449219 2505.851562 1654.449219 "/>
<path fill-rule="nonzero" fill="rgb(91.40625%, 91.796875%, 92.1875%)" fill-opacity="1" d="M 2505.851562 1813.351562 C 2411.519531 1813.351562 2335.050781 1777.78125 2335.050781 1733.898438 C 2335.050781 1690.019531 2411.519531 1654.449219 2505.851562 1654.449219 C 2600.179688 1654.449219 2676.648438 1690.019531 2676.648438 1733.898438 C 2676.648438 1777.78125 2600.179688 1813.351562 2505.851562 1813.351562 "/>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 2163.769531 1355.378906 C 2052.628906 1355.378906 1955.558594 1327.339844 1903.488281 1285.640625 C 1903.539062 1284.558594 1903.570312 1283.480469 1903.570312 1282.390625 C 1903.570312 1281.96875 1903.558594 1281.539062 1903.550781 1281.121094 C 1953.691406 1324.019531 2051.421875 1353.140625 2163.769531 1353.140625 C 2327.238281 1353.140625 2459.761719 1291.488281 2459.761719 1215.449219 C 2459.761719 1182.019531 2434.128906 1151.359375 2391.53125 1127.511719 L 2391.53125 1124.050781 C 2437.050781 1148.570312 2464.570312 1180.511719 2464.570312 1215.449219 C 2464.570312 1292.730469 2329.898438 1355.378906 2163.769531 1355.378906 M 1867.78125 1216.140625 C 1866.25 1214.808594 1864.671875 1213.5 1863.039062 1212.199219 C 1866.148438 1149.039062 1959.210938 1096.21875 2084.960938 1080.378906 L 2088.199219 1082.300781 C 1961.339844 1097.839844 1867.78125 1151.558594 1867.78125 1215.449219 C 1867.78125 1215.679688 1867.78125 1215.910156 1867.78125 1216.140625 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 99.609375%, 99.609375%)" fill-opacity="1" d="M 2163.769531 1353.140625 C 2051.421875 1353.140625 1953.691406 1324.019531 1903.550781 1281.121094 C 1903.519531 1279.421875 1903.421875 1277.71875 1903.261719 1276.019531 C 1951.058594 1320.421875 2049.769531 1350.898438 2163.769531 1350.898438 C 2324.578125 1350.898438 2454.941406 1290.261719 2454.941406 1215.449219 C 2454.941406 1183.539062 2431.21875 1154.210938 2391.53125 1131.058594 L 2391.53125 1127.511719 C 2434.128906 1151.359375 2459.761719 1182.019531 2459.761719 1215.449219 C 2459.761719 1291.488281 2327.238281 1353.140625 2163.769531 1353.140625 M 1872.808594 1220.679688 C 1871.191406 1219.148438 1869.511719 1217.628906 1867.78125 1216.140625 C 1867.78125 1215.910156 1867.78125 1215.679688 1867.78125 1215.449219 C 1867.78125 1151.558594 1961.339844 1097.839844 2088.199219 1082.300781 L 2091.441406 1084.21875 C 1965.621094 1099.179688 1872.589844 1152.261719 1872.589844 1215.449219 C 1872.589844 1217.199219 1872.671875 1218.941406 1872.808594 1220.679688 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.609375%)" fill-opacity="1" d="M 2163.769531 1350.898438 C 2049.769531 1350.898438 1951.058594 1320.421875 1903.261719 1276.019531 C 1903.058594 1273.980469 1902.78125 1271.960938 1902.390625 1269.949219 C 1947.171875 1316.351562 2047.359375 1348.660156 2163.769531 1348.660156 C 2321.921875 1348.660156 2450.128906 1289.019531 2450.128906 1215.449219 C 2450.128906 1185.089844 2428.289062 1157.101562 2391.53125 1134.699219 L 2391.53125 1131.058594 C 2431.21875 1154.210938 2454.941406 1183.539062 2454.941406 1215.449219 C 2454.941406 1290.261719 2324.578125 1350.898438 2163.769531 1350.898438 M 1878.328125 1226.191406 C 1876.570312 1224.328125 1874.730469 1222.5 1872.808594 1220.679688 C 1872.671875 1218.941406 1872.589844 1217.199219 1872.589844 1215.449219 C 1872.589844 1152.261719 1965.621094 1099.179688 2091.441406 1084.21875 L 2094.691406 1086.148438 C 1969.910156 1100.53125 1877.410156 1152.960938 1877.410156 1215.449219 C 1877.410156 1219.070312 1877.71875 1222.648438 1878.328125 1226.191406 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 2163.769531 1348.660156 C 2047.359375 1348.660156 1947.171875 1316.351562 1902.390625 1269.949219 C 1901.871094 1267.191406 1901.171875 1264.449219 1900.308594 1261.738281 C 1940.511719 1311.230469 2043.308594 1346.421875 2163.769531 1346.421875 C 2319.261719 1346.421875 2445.308594 1287.78125 2445.308594 1215.449219 C 2445.308594 1186.671875 2425.351562 1160.058594 2391.53125 1138.449219 L 2391.53125 1134.699219 C 2428.289062 1157.101562 2450.128906 1185.089844 2450.128906 1215.449219 C 2450.128906 1289.019531 2321.921875 1348.660156 2163.769531 1348.660156 M 1884.980469 1233.859375 C 1882.921875 1231.269531 1880.699219 1228.710938 1878.328125 1226.191406 C 1877.71875 1222.648438 1877.410156 1219.070312 1877.410156 1215.449219 C 1877.410156 1152.960938 1969.910156 1100.53125 2094.691406 1086.148438 L 2097.960938 1088.089844 C 1974.210938 1101.871094 1882.21875 1153.671875 1882.21875 1215.449219 C 1882.21875 1221.699219 1883.171875 1227.851562 1884.980469 1233.859375 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 2163.769531 1346.421875 C 2043.308594 1346.421875 1940.511719 1311.230469 1900.308594 1261.738281 C 1897.25 1252.089844 1892.058594 1242.761719 1884.980469 1233.859375 C 1883.171875 1227.851562 1882.21875 1221.699219 1882.21875 1215.449219 C 1882.21875 1153.671875 1974.210938 1101.871094 2097.960938 1088.089844 L 2101.238281 1090.03125 C 1978.511719 1103.210938 1887.039062 1154.359375 1887.039062 1215.449219 C 1887.039062 1286.550781 2010.941406 1344.179688 2163.769531 1344.179688 C 2316.601562 1344.179688 2440.5 1286.550781 2440.5 1215.449219 C 2440.5 1188.289062 2422.410156 1163.089844 2391.53125 1142.320312 L 2391.53125 1138.449219 C 2425.351562 1160.058594 2445.308594 1186.671875 2445.308594 1215.449219 C 2445.308594 1287.78125 2319.261719 1346.421875 2163.769531 1346.421875 "/>
<path fill-rule="nonzero" fill="rgb(98.4375%, 98.4375%, 98.4375%)" fill-opacity="1" d="M 2163.769531 1344.179688 C 2010.941406 1344.179688 1887.039062 1286.550781 1887.039062 1215.449219 C 1887.039062 1154.359375 1978.511719 1103.210938 2101.238281 1090.03125 L 2104.519531 1091.980469 C 1982.839844 1104.558594 1891.851562 1155.058594 1891.851562 1215.449219 C 1891.851562 1285.308594 2013.589844 1341.941406 2163.769531 1341.941406 C 2313.941406 1341.941406 2435.679688 1285.308594 2435.679688 1215.449219 C 2435.679688 1189.941406 2419.449219 1166.199219 2391.53125 1146.328125 L 2391.53125 1142.320312 C 2422.410156 1163.089844 2440.5 1188.289062 2440.5 1215.449219 C 2440.5 1286.550781 2316.601562 1344.179688 2163.769531 1344.179688 "/>
<path fill-rule="nonzero" fill="rgb(98.046875%, 98.046875%, 98.046875%)" fill-opacity="1" d="M 2163.769531 1341.941406 C 2013.589844 1341.941406 1891.851562 1285.308594 1891.851562 1215.449219 C 1891.851562 1155.058594 1982.839844 1104.558594 2104.519531 1091.980469 L 2107.828125 1093.941406 C 1987.171875 1105.898438 1896.671875 1155.761719 1896.671875 1215.449219 C 1896.671875 1284.070312 2016.25 1339.699219 2163.769531 1339.699219 C 2311.28125 1339.699219 2430.871094 1284.070312 2430.871094 1215.449219 C 2430.871094 1191.648438 2416.480469 1169.410156 2391.53125 1150.511719 L 2391.53125 1146.328125 C 2419.449219 1166.199219 2435.679688 1189.941406 2435.679688 1215.449219 C 2435.679688 1285.308594 2313.941406 1341.941406 2163.769531 1341.941406 "/>
<path fill-rule="nonzero" fill="rgb(97.65625%, 97.65625%, 98.046875%)" fill-opacity="1" d="M 2163.769531 1339.699219 C 2016.25 1339.699219 1896.671875 1284.070312 1896.671875 1215.449219 C 1896.671875 1155.761719 1987.171875 1105.898438 2107.828125 1093.941406 L 2111.140625 1095.898438 C 1991.511719 1107.238281 1901.480469 1156.460938 1901.480469 1215.449219 C 1901.480469 1282.828125 2018.910156 1337.460938 2163.769531 1337.460938 C 2308.621094 1337.460938 2426.050781 1282.828125 2426.050781 1215.449219 C 2426.050781 1193.421875 2413.5 1172.75 2391.53125 1154.898438 L 2391.53125 1150.511719 C 2416.480469 1169.410156 2430.871094 1191.648438 2430.871094 1215.449219 C 2430.871094 1284.070312 2311.28125 1339.699219 2163.769531 1339.699219 "/>
<path fill-rule="nonzero" fill="rgb(97.264099%, 97.264099%, 97.65625%)" fill-opacity="1" d="M 2163.769531 1337.460938 C 2018.910156 1337.460938 1901.480469 1282.828125 1901.480469 1215.449219 C 1901.480469 1156.460938 1991.511719 1107.238281 2111.140625 1095.898438 L 2114.480469 1097.878906 C 1995.859375 1108.578125 1906.300781 1157.148438 1906.300781 1215.449219 C 1906.300781 1281.601562 2021.570312 1335.21875 2163.769531 1335.21875 C 2305.960938 1335.21875 2421.238281 1281.601562 2421.238281 1215.449219 C 2421.238281 1195.261719 2410.488281 1176.238281 2391.53125 1159.550781 L 2391.53125 1154.898438 C 2413.5 1172.75 2426.050781 1193.421875 2426.050781 1215.449219 C 2426.050781 1282.828125 2308.621094 1337.460938 2163.769531 1337.460938 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 97.264099%, 97.264099%)" fill-opacity="1" d="M 2163.769531 1335.21875 C 2021.570312 1335.21875 1906.300781 1281.601562 1906.300781 1215.449219 C 1906.300781 1157.148438 1995.859375 1108.578125 2114.480469 1097.878906 L 2117.828125 1099.871094 C 2000.21875 1109.910156 1911.109375 1157.839844 1911.109375 1215.449219 C 1911.109375 1280.359375 2024.230469 1332.980469 2163.769531 1332.980469 C 2303.308594 1332.980469 2416.421875 1280.359375 2416.421875 1215.449219 C 2416.421875 1197.210938 2407.480469 1179.929688 2391.53125 1164.519531 L 2391.53125 1159.550781 C 2410.488281 1176.238281 2421.238281 1195.261719 2421.238281 1215.449219 C 2421.238281 1281.601562 2305.960938 1335.21875 2163.769531 1335.21875 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 96.875%, 96.875%)" fill-opacity="1" d="M 2163.769531 1332.980469 C 2024.230469 1332.980469 1911.109375 1280.359375 1911.109375 1215.449219 C 1911.109375 1157.839844 2000.21875 1109.910156 2117.828125 1099.871094 L 2121.191406 1101.859375 C 2004.609375 1111.25 1915.929688 1158.539062 1915.929688 1215.449219 C 1915.929688 1279.121094 2026.890625 1330.738281 2163.769531 1330.738281 C 2300.648438 1330.738281 2411.609375 1279.121094 2411.609375 1215.449219 C 2411.609375 1199.28125 2404.449219 1183.890625 2391.53125 1169.921875 L 2391.53125 1164.519531 C 2407.480469 1179.929688 2416.421875 1197.210938 2416.421875 1215.449219 C 2416.421875 1280.359375 2303.308594 1332.980469 2163.769531 1332.980469 "/>
<path fill-rule="nonzero" fill="rgb(96.484375%, 96.484375%, 96.875%)" fill-opacity="1" d="M 2163.769531 1330.738281 C 2026.890625 1330.738281 1915.929688 1279.121094 1915.929688 1215.449219 C 1915.929688 1158.539062 2004.609375 1111.25 2121.191406 1101.859375 L 2124.578125 1103.871094 C 2009 1112.589844 1920.738281 1159.230469 1920.738281 1215.449219 C 1920.738281 1277.890625 2029.550781 1328.5 2163.769531 1328.5 C 2297.988281 1328.5 2406.789062 1277.890625 2406.789062 1215.449219 C 2406.789062 1201.550781 2401.398438 1188.230469 2391.53125 1175.929688 L 2391.53125 1169.921875 C 2404.449219 1183.890625 2411.609375 1199.28125 2411.609375 1215.449219 C 2411.609375 1279.121094 2300.648438 1330.738281 2163.769531 1330.738281 "/>
<path fill-rule="nonzero" fill="rgb(96.09375%, 96.09375%, 96.484375%)" fill-opacity="1" d="M 2163.769531 1328.5 C 2029.550781 1328.5 1920.738281 1277.890625 1920.738281 1215.449219 C 1920.738281 1159.230469 2009 1112.589844 2124.578125 1103.871094 L 2127.988281 1105.890625 C 2013.410156 1113.921875 1925.558594 1159.921875 1925.558594 1215.449219 C 1925.558594 1276.648438 2032.210938 1326.261719 2163.769531 1326.261719 C 2295.328125 1326.261719 2401.980469 1276.648438 2401.980469 1215.449219 C 2401.980469 1204.121094 2398.320312 1193.191406 2391.53125 1182.890625 L 2391.53125 1175.929688 C 2401.398438 1188.230469 2406.789062 1201.550781 2406.789062 1215.449219 C 2406.789062 1277.890625 2297.988281 1328.5 2163.769531 1328.5 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 2163.769531 1326.261719 C 2032.210938 1326.261719 1925.558594 1276.648438 1925.558594 1215.449219 C 1925.558594 1159.921875 2013.410156 1113.921875 2127.988281 1105.890625 L 2131.410156 1107.921875 C 2017.820312 1115.25 1930.378906 1160.601562 1930.378906 1215.449219 C 1930.378906 1275.410156 2034.871094 1324.019531 2163.769531 1324.019531 C 2292.671875 1324.019531 2397.160156 1275.410156 2397.160156 1215.449219 C 2397.160156 1207.269531 2395.21875 1199.300781 2391.53125 1191.628906 L 2391.53125 1182.890625 C 2398.320312 1193.191406 2401.980469 1204.121094 2401.980469 1215.449219 C 2401.980469 1276.648438 2295.328125 1326.261719 2163.769531 1326.261719 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 2163.769531 1324.019531 C 2034.871094 1324.019531 1930.378906 1275.410156 1930.378906 1215.449219 C 1930.378906 1160.601562 2017.820312 1115.25 2131.410156 1107.921875 L 2134.871094 1109.96875 C 2022.28125 1116.578125 1935.191406 1161.289062 1935.191406 1215.449219 C 1935.191406 1274.179688 2037.53125 1321.78125 2163.769531 1321.78125 C 2290.011719 1321.78125 2392.351562 1274.179688 2392.351562 1215.449219 C 2392.351562 1211.890625 2391.96875 1208.359375 2391.230469 1204.878906 C 2391.421875 1204.039062 2391.53125 1203.199219 2391.53125 1202.371094 L 2391.53125 1191.628906 C 2395.21875 1199.300781 2397.160156 1207.269531 2397.160156 1215.449219 C 2397.160156 1275.410156 2292.671875 1324.019531 2163.769531 1324.019531 "/>
<path fill-rule="nonzero" fill="rgb(95.3125%, 95.701599%, 95.701599%)" fill-opacity="1" d="M 2163.769531 1321.78125 C 2037.53125 1321.78125 1935.191406 1274.179688 1935.191406 1215.449219 C 1935.191406 1161.289062 2022.28125 1116.578125 2134.871094 1109.96875 L 2138.339844 1112.03125 C 2026.738281 1117.898438 1940.011719 1161.96875 1940.011719 1215.449219 C 1940.011719 1272.941406 2040.191406 1319.539062 2163.769531 1319.539062 C 2287.351562 1319.539062 2387.53125 1272.941406 2387.53125 1215.449219 C 2387.53125 1214.410156 2387.5 1213.371094 2387.429688 1212.328125 C 2389.328125 1209.871094 2390.671875 1207.351562 2391.230469 1204.878906 C 2391.96875 1208.359375 2392.351562 1211.890625 2392.351562 1215.449219 C 2392.351562 1274.179688 2290.011719 1321.78125 2163.769531 1321.78125 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 95.3125%, 95.701599%)" fill-opacity="1" d="M 2163.769531 1319.539062 C 2040.191406 1319.539062 1940.011719 1272.941406 1940.011719 1215.449219 C 1940.011719 1161.96875 2026.738281 1117.898438 2138.339844 1112.03125 L 2141.851562 1114.109375 C 2031.21875 1119.21875 1944.820312 1162.640625 1944.820312 1215.449219 C 1944.820312 1271.699219 2042.851562 1317.300781 2163.769531 1317.300781 C 2283.320312 1317.300781 2380.488281 1272.730469 2382.679688 1217.371094 C 2384.488281 1215.75 2386.101562 1214.050781 2387.429688 1212.328125 C 2387.5 1213.371094 2387.53125 1214.410156 2387.53125 1215.449219 C 2387.53125 1272.941406 2287.351562 1319.539062 2163.769531 1319.539062 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 94.921875%, 95.3125%)" fill-opacity="1" d="M 2163.769531 1317.300781 C 2042.851562 1317.300781 1944.820312 1271.699219 1944.820312 1215.449219 C 1944.820312 1162.640625 2031.21875 1119.21875 2141.851562 1114.109375 L 2145.390625 1116.210938 C 2035.730469 1120.550781 1949.628906 1163.320312 1949.628906 1215.449219 C 1949.628906 1270.460938 2045.511719 1315.058594 2163.769531 1315.058594 C 2277.738281 1315.058594 2370.910156 1273.640625 2377.53125 1221.398438 C 2379.359375 1220.128906 2381.101562 1218.78125 2382.679688 1217.371094 C 2380.488281 1272.730469 2283.320312 1317.300781 2163.769531 1317.300781 "/>
<path fill-rule="nonzero" fill="rgb(94.53125%, 94.53125%, 94.921875%)" fill-opacity="1" d="M 2163.769531 1315.058594 C 2045.511719 1315.058594 1949.628906 1270.460938 1949.628906 1215.449219 C 1949.628906 1163.320312 2035.730469 1120.550781 2145.390625 1116.210938 L 2148.960938 1118.328125 C 2040.269531 1121.859375 1954.449219 1164 1954.449219 1215.449219 C 1954.449219 1269.230469 2048.171875 1312.820312 2163.769531 1312.820312 C 2272.679688 1312.820312 2362.160156 1274.140625 2372.160156 1224.691406 C 2374.011719 1223.691406 2375.808594 1222.578125 2377.53125 1221.398438 C 2370.910156 1273.640625 2277.738281 1315.058594 2163.769531 1315.058594 "/>
<path fill-rule="nonzero" fill="rgb(94.139099%, 94.139099%, 94.53125%)" fill-opacity="1" d="M 2163.769531 1312.820312 C 2048.171875 1312.820312 1954.449219 1269.230469 1954.449219 1215.449219 C 1954.449219 1164 2040.269531 1121.859375 2148.960938 1118.328125 L 2152.570312 1120.46875 C 2044.828125 1123.171875 1959.269531 1164.660156 1959.269531 1215.449219 C 1959.269531 1267.988281 2050.820312 1310.578125 2163.769531 1310.578125 C 2268.101562 1310.578125 2354.179688 1274.238281 2366.699219 1227.289062 C 2368.539062 1226.539062 2370.371094 1225.660156 2372.160156 1224.691406 C 2362.160156 1274.140625 2272.679688 1312.820312 2163.769531 1312.820312 "/>
<path fill-rule="nonzero" fill="rgb(93.75%, 93.75%, 94.139099%)" fill-opacity="1" d="M 2163.769531 1310.578125 C 2050.820312 1310.578125 1959.269531 1267.988281 1959.269531 1215.449219 C 1959.269531 1164.660156 2044.828125 1123.171875 2152.570312 1120.46875 L 2156.21875 1122.628906 C 2049.429688 1124.46875 1964.078125 1165.328125 1964.078125 1215.449219 C 1964.078125 1266.75 2053.480469 1308.339844 2163.769531 1308.339844 C 2264.039062 1308.339844 2347.050781 1273.96875 2361.300781 1229.171875 C 2363.078125 1228.671875 2364.890625 1228.039062 2366.699219 1227.289062 C 2354.179688 1274.238281 2268.101562 1310.578125 2163.769531 1310.578125 "/>
<path fill-rule="nonzero" fill="rgb(93.359375%, 93.75%, 94.139099%)" fill-opacity="1" d="M 2163.769531 1308.339844 C 2053.480469 1308.339844 1964.078125 1266.75 1964.078125 1215.449219 C 1964.078125 1165.328125 2049.429688 1124.46875 2156.21875 1122.628906 L 2159.921875 1124.820312 C 2054.070312 1125.78125 1968.898438 1165.988281 1968.898438 1215.449219 C 1968.898438 1265.519531 2056.140625 1306.101562 2163.769531 1306.101562 C 2259.808594 1306.101562 2339.628906 1273.78125 2355.699219 1231.238281 C 2356.539062 1230.859375 2357.359375 1230.410156 2358.140625 1229.910156 C 2359.171875 1229.710938 2360.230469 1229.46875 2361.300781 1229.171875 C 2347.050781 1273.96875 2264.039062 1308.339844 2163.769531 1308.339844 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 93.359375%, 93.75%)" fill-opacity="1" d="M 2163.769531 1306.101562 C 2056.140625 1306.101562 1968.898438 1265.519531 1968.898438 1215.449219 C 1968.898438 1165.988281 2054.070312 1125.78125 2159.921875 1124.820312 L 2163.671875 1127.050781 C 2058.75 1127.070312 1973.710938 1166.640625 1973.710938 1215.449219 C 1973.710938 1264.28125 2058.800781 1303.859375 2163.769531 1303.859375 C 2255.929688 1303.859375 2332.78125 1273.339844 2350.148438 1232.851562 C 2352.078125 1232.589844 2353.949219 1232.039062 2355.699219 1231.238281 C 2339.628906 1273.78125 2259.808594 1306.101562 2163.769531 1306.101562 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 92.96875%, 93.359375%)" fill-opacity="1" d="M 2163.769531 1303.859375 C 2058.800781 1303.859375 1973.710938 1264.28125 1973.710938 1215.449219 C 1973.710938 1166.640625 2058.75 1127.070312 2163.671875 1127.050781 L 2167.46875 1129.300781 C 2166.238281 1129.289062 2165.011719 1129.28125 2163.769531 1129.28125 C 2061.460938 1129.28125 1978.53125 1167.859375 1978.53125 1215.449219 C 1978.53125 1263.039062 2061.460938 1301.621094 2163.769531 1301.621094 C 2253.261719 1301.621094 2327.921875 1272.109375 2345.230469 1232.859375 C 2346.03125 1232.960938 2346.839844 1233.011719 2347.628906 1233.011719 C 2348.480469 1233.011719 2349.320312 1232.960938 2350.148438 1232.851562 C 2332.78125 1273.339844 2255.929688 1303.859375 2163.769531 1303.859375 "/>
<path fill-rule="nonzero" fill="rgb(92.576599%, 92.576599%, 92.96875%)" fill-opacity="1" d="M 2163.769531 1301.621094 C 2061.460938 1301.621094 1978.53125 1263.039062 1978.53125 1215.449219 C 1978.53125 1167.859375 2061.460938 1129.28125 2163.769531 1129.28125 C 2165.011719 1129.28125 2166.238281 1129.289062 2167.46875 1129.300781 L 2171.351562 1131.601562 C 2168.839844 1131.550781 2166.300781 1131.519531 2163.769531 1131.519531 C 2064.121094 1131.519531 1983.339844 1169.101562 1983.339844 1215.449219 C 1983.339844 1261.800781 2064.121094 1299.378906 2163.769531 1299.378906 C 2251.429688 1299.378906 2324.5 1270.300781 2340.800781 1231.738281 C 2342.261719 1232.300781 2343.75 1232.671875 2345.230469 1232.859375 C 2327.921875 1272.109375 2253.261719 1301.621094 2163.769531 1301.621094 "/>
<path fill-rule="nonzero" fill="rgb(92.1875%, 92.1875%, 92.96875%)" fill-opacity="1" d="M 2163.769531 1299.378906 C 2064.121094 1299.378906 1983.339844 1261.800781 1983.339844 1215.449219 C 1983.339844 1169.101562 2064.121094 1131.519531 2163.769531 1131.519531 C 2166.300781 1131.519531 2168.839844 1131.550781 2171.351562 1131.601562 L 2175.289062 1133.941406 C 2171.480469 1133.820312 2167.640625 1133.761719 2163.769531 1133.761719 C 2066.78125 1133.761719 1988.160156 1170.339844 1988.160156 1215.449219 C 1988.160156 1260.570312 2066.78125 1297.140625 2163.769531 1297.140625 C 2250.339844 1297.140625 2322.289062 1268 2336.738281 1229.660156 L 2337.910156 1230.351562 C 2338.859375 1230.910156 2339.828125 1231.371094 2340.800781 1231.738281 C 2324.5 1270.300781 2251.429688 1299.378906 2163.769531 1299.378906 "/>
<path fill-rule="nonzero" fill="rgb(91.796875%, 91.796875%, 92.576599%)" fill-opacity="1" d="M 2163.769531 1297.140625 C 2066.78125 1297.140625 1988.160156 1260.570312 1988.160156 1215.449219 C 1988.160156 1170.339844 2066.78125 1133.761719 2163.769531 1133.761719 C 2167.640625 1133.761719 2171.480469 1133.820312 2175.289062 1133.941406 L 2179.328125 1136.328125 C 2174.199219 1136.109375 2169.011719 1136 2163.769531 1136 C 2069.441406 1136 1992.96875 1171.570312 1992.96875 1215.449219 C 1992.96875 1259.328125 2069.441406 1294.898438 2163.769531 1294.898438 C 2249.46875 1294.898438 2320.429688 1265.539062 2332.691406 1227.261719 L 2336.738281 1229.660156 C 2322.289062 1268 2250.339844 1297.140625 2163.769531 1297.140625 "/>
<path fill-rule="nonzero" fill="rgb(91.40625%, 91.796875%, 92.1875%)" fill-opacity="1" d="M 2163.769531 1294.898438 C 2069.441406 1294.898438 1992.96875 1259.328125 1992.96875 1215.449219 C 1992.96875 1171.570312 2069.441406 1136 2163.769531 1136 C 2169.011719 1136 2174.199219 1136.109375 2179.328125 1136.328125 L 2332.691406 1227.261719 C 2320.429688 1265.539062 2249.46875 1294.898438 2163.769531 1294.898438 "/>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 1602.761719 1422.308594 C 1436.628906 1422.308594 1301.949219 1359.660156 1301.949219 1282.390625 C 1301.949219 1205.109375 1436.628906 1142.460938 1602.761719 1142.460938 C 1713.898438 1142.460938 1810.960938 1170.5 1863.039062 1212.199219 C 1862.988281 1213.28125 1862.960938 1214.359375 1862.960938 1215.449219 C 1862.960938 1215.871094 1862.96875 1216.300781 1862.980469 1216.71875 C 1812.839844 1173.820312 1715.109375 1144.699219 1602.761719 1144.699219 C 1439.289062 1144.699219 1306.769531 1206.351562 1306.769531 1282.390625 C 1306.769531 1358.429688 1439.289062 1420.070312 1602.761719 1420.070312 C 1766.230469 1420.070312 1898.75 1358.429688 1898.75 1282.390625 C 1898.75 1282.160156 1898.75 1281.929688 1898.75 1281.699219 C 1900.28125 1283.03125 1901.859375 1284.339844 1903.488281 1285.640625 C 1899.761719 1361.410156 1766.550781 1422.308594 1602.761719 1422.308594 "/>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 1903.488281 1285.640625 C 1901.859375 1284.339844 1900.28125 1283.03125 1898.75 1281.699219 C 1898.730469 1279.988281 1898.640625 1278.289062 1898.488281 1276.601562 C 1900.121094 1278.121094 1901.808594 1279.628906 1903.550781 1281.121094 C 1903.558594 1281.539062 1903.570312 1281.96875 1903.570312 1282.390625 C 1903.570312 1283.480469 1903.539062 1284.558594 1903.488281 1285.640625 M 1868.039062 1221.238281 C 1866.410156 1219.71875 1864.71875 1218.210938 1862.980469 1216.71875 C 1862.96875 1216.300781 1862.960938 1215.871094 1862.960938 1215.449219 C 1862.960938 1214.359375 1862.988281 1213.28125 1863.039062 1212.199219 C 1864.671875 1213.5 1866.25 1214.808594 1867.78125 1216.140625 C 1867.800781 1217.851562 1867.878906 1219.550781 1868.039062 1221.238281 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 99.609375%, 99.609375%)" fill-opacity="1" d="M 1903.550781 1281.121094 C 1901.808594 1279.628906 1900.121094 1278.121094 1898.488281 1276.601562 C 1898.308594 1274.558594 1898.03125 1272.53125 1897.671875 1270.519531 C 1899.441406 1272.378906 1901.308594 1274.210938 1903.261719 1276.019531 C 1903.421875 1277.71875 1903.519531 1279.421875 1903.550781 1281.121094 M 1873.589844 1226.75 C 1871.828125 1224.898438 1869.96875 1223.058594 1868.039062 1221.238281 C 1867.878906 1219.550781 1867.800781 1217.851562 1867.78125 1216.140625 C 1869.511719 1217.628906 1871.191406 1219.148438 1872.808594 1220.679688 C 1872.96875 1222.71875 1873.230469 1224.738281 1873.589844 1226.75 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.609375%)" fill-opacity="1" d="M 1903.261719 1276.019531 C 1901.308594 1274.210938 1899.441406 1272.378906 1897.671875 1270.519531 C 1897.160156 1267.75 1896.480469 1265.019531 1895.621094 1262.308594 C 1897.710938 1264.890625 1899.96875 1267.441406 1902.390625 1269.949219 C 1902.78125 1271.960938 1903.058594 1273.980469 1903.261719 1276.019531 M 1880.289062 1234.410156 C 1878.21875 1231.820312 1875.988281 1229.269531 1873.589844 1226.75 C 1873.230469 1224.738281 1872.96875 1222.71875 1872.808594 1220.679688 C 1874.730469 1222.5 1876.570312 1224.328125 1878.328125 1226.191406 C 1878.800781 1228.960938 1879.460938 1231.699219 1880.289062 1234.410156 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 1902.390625 1269.949219 C 1899.96875 1267.441406 1897.710938 1264.890625 1895.621094 1262.308594 C 1892.589844 1252.648438 1887.398438 1243.308594 1880.289062 1234.410156 C 1879.460938 1231.699219 1878.800781 1228.960938 1878.328125 1226.191406 C 1880.699219 1228.710938 1882.921875 1231.269531 1884.980469 1233.859375 C 1887.910156 1243.53125 1893.101562 1252.871094 1900.308594 1261.738281 C 1901.171875 1264.449219 1901.871094 1267.191406 1902.390625 1269.949219 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 1900.308594 1261.738281 C 1893.101562 1252.871094 1887.910156 1243.53125 1884.980469 1233.859375 C 1892.058594 1242.761719 1897.25 1252.089844 1900.308594 1261.738281 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 99.609375%, 99.609375%)" fill-opacity="1" d="M 1602.761719 1420.070312 C 1439.289062 1420.070312 1306.769531 1358.429688 1306.769531 1282.390625 C 1306.769531 1206.351562 1439.289062 1144.699219 1602.761719 1144.699219 C 1715.109375 1144.699219 1812.839844 1173.820312 1862.980469 1216.71875 C 1863.011719 1218.421875 1863.109375 1220.121094 1863.269531 1221.820312 C 1815.46875 1177.421875 1716.761719 1146.941406 1602.761719 1146.941406 C 1441.949219 1146.941406 1311.578125 1207.578125 1311.578125 1282.390625 C 1311.578125 1357.191406 1441.949219 1417.828125 1602.761719 1417.828125 C 1763.570312 1417.828125 1893.929688 1357.191406 1893.929688 1282.390625 C 1893.929688 1280.640625 1893.859375 1278.890625 1893.71875 1277.160156 C 1895.339844 1278.691406 1897.019531 1280.199219 1898.75 1281.699219 C 1898.75 1281.929688 1898.75 1282.160156 1898.75 1282.390625 C 1898.75 1358.429688 1766.230469 1420.070312 1602.761719 1420.070312 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 99.609375%, 99.609375%)" fill-opacity="1" d="M 1898.75 1281.699219 C 1897.019531 1280.199219 1895.339844 1278.691406 1893.71875 1277.160156 C 1893.558594 1275.121094 1893.289062 1273.101562 1892.941406 1271.078125 C 1894.699219 1272.941406 1896.558594 1274.78125 1898.488281 1276.601562 C 1898.640625 1278.289062 1898.730469 1279.988281 1898.75 1281.699219 M 1868.859375 1227.320312 C 1867.078125 1225.460938 1865.21875 1223.628906 1863.269531 1221.820312 C 1863.109375 1220.121094 1863.011719 1218.421875 1862.980469 1216.71875 C 1864.71875 1218.210938 1866.410156 1219.71875 1868.039062 1221.238281 C 1868.21875 1223.28125 1868.488281 1225.308594 1868.859375 1227.320312 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 1898.488281 1276.601562 C 1896.558594 1274.78125 1894.699219 1272.941406 1892.941406 1271.078125 C 1892.441406 1268.320312 1891.769531 1265.578125 1890.929688 1262.871094 C 1893.019531 1265.460938 1895.261719 1268 1897.671875 1270.519531 C 1898.03125 1272.53125 1898.308594 1274.558594 1898.488281 1276.601562 M 1875.601562 1234.96875 C 1873.511719 1232.378906 1871.269531 1229.828125 1868.859375 1227.320312 C 1868.488281 1225.308594 1868.21875 1223.28125 1868.039062 1221.238281 C 1869.96875 1223.058594 1871.828125 1224.898438 1873.589844 1226.75 C 1874.078125 1229.519531 1874.75 1232.261719 1875.601562 1234.96875 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 99.21875%)" fill-opacity="1" d="M 1897.671875 1270.519531 C 1895.261719 1268 1893.019531 1265.460938 1890.929688 1262.871094 C 1887.929688 1253.199219 1882.738281 1243.859375 1875.601562 1234.96875 C 1874.75 1232.261719 1874.078125 1229.519531 1873.589844 1226.75 C 1875.988281 1229.269531 1878.21875 1231.820312 1880.289062 1234.410156 C 1883.25 1244.078125 1888.449219 1253.421875 1895.621094 1262.308594 C 1896.480469 1265.019531 1897.160156 1267.75 1897.671875 1270.519531 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 1895.621094 1262.308594 C 1888.449219 1253.421875 1883.25 1244.078125 1880.289062 1234.410156 C 1887.398438 1243.308594 1892.589844 1252.648438 1895.621094 1262.308594 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.609375%)" fill-opacity="1" d="M 1602.761719 1417.828125 C 1441.949219 1417.828125 1311.578125 1357.191406 1311.578125 1282.390625 C 1311.578125 1207.578125 1441.949219 1146.941406 1602.761719 1146.941406 C 1716.761719 1146.941406 1815.46875 1177.421875 1863.269531 1221.820312 C 1863.460938 1223.859375 1863.75 1225.871094 1864.128906 1227.890625 C 1819.359375 1181.488281 1719.171875 1149.179688 1602.761719 1149.179688 C 1444.609375 1149.179688 1316.398438 1208.820312 1316.398438 1282.390625 C 1316.398438 1355.949219 1444.609375 1415.589844 1602.761719 1415.589844 C 1760.910156 1415.589844 1889.121094 1355.949219 1889.121094 1282.390625 C 1889.121094 1278.769531 1888.808594 1275.191406 1888.199219 1271.648438 C 1889.960938 1273.511719 1891.800781 1275.339844 1893.71875 1277.160156 C 1893.859375 1278.890625 1893.929688 1280.640625 1893.929688 1282.390625 C 1893.929688 1357.191406 1763.570312 1417.828125 1602.761719 1417.828125 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.609375%)" fill-opacity="1" d="M 1893.71875 1277.160156 C 1891.800781 1275.339844 1889.960938 1273.511719 1888.199219 1271.648438 C 1887.730469 1268.878906 1887.070312 1266.140625 1886.238281 1263.429688 C 1888.308594 1266.019531 1890.539062 1268.570312 1892.941406 1271.078125 C 1893.289062 1273.101562 1893.558594 1275.121094 1893.71875 1277.160156 M 1870.898438 1235.53125 C 1868.808594 1232.941406 1866.558594 1230.398438 1864.128906 1227.890625 C 1863.75 1225.871094 1863.460938 1223.859375 1863.269531 1221.820312 C 1865.21875 1223.628906 1867.078125 1225.460938 1868.859375 1227.320312 C 1869.371094 1230.078125 1870.050781 1232.820312 1870.898438 1235.53125 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 99.21875%)" fill-opacity="1" d="M 1892.941406 1271.078125 C 1890.539062 1268.570312 1888.308594 1266.019531 1886.238281 1263.429688 C 1883.28125 1253.75 1878.078125 1244.421875 1870.898438 1235.53125 C 1870.050781 1232.820312 1869.371094 1230.078125 1868.859375 1227.320312 C 1871.269531 1229.828125 1873.511719 1232.378906 1875.601562 1234.96875 C 1878.589844 1244.640625 1883.789062 1253.980469 1890.929688 1262.871094 C 1891.769531 1265.578125 1892.441406 1268.320312 1892.941406 1271.078125 "/>
<path fill-rule="nonzero" fill="rgb(98.4375%, 98.4375%, 99.21875%)" fill-opacity="1" d="M 1890.929688 1262.871094 C 1883.789062 1253.980469 1878.589844 1244.640625 1875.601562 1234.96875 C 1882.738281 1243.859375 1887.929688 1253.199219 1890.929688 1262.871094 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 1602.761719 1415.589844 C 1444.609375 1415.589844 1316.398438 1355.949219 1316.398438 1282.390625 C 1316.398438 1208.820312 1444.609375 1149.179688 1602.761719 1149.179688 C 1719.171875 1149.179688 1819.359375 1181.488281 1864.128906 1227.890625 C 1864.660156 1230.648438 1865.351562 1233.390625 1866.210938 1236.101562 C 1826.011719 1186.609375 1723.21875 1151.421875 1602.761719 1151.421875 C 1447.269531 1151.421875 1321.210938 1210.058594 1321.210938 1282.390625 C 1321.210938 1354.71875 1447.269531 1413.351562 1602.761719 1413.351562 C 1758.25 1413.351562 1884.300781 1354.71875 1884.300781 1282.390625 C 1884.300781 1276.140625 1883.359375 1269.988281 1881.550781 1263.980469 C 1883.609375 1266.570312 1885.828125 1269.128906 1888.199219 1271.648438 C 1888.808594 1275.191406 1889.121094 1278.769531 1889.121094 1282.390625 C 1889.121094 1355.949219 1760.910156 1415.589844 1602.761719 1415.589844 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 1888.199219 1271.648438 C 1885.828125 1269.128906 1883.609375 1266.570312 1881.550781 1263.980469 C 1878.621094 1254.308594 1873.421875 1244.96875 1866.210938 1236.101562 C 1865.351562 1233.390625 1864.660156 1230.648438 1864.128906 1227.890625 C 1866.558594 1230.398438 1868.808594 1232.941406 1870.898438 1235.53125 C 1873.941406 1245.191406 1879.128906 1254.53125 1886.238281 1263.429688 C 1887.070312 1266.140625 1887.730469 1268.878906 1888.199219 1271.648438 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 1886.238281 1263.429688 C 1879.128906 1254.53125 1873.941406 1245.191406 1870.898438 1235.53125 C 1878.078125 1244.421875 1883.28125 1253.75 1886.238281 1263.429688 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 1602.761719 1413.351562 C 1447.269531 1413.351562 1321.210938 1354.71875 1321.210938 1282.390625 C 1321.210938 1210.058594 1447.269531 1151.421875 1602.761719 1151.421875 C 1723.21875 1151.421875 1826.011719 1186.609375 1866.210938 1236.101562 C 1869.28125 1245.75 1874.46875 1255.078125 1881.550781 1263.980469 C 1883.359375 1269.988281 1884.300781 1276.140625 1884.300781 1282.390625 C 1884.300781 1354.71875 1758.25 1413.351562 1602.761719 1413.351562 M 1602.761719 1153.660156 C 1449.929688 1153.660156 1326.03125 1211.289062 1326.03125 1282.390625 C 1326.03125 1353.480469 1449.929688 1411.109375 1602.761719 1411.109375 C 1755.589844 1411.109375 1879.488281 1353.480469 1879.488281 1282.390625 C 1879.488281 1211.289062 1755.589844 1153.660156 1602.761719 1153.660156 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 1881.550781 1263.980469 C 1874.46875 1255.078125 1869.28125 1245.75 1866.210938 1236.101562 C 1873.421875 1244.96875 1878.621094 1254.308594 1881.550781 1263.980469 "/>
<path fill-rule="nonzero" fill="rgb(98.4375%, 98.4375%, 98.4375%)" fill-opacity="1" d="M 1602.761719 1411.109375 C 1449.929688 1411.109375 1326.03125 1353.480469 1326.03125 1282.390625 C 1326.03125 1211.289062 1449.929688 1153.660156 1602.761719 1153.660156 C 1755.589844 1153.660156 1879.488281 1211.289062 1879.488281 1282.390625 C 1879.488281 1353.480469 1755.589844 1411.109375 1602.761719 1411.109375 M 1602.761719 1155.898438 C 1452.589844 1155.898438 1330.839844 1212.53125 1330.839844 1282.390625 C 1330.839844 1352.238281 1452.589844 1408.871094 1602.761719 1408.871094 C 1752.929688 1408.871094 1874.671875 1352.238281 1874.671875 1282.390625 C 1874.671875 1212.53125 1752.929688 1155.898438 1602.761719 1155.898438 "/>
<path fill-rule="nonzero" fill="rgb(98.046875%, 98.046875%, 98.046875%)" fill-opacity="1" d="M 1602.761719 1408.871094 C 1452.589844 1408.871094 1330.839844 1352.238281 1330.839844 1282.390625 C 1330.839844 1212.53125 1452.589844 1155.898438 1602.761719 1155.898438 C 1752.929688 1155.898438 1874.671875 1212.53125 1874.671875 1282.390625 C 1874.671875 1352.238281 1752.929688 1408.871094 1602.761719 1408.871094 M 1602.761719 1158.140625 C 1455.238281 1158.140625 1335.660156 1213.769531 1335.660156 1282.390625 C 1335.660156 1351 1455.238281 1406.628906 1602.761719 1406.628906 C 1750.269531 1406.628906 1869.859375 1351 1869.859375 1282.390625 C 1869.859375 1213.769531 1750.269531 1158.140625 1602.761719 1158.140625 "/>
<path fill-rule="nonzero" fill="rgb(97.65625%, 97.65625%, 98.046875%)" fill-opacity="1" d="M 1602.761719 1406.628906 C 1455.238281 1406.628906 1335.660156 1351 1335.660156 1282.390625 C 1335.660156 1213.769531 1455.238281 1158.140625 1602.761719 1158.140625 C 1750.269531 1158.140625 1869.859375 1213.769531 1869.859375 1282.390625 C 1869.859375 1351 1750.269531 1406.628906 1602.761719 1406.628906 M 1602.761719 1160.378906 C 1457.898438 1160.378906 1340.480469 1215 1340.480469 1282.390625 C 1340.480469 1349.769531 1457.898438 1404.390625 1602.761719 1404.390625 C 1747.621094 1404.390625 1865.039062 1349.769531 1865.039062 1282.390625 C 1865.039062 1215 1747.621094 1160.378906 1602.761719 1160.378906 "/>
<path fill-rule="nonzero" fill="rgb(97.264099%, 97.264099%, 97.65625%)" fill-opacity="1" d="M 1602.761719 1404.390625 C 1457.898438 1404.390625 1340.480469 1349.769531 1340.480469 1282.390625 C 1340.480469 1215 1457.898438 1160.378906 1602.761719 1160.378906 C 1747.621094 1160.378906 1865.039062 1215 1865.039062 1282.390625 C 1865.039062 1349.769531 1747.621094 1404.390625 1602.761719 1404.390625 M 1602.761719 1162.621094 C 1460.558594 1162.621094 1345.289062 1216.238281 1345.289062 1282.390625 C 1345.289062 1348.53125 1460.558594 1402.148438 1602.761719 1402.148438 C 1744.960938 1402.148438 1860.230469 1348.53125 1860.230469 1282.390625 C 1860.230469 1216.238281 1744.960938 1162.621094 1602.761719 1162.621094 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 97.264099%, 97.264099%)" fill-opacity="1" d="M 1602.761719 1402.148438 C 1460.558594 1402.148438 1345.289062 1348.53125 1345.289062 1282.390625 C 1345.289062 1216.238281 1460.558594 1162.621094 1602.761719 1162.621094 C 1744.960938 1162.621094 1860.230469 1216.238281 1860.230469 1282.390625 C 1860.230469 1348.53125 1744.960938 1402.148438 1602.761719 1402.148438 M 1602.761719 1164.859375 C 1463.21875 1164.859375 1350.109375 1217.480469 1350.109375 1282.390625 C 1350.109375 1347.289062 1463.21875 1399.910156 1602.761719 1399.910156 C 1742.300781 1399.910156 1855.410156 1347.289062 1855.410156 1282.390625 C 1855.410156 1217.480469 1742.300781 1164.859375 1602.761719 1164.859375 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 96.875%, 96.875%)" fill-opacity="1" d="M 1602.761719 1399.910156 C 1463.21875 1399.910156 1350.109375 1347.289062 1350.109375 1282.390625 C 1350.109375 1217.480469 1463.21875 1164.859375 1602.761719 1164.859375 C 1742.300781 1164.859375 1855.410156 1217.480469 1855.410156 1282.390625 C 1855.410156 1347.289062 1742.300781 1399.910156 1602.761719 1399.910156 M 1602.761719 1167.101562 C 1465.878906 1167.101562 1354.921875 1218.71875 1354.921875 1282.390625 C 1354.921875 1346.058594 1465.878906 1397.671875 1602.761719 1397.671875 C 1739.640625 1397.671875 1850.601562 1346.058594 1850.601562 1282.390625 C 1850.601562 1218.71875 1739.640625 1167.101562 1602.761719 1167.101562 "/>
<path fill-rule="nonzero" fill="rgb(96.484375%, 96.484375%, 96.875%)" fill-opacity="1" d="M 1602.761719 1397.671875 C 1465.878906 1397.671875 1354.921875 1346.058594 1354.921875 1282.390625 C 1354.921875 1218.71875 1465.878906 1167.101562 1602.761719 1167.101562 C 1739.640625 1167.101562 1850.601562 1218.71875 1850.601562 1282.390625 C 1850.601562 1346.058594 1739.640625 1397.671875 1602.761719 1397.671875 M 1602.761719 1169.339844 C 1468.539062 1169.339844 1359.738281 1219.949219 1359.738281 1282.390625 C 1359.738281 1344.820312 1468.539062 1395.429688 1602.761719 1395.429688 C 1736.980469 1395.429688 1845.78125 1344.820312 1845.78125 1282.390625 C 1845.78125 1219.949219 1736.980469 1169.339844 1602.761719 1169.339844 "/>
<path fill-rule="nonzero" fill="rgb(96.09375%, 96.09375%, 96.484375%)" fill-opacity="1" d="M 1602.761719 1395.429688 C 1468.539062 1395.429688 1359.738281 1344.820312 1359.738281 1282.390625 C 1359.738281 1219.949219 1468.539062 1169.339844 1602.761719 1169.339844 C 1736.980469 1169.339844 1845.78125 1219.949219 1845.78125 1282.390625 C 1845.78125 1344.820312 1736.980469 1395.429688 1602.761719 1395.429688 M 1602.761719 1171.578125 C 1471.199219 1171.578125 1364.550781 1221.191406 1364.550781 1282.390625 C 1364.550781 1343.578125 1471.199219 1393.191406 1602.761719 1393.191406 C 1734.320312 1393.191406 1840.96875 1343.578125 1840.96875 1282.390625 C 1840.96875 1221.191406 1734.320312 1171.578125 1602.761719 1171.578125 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 1602.761719 1393.191406 C 1471.199219 1393.191406 1364.550781 1343.578125 1364.550781 1282.390625 C 1364.550781 1221.191406 1471.199219 1171.578125 1602.761719 1171.578125 C 1734.320312 1171.578125 1840.96875 1221.191406 1840.96875 1282.390625 C 1840.96875 1343.578125 1734.320312 1393.191406 1602.761719 1393.191406 M 1602.761719 1173.820312 C 1473.859375 1173.820312 1369.371094 1222.429688 1369.371094 1282.390625 C 1369.371094 1342.351562 1473.859375 1390.949219 1602.761719 1390.949219 C 1731.660156 1390.949219 1836.148438 1342.351562 1836.148438 1282.390625 C 1836.148438 1222.429688 1731.660156 1173.820312 1602.761719 1173.820312 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 1602.761719 1390.949219 C 1473.859375 1390.949219 1369.371094 1342.351562 1369.371094 1282.390625 C 1369.371094 1222.429688 1473.859375 1173.820312 1602.761719 1173.820312 C 1731.660156 1173.820312 1836.148438 1222.429688 1836.148438 1282.390625 C 1836.148438 1342.351562 1731.660156 1390.949219 1602.761719 1390.949219 M 1602.761719 1176.058594 C 1476.519531 1176.058594 1374.179688 1223.660156 1374.179688 1282.390625 C 1374.179688 1341.109375 1476.519531 1388.710938 1602.761719 1388.710938 C 1729 1388.710938 1831.339844 1341.109375 1831.339844 1282.390625 C 1831.339844 1223.660156 1729 1176.058594 1602.761719 1176.058594 "/>
<path fill-rule="nonzero" fill="rgb(95.3125%, 95.701599%, 95.701599%)" fill-opacity="1" d="M 1602.761719 1388.710938 C 1476.519531 1388.710938 1374.179688 1341.109375 1374.179688 1282.390625 C 1374.179688 1223.660156 1476.519531 1176.058594 1602.761719 1176.058594 C 1729 1176.058594 1831.339844 1223.660156 1831.339844 1282.390625 C 1831.339844 1341.109375 1729 1388.710938 1602.761719 1388.710938 M 1602.761719 1178.300781 C 1479.179688 1178.300781 1379 1224.898438 1379 1282.390625 C 1379 1339.871094 1479.179688 1386.46875 1602.761719 1386.46875 C 1726.339844 1386.46875 1826.519531 1339.871094 1826.519531 1282.390625 C 1826.519531 1224.898438 1726.339844 1178.300781 1602.761719 1178.300781 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 95.3125%, 95.701599%)" fill-opacity="1" d="M 1602.761719 1386.46875 C 1479.179688 1386.46875 1379 1339.871094 1379 1282.390625 C 1379 1224.898438 1479.179688 1178.300781 1602.761719 1178.300781 C 1726.339844 1178.300781 1826.519531 1224.898438 1826.519531 1282.390625 C 1826.519531 1339.871094 1726.339844 1386.46875 1602.761719 1386.46875 M 1602.761719 1180.539062 C 1481.839844 1180.539062 1383.808594 1226.140625 1383.808594 1282.390625 C 1383.808594 1338.640625 1481.839844 1384.230469 1602.761719 1384.230469 C 1723.679688 1384.230469 1821.710938 1338.640625 1821.710938 1282.390625 C 1821.710938 1226.140625 1723.679688 1180.539062 1602.761719 1180.539062 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 94.921875%, 95.3125%)" fill-opacity="1" d="M 1602.761719 1384.230469 C 1481.839844 1384.230469 1383.808594 1338.640625 1383.808594 1282.390625 C 1383.808594 1226.140625 1481.839844 1180.539062 1602.761719 1180.539062 C 1723.679688 1180.539062 1821.710938 1226.140625 1821.710938 1282.390625 C 1821.710938 1338.640625 1723.679688 1384.230469 1602.761719 1384.230469 M 1602.761719 1182.78125 C 1484.5 1182.78125 1388.628906 1227.378906 1388.628906 1282.390625 C 1388.628906 1337.398438 1484.5 1381.988281 1602.761719 1381.988281 C 1721.019531 1381.988281 1816.890625 1337.398438 1816.890625 1282.390625 C 1816.890625 1227.378906 1721.019531 1182.78125 1602.761719 1182.78125 "/>
<path fill-rule="nonzero" fill="rgb(94.53125%, 94.53125%, 94.921875%)" fill-opacity="1" d="M 1602.761719 1381.988281 C 1484.5 1381.988281 1388.628906 1337.398438 1388.628906 1282.390625 C 1388.628906 1227.378906 1484.5 1182.78125 1602.761719 1182.78125 C 1721.019531 1182.78125 1816.890625 1227.378906 1816.890625 1282.390625 C 1816.890625 1337.398438 1721.019531 1381.988281 1602.761719 1381.988281 M 1602.761719 1185.019531 C 1487.160156 1185.019531 1393.441406 1228.609375 1393.441406 1282.390625 C 1393.441406 1336.160156 1487.160156 1379.75 1602.761719 1379.75 C 1718.359375 1379.75 1812.078125 1336.160156 1812.078125 1282.390625 C 1812.078125 1228.609375 1718.359375 1185.019531 1602.761719 1185.019531 "/>
<path fill-rule="nonzero" fill="rgb(94.139099%, 94.139099%, 94.53125%)" fill-opacity="1" d="M 1602.761719 1379.75 C 1487.160156 1379.75 1393.441406 1336.160156 1393.441406 1282.390625 C 1393.441406 1228.609375 1487.160156 1185.019531 1602.761719 1185.019531 C 1718.359375 1185.019531 1812.078125 1228.609375 1812.078125 1282.390625 C 1812.078125 1336.160156 1718.359375 1379.75 1602.761719 1379.75 M 1602.761719 1187.261719 C 1489.820312 1187.261719 1398.261719 1229.851562 1398.261719 1282.390625 C 1398.261719 1334.921875 1489.820312 1377.511719 1602.761719 1377.511719 C 1715.699219 1377.511719 1807.261719 1334.921875 1807.261719 1282.390625 C 1807.261719 1229.851562 1715.699219 1187.261719 1602.761719 1187.261719 "/>
<path fill-rule="nonzero" fill="rgb(93.75%, 93.75%, 94.139099%)" fill-opacity="1" d="M 1602.761719 1377.511719 C 1489.820312 1377.511719 1398.261719 1334.921875 1398.261719 1282.390625 C 1398.261719 1229.851562 1489.820312 1187.261719 1602.761719 1187.261719 C 1715.699219 1187.261719 1807.261719 1229.851562 1807.261719 1282.390625 C 1807.261719 1334.921875 1715.699219 1377.511719 1602.761719 1377.511719 M 1602.761719 1189.5 C 1492.46875 1189.5 1403.070312 1231.089844 1403.070312 1282.390625 C 1403.070312 1333.691406 1492.46875 1375.269531 1602.761719 1375.269531 C 1713.039062 1375.269531 1802.449219 1333.691406 1802.449219 1282.390625 C 1802.449219 1231.089844 1713.039062 1189.5 1602.761719 1189.5 "/>
<path fill-rule="nonzero" fill="rgb(93.359375%, 93.75%, 94.139099%)" fill-opacity="1" d="M 1602.761719 1375.269531 C 1492.46875 1375.269531 1403.070312 1333.691406 1403.070312 1282.390625 C 1403.070312 1231.089844 1492.46875 1189.5 1602.761719 1189.5 C 1713.039062 1189.5 1802.449219 1231.089844 1802.449219 1282.390625 C 1802.449219 1333.691406 1713.039062 1375.269531 1602.761719 1375.269531 M 1602.761719 1191.738281 C 1495.128906 1191.738281 1407.890625 1232.320312 1407.890625 1282.390625 C 1407.890625 1332.449219 1495.128906 1373.03125 1602.761719 1373.03125 C 1710.390625 1373.03125 1797.628906 1332.449219 1797.628906 1282.390625 C 1797.628906 1232.320312 1710.390625 1191.738281 1602.761719 1191.738281 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 93.359375%, 93.75%)" fill-opacity="1" d="M 1602.761719 1373.03125 C 1495.128906 1373.03125 1407.890625 1332.449219 1407.890625 1282.390625 C 1407.890625 1232.320312 1495.128906 1191.738281 1602.761719 1191.738281 C 1710.390625 1191.738281 1797.628906 1232.320312 1797.628906 1282.390625 C 1797.628906 1332.449219 1710.390625 1373.03125 1602.761719 1373.03125 M 1602.761719 1193.980469 C 1497.789062 1193.980469 1412.699219 1233.558594 1412.699219 1282.390625 C 1412.699219 1331.210938 1497.789062 1370.789062 1602.761719 1370.789062 C 1707.730469 1370.789062 1792.820312 1331.210938 1792.820312 1282.390625 C 1792.820312 1233.558594 1707.730469 1193.980469 1602.761719 1193.980469 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 92.96875%, 93.359375%)" fill-opacity="1" d="M 1602.761719 1370.789062 C 1497.789062 1370.789062 1412.699219 1331.210938 1412.699219 1282.390625 C 1412.699219 1233.558594 1497.789062 1193.980469 1602.761719 1193.980469 C 1707.730469 1193.980469 1792.820312 1233.558594 1792.820312 1282.390625 C 1792.820312 1331.210938 1707.730469 1370.789062 1602.761719 1370.789062 M 1602.761719 1196.21875 C 1500.449219 1196.21875 1417.519531 1234.800781 1417.519531 1282.390625 C 1417.519531 1329.980469 1500.449219 1368.550781 1602.761719 1368.550781 C 1705.070312 1368.550781 1788 1329.980469 1788 1282.390625 C 1788 1234.800781 1705.070312 1196.21875 1602.761719 1196.21875 "/>
<path fill-rule="nonzero" fill="rgb(92.576599%, 92.576599%, 92.96875%)" fill-opacity="1" d="M 1602.761719 1368.550781 C 1500.449219 1368.550781 1417.519531 1329.980469 1417.519531 1282.390625 C 1417.519531 1234.800781 1500.449219 1196.21875 1602.761719 1196.21875 C 1705.070312 1196.21875 1788 1234.800781 1788 1282.390625 C 1788 1329.980469 1705.070312 1368.550781 1602.761719 1368.550781 M 1602.761719 1198.460938 C 1503.109375 1198.460938 1422.328125 1236.03125 1422.328125 1282.390625 C 1422.328125 1328.738281 1503.109375 1366.308594 1602.761719 1366.308594 C 1702.410156 1366.308594 1783.191406 1328.738281 1783.191406 1282.390625 C 1783.191406 1236.03125 1702.410156 1198.460938 1602.761719 1198.460938 "/>
<path fill-rule="nonzero" fill="rgb(92.1875%, 92.1875%, 92.96875%)" fill-opacity="1" d="M 1602.761719 1366.308594 C 1503.109375 1366.308594 1422.328125 1328.738281 1422.328125 1282.390625 C 1422.328125 1236.03125 1503.109375 1198.460938 1602.761719 1198.460938 C 1702.410156 1198.460938 1783.191406 1236.03125 1783.191406 1282.390625 C 1783.191406 1328.738281 1702.410156 1366.308594 1602.761719 1366.308594 M 1602.761719 1200.699219 C 1505.769531 1200.699219 1427.148438 1237.269531 1427.148438 1282.390625 C 1427.148438 1327.5 1505.769531 1364.070312 1602.761719 1364.070312 C 1699.75 1364.070312 1778.371094 1327.5 1778.371094 1282.390625 C 1778.371094 1237.269531 1699.75 1200.699219 1602.761719 1200.699219 "/>
<path fill-rule="nonzero" fill="rgb(91.796875%, 91.796875%, 92.576599%)" fill-opacity="1" d="M 1602.761719 1364.070312 C 1505.769531 1364.070312 1427.148438 1327.5 1427.148438 1282.390625 C 1427.148438 1237.269531 1505.769531 1200.699219 1602.761719 1200.699219 C 1699.75 1200.699219 1778.371094 1237.269531 1778.371094 1282.390625 C 1778.371094 1327.5 1699.75 1364.070312 1602.761719 1364.070312 M 1602.761719 1202.941406 C 1508.429688 1202.941406 1431.960938 1238.511719 1431.960938 1282.390625 C 1431.960938 1326.261719 1508.429688 1361.839844 1602.761719 1361.839844 C 1697.089844 1361.839844 1773.558594 1326.261719 1773.558594 1282.390625 C 1773.558594 1238.511719 1697.089844 1202.941406 1602.761719 1202.941406 "/>
<path fill-rule="nonzero" fill="rgb(91.40625%, 91.796875%, 92.1875%)" fill-opacity="1" d="M 1602.761719 1361.839844 C 1508.429688 1361.839844 1431.960938 1326.261719 1431.960938 1282.390625 C 1431.960938 1238.511719 1508.429688 1202.941406 1602.761719 1202.941406 C 1697.089844 1202.941406 1773.558594 1238.511719 1773.558594 1282.390625 C 1773.558594 1326.261719 1697.089844 1361.839844 1602.761719 1361.839844 "/>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 660.53125 1978.601562 C 440.203125 1978.601562 261.59375 1895.519531 261.59375 1793.03125 C 261.59375 1690.539062 440.203125 1607.460938 660.53125 1607.460938 C 880.859375 1607.460938 1059.46875 1690.539062 1059.46875 1793.03125 C 1059.46875 1895.519531 880.859375 1978.601562 660.53125 1978.601562 M 660.53125 1610.429688 C 443.730469 1610.429688 267.980469 1692.179688 267.980469 1793.03125 C 267.980469 1893.878906 443.730469 1975.628906 660.53125 1975.628906 C 877.332031 1975.628906 1053.078125 1893.878906 1053.078125 1793.03125 C 1053.078125 1692.179688 877.332031 1610.429688 660.53125 1610.429688 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 99.609375%, 99.609375%)" fill-opacity="1" d="M 660.53125 1975.628906 C 443.730469 1975.628906 267.980469 1893.878906 267.980469 1793.03125 C 267.980469 1692.179688 443.730469 1610.429688 660.53125 1610.429688 C 877.332031 1610.429688 1053.078125 1692.179688 1053.078125 1793.03125 C 1053.078125 1893.878906 877.332031 1975.628906 660.53125 1975.628906 M 660.53125 1613.398438 C 447.257812 1613.398438 274.363281 1693.820312 274.363281 1793.03125 C 274.363281 1892.238281 447.257812 1972.660156 660.53125 1972.660156 C 873.804688 1972.660156 1046.699219 1892.238281 1046.699219 1793.03125 C 1046.699219 1693.820312 873.804688 1613.398438 660.53125 1613.398438 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.609375%)" fill-opacity="1" d="M 660.53125 1972.660156 C 447.257812 1972.660156 274.363281 1892.238281 274.363281 1793.03125 C 274.363281 1693.820312 447.257812 1613.398438 660.53125 1613.398438 C 873.804688 1613.398438 1046.699219 1693.820312 1046.699219 1793.03125 C 1046.699219 1892.238281 873.804688 1972.660156 660.53125 1972.660156 M 660.53125 1616.371094 C 450.785156 1616.371094 280.75 1695.460938 280.75 1793.03125 C 280.75 1890.601562 450.785156 1969.691406 660.53125 1969.691406 C 870.277344 1969.691406 1040.308594 1890.601562 1040.308594 1793.03125 C 1040.308594 1695.460938 870.277344 1616.371094 660.53125 1616.371094 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 660.53125 1969.691406 C 450.785156 1969.691406 280.75 1890.601562 280.75 1793.03125 C 280.75 1695.460938 450.785156 1616.371094 660.53125 1616.371094 C 870.277344 1616.371094 1040.308594 1695.460938 1040.308594 1793.03125 C 1040.308594 1890.601562 870.277344 1969.691406 660.53125 1969.691406 M 660.53125 1619.339844 C 454.3125 1619.339844 287.136719 1697.101562 287.136719 1793.03125 C 287.136719 1888.949219 454.3125 1966.71875 660.53125 1966.71875 C 866.75 1966.71875 1033.929688 1888.949219 1033.929688 1793.03125 C 1033.929688 1697.101562 866.75 1619.339844 660.53125 1619.339844 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 660.53125 1966.71875 C 454.3125 1966.71875 287.136719 1888.949219 287.136719 1793.03125 C 287.136719 1697.101562 454.3125 1619.339844 660.53125 1619.339844 C 866.75 1619.339844 1033.929688 1697.101562 1033.929688 1793.03125 C 1033.929688 1888.949219 866.75 1966.71875 660.53125 1966.71875 M 660.53125 1622.308594 C 457.839844 1622.308594 293.523438 1698.738281 293.523438 1793.03125 C 293.523438 1887.308594 457.839844 1963.75 660.53125 1963.75 C 863.226562 1963.75 1027.539062 1887.308594 1027.539062 1793.03125 C 1027.539062 1698.738281 863.226562 1622.308594 660.53125 1622.308594 "/>
<path fill-rule="nonzero" fill="rgb(98.4375%, 98.4375%, 98.4375%)" fill-opacity="1" d="M 660.53125 1963.75 C 457.839844 1963.75 293.523438 1887.308594 293.523438 1793.03125 C 293.523438 1698.738281 457.839844 1622.308594 660.53125 1622.308594 C 863.226562 1622.308594 1027.539062 1698.738281 1027.539062 1793.03125 C 1027.539062 1887.308594 863.226562 1963.75 660.53125 1963.75 M 660.53125 1625.28125 C 461.363281 1625.28125 299.910156 1700.378906 299.910156 1793.03125 C 299.910156 1885.671875 461.363281 1960.78125 660.53125 1960.78125 C 859.695312 1960.78125 1021.148438 1885.671875 1021.148438 1793.03125 C 1021.148438 1700.378906 859.695312 1625.28125 660.53125 1625.28125 "/>
<path fill-rule="nonzero" fill="rgb(98.046875%, 98.046875%, 98.046875%)" fill-opacity="1" d="M 660.53125 1960.78125 C 461.363281 1960.78125 299.910156 1885.671875 299.910156 1793.03125 C 299.910156 1700.378906 461.363281 1625.28125 660.53125 1625.28125 C 859.695312 1625.28125 1021.148438 1700.378906 1021.148438 1793.03125 C 1021.148438 1885.671875 859.695312 1960.78125 660.53125 1960.78125 M 660.53125 1628.25 C 464.890625 1628.25 306.292969 1702.019531 306.292969 1793.03125 C 306.292969 1884.03125 464.890625 1957.808594 660.53125 1957.808594 C 856.171875 1957.808594 1014.769531 1884.03125 1014.769531 1793.03125 C 1014.769531 1702.019531 856.171875 1628.25 660.53125 1628.25 "/>
<path fill-rule="nonzero" fill="rgb(97.65625%, 97.65625%, 98.046875%)" fill-opacity="1" d="M 660.53125 1957.808594 C 464.890625 1957.808594 306.292969 1884.03125 306.292969 1793.03125 C 306.292969 1702.019531 464.890625 1628.25 660.53125 1628.25 C 856.171875 1628.25 1014.769531 1702.019531 1014.769531 1793.03125 C 1014.769531 1884.03125 856.171875 1957.808594 660.53125 1957.808594 M 660.53125 1631.21875 C 468.417969 1631.21875 312.679688 1703.671875 312.679688 1793.03125 C 312.679688 1882.390625 468.417969 1954.839844 660.53125 1954.839844 C 852.644531 1954.839844 1008.378906 1882.390625 1008.378906 1793.03125 C 1008.378906 1703.671875 852.644531 1631.21875 660.53125 1631.21875 "/>
<path fill-rule="nonzero" fill="rgb(97.264099%, 97.264099%, 97.65625%)" fill-opacity="1" d="M 660.53125 1954.839844 C 468.417969 1954.839844 312.679688 1882.390625 312.679688 1793.03125 C 312.679688 1703.671875 468.417969 1631.21875 660.53125 1631.21875 C 852.644531 1631.21875 1008.378906 1703.671875 1008.378906 1793.03125 C 1008.378906 1882.390625 852.644531 1954.839844 660.53125 1954.839844 M 660.53125 1634.191406 C 471.945312 1634.191406 319.066406 1705.308594 319.066406 1793.03125 C 319.066406 1880.75 471.945312 1951.871094 660.53125 1951.871094 C 849.117188 1951.871094 1002 1880.75 1002 1793.03125 C 1002 1705.308594 849.117188 1634.191406 660.53125 1634.191406 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 97.264099%, 97.264099%)" fill-opacity="1" d="M 660.53125 1951.871094 C 471.945312 1951.871094 319.066406 1880.75 319.066406 1793.03125 C 319.066406 1705.308594 471.945312 1634.191406 660.53125 1634.191406 C 849.117188 1634.191406 1002 1705.308594 1002 1793.03125 C 1002 1880.75 849.117188 1951.871094 660.53125 1951.871094 M 660.53125 1637.160156 C 475.472656 1637.160156 325.453125 1706.949219 325.453125 1793.03125 C 325.453125 1879.109375 475.472656 1948.898438 660.53125 1948.898438 C 845.589844 1948.898438 995.609375 1879.109375 995.609375 1793.03125 C 995.609375 1706.949219 845.589844 1637.160156 660.53125 1637.160156 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 96.875%, 96.875%)" fill-opacity="1" d="M 660.53125 1948.898438 C 475.472656 1948.898438 325.453125 1879.109375 325.453125 1793.03125 C 325.453125 1706.949219 475.472656 1637.160156 660.53125 1637.160156 C 845.589844 1637.160156 995.609375 1706.949219 995.609375 1793.03125 C 995.609375 1879.109375 845.589844 1948.898438 660.53125 1948.898438 M 660.53125 1640.128906 C 479 1640.128906 331.839844 1708.589844 331.839844 1793.03125 C 331.839844 1877.46875 479 1945.921875 660.53125 1945.921875 C 842.0625 1945.921875 989.222656 1877.46875 989.222656 1793.03125 C 989.222656 1708.589844 842.0625 1640.128906 660.53125 1640.128906 "/>
<path fill-rule="nonzero" fill="rgb(96.484375%, 96.484375%, 96.875%)" fill-opacity="1" d="M 660.53125 1945.921875 C 479 1945.921875 331.839844 1877.46875 331.839844 1793.03125 C 331.839844 1708.589844 479 1640.128906 660.53125 1640.128906 C 842.0625 1640.128906 989.222656 1708.589844 989.222656 1793.03125 C 989.222656 1877.46875 842.0625 1945.921875 660.53125 1945.921875 M 660.53125 1643.109375 C 482.527344 1643.109375 338.222656 1710.230469 338.222656 1793.03125 C 338.222656 1875.828125 482.527344 1942.949219 660.53125 1942.949219 C 838.535156 1942.949219 982.839844 1875.828125 982.839844 1793.03125 C 982.839844 1710.230469 838.535156 1643.109375 660.53125 1643.109375 "/>
<path fill-rule="nonzero" fill="rgb(96.09375%, 96.09375%, 96.484375%)" fill-opacity="1" d="M 660.53125 1942.949219 C 482.527344 1942.949219 338.222656 1875.828125 338.222656 1793.03125 C 338.222656 1710.230469 482.527344 1643.109375 660.53125 1643.109375 C 838.535156 1643.109375 982.839844 1710.230469 982.839844 1793.03125 C 982.839844 1875.828125 838.535156 1942.949219 660.53125 1942.949219 M 660.53125 1646.070312 C 486.050781 1646.070312 344.609375 1711.871094 344.609375 1793.03125 C 344.609375 1874.191406 486.050781 1939.980469 660.53125 1939.980469 C 835.007812 1939.980469 976.453125 1874.191406 976.453125 1793.03125 C 976.453125 1711.871094 835.007812 1646.070312 660.53125 1646.070312 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 660.53125 1939.980469 C 486.050781 1939.980469 344.609375 1874.191406 344.609375 1793.03125 C 344.609375 1711.871094 486.050781 1646.070312 660.53125 1646.070312 C 835.007812 1646.070312 976.453125 1711.871094 976.453125 1793.03125 C 976.453125 1874.191406 835.007812 1939.980469 660.53125 1939.980469 M 660.53125 1649.050781 C 489.578125 1649.050781 350.996094 1713.511719 350.996094 1793.03125 C 350.996094 1872.550781 489.578125 1937.011719 660.53125 1937.011719 C 831.480469 1937.011719 970.066406 1872.550781 970.066406 1793.03125 C 970.066406 1713.511719 831.480469 1649.050781 660.53125 1649.050781 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 660.53125 1937.011719 C 489.578125 1937.011719 350.996094 1872.550781 350.996094 1793.03125 C 350.996094 1713.511719 489.578125 1649.050781 660.53125 1649.050781 C 831.480469 1649.050781 970.066406 1713.511719 970.066406 1793.03125 C 970.066406 1872.550781 831.480469 1937.011719 660.53125 1937.011719 M 660.53125 1652.019531 C 493.105469 1652.019531 357.382812 1715.148438 357.382812 1793.03125 C 357.382812 1870.910156 493.105469 1934.039062 660.53125 1934.039062 C 827.953125 1934.039062 963.679688 1870.910156 963.679688 1793.03125 C 963.679688 1715.148438 827.953125 1652.019531 660.53125 1652.019531 "/>
<path fill-rule="nonzero" fill="rgb(95.3125%, 95.701599%, 95.701599%)" fill-opacity="1" d="M 660.53125 1934.039062 C 493.105469 1934.039062 357.382812 1870.910156 357.382812 1793.03125 C 357.382812 1715.148438 493.105469 1652.019531 660.53125 1652.019531 C 827.953125 1652.019531 963.679688 1715.148438 963.679688 1793.03125 C 963.679688 1870.910156 827.953125 1934.039062 660.53125 1934.039062 M 660.53125 1654.988281 C 496.632812 1654.988281 363.769531 1716.789062 363.769531 1793.03125 C 363.769531 1869.269531 496.632812 1931.070312 660.53125 1931.070312 C 824.429688 1931.070312 957.292969 1869.269531 957.292969 1793.03125 C 957.292969 1716.789062 824.429688 1654.988281 660.53125 1654.988281 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 95.3125%, 95.701599%)" fill-opacity="1" d="M 660.53125 1931.070312 C 496.632812 1931.070312 363.769531 1869.269531 363.769531 1793.03125 C 363.769531 1716.789062 496.632812 1654.988281 660.53125 1654.988281 C 824.429688 1654.988281 957.292969 1716.789062 957.292969 1793.03125 C 957.292969 1869.269531 824.429688 1931.070312 660.53125 1931.070312 M 660.53125 1657.960938 C 500.160156 1657.960938 370.152344 1718.429688 370.152344 1793.03125 C 370.152344 1867.628906 500.160156 1928.101562 660.53125 1928.101562 C 820.902344 1928.101562 950.90625 1867.628906 950.90625 1793.03125 C 950.90625 1718.429688 820.902344 1657.960938 660.53125 1657.960938 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 94.921875%, 95.3125%)" fill-opacity="1" d="M 660.53125 1928.101562 C 500.160156 1928.101562 370.152344 1867.628906 370.152344 1793.03125 C 370.152344 1718.429688 500.160156 1657.960938 660.53125 1657.960938 C 820.902344 1657.960938 950.90625 1718.429688 950.90625 1793.03125 C 950.90625 1867.628906 820.902344 1928.101562 660.53125 1928.101562 M 660.53125 1660.929688 C 503.6875 1660.929688 376.539062 1720.070312 376.539062 1793.03125 C 376.539062 1865.988281 503.6875 1925.128906 660.53125 1925.128906 C 817.375 1925.128906 944.523438 1865.988281 944.523438 1793.03125 C 944.523438 1720.070312 817.375 1660.929688 660.53125 1660.929688 "/>
<path fill-rule="nonzero" fill="rgb(94.53125%, 94.53125%, 94.921875%)" fill-opacity="1" d="M 660.53125 1925.128906 C 503.6875 1925.128906 376.539062 1865.988281 376.539062 1793.03125 C 376.539062 1720.070312 503.6875 1660.929688 660.53125 1660.929688 C 817.375 1660.929688 944.523438 1720.070312 944.523438 1793.03125 C 944.523438 1865.988281 817.375 1925.128906 660.53125 1925.128906 M 660.53125 1663.898438 C 507.214844 1663.898438 382.925781 1721.710938 382.925781 1793.03125 C 382.925781 1864.351562 507.214844 1922.160156 660.53125 1922.160156 C 813.847656 1922.160156 938.136719 1864.351562 938.136719 1793.03125 C 938.136719 1721.710938 813.847656 1663.898438 660.53125 1663.898438 "/>
<path fill-rule="nonzero" fill="rgb(94.139099%, 94.139099%, 94.53125%)" fill-opacity="1" d="M 660.53125 1922.160156 C 507.214844 1922.160156 382.925781 1864.351562 382.925781 1793.03125 C 382.925781 1721.710938 507.214844 1663.898438 660.53125 1663.898438 C 813.847656 1663.898438 938.136719 1721.710938 938.136719 1793.03125 C 938.136719 1864.351562 813.847656 1922.160156 660.53125 1922.160156 M 660.53125 1666.871094 C 510.742188 1666.871094 389.3125 1723.351562 389.3125 1793.03125 C 389.3125 1862.710938 510.742188 1919.191406 660.53125 1919.191406 C 810.320312 1919.191406 931.75 1862.710938 931.75 1793.03125 C 931.75 1723.351562 810.320312 1666.871094 660.53125 1666.871094 "/>
<path fill-rule="nonzero" fill="rgb(93.75%, 93.75%, 94.139099%)" fill-opacity="1" d="M 660.53125 1919.191406 C 510.742188 1919.191406 389.3125 1862.710938 389.3125 1793.03125 C 389.3125 1723.351562 510.742188 1666.871094 660.53125 1666.871094 C 810.320312 1666.871094 931.75 1723.351562 931.75 1793.03125 C 931.75 1862.710938 810.320312 1919.191406 660.53125 1919.191406 M 660.53125 1669.839844 C 514.265625 1669.839844 395.695312 1724.988281 395.695312 1793.03125 C 395.695312 1861.070312 514.265625 1916.21875 660.53125 1916.21875 C 806.792969 1916.21875 925.363281 1861.070312 925.363281 1793.03125 C 925.363281 1724.988281 806.792969 1669.839844 660.53125 1669.839844 "/>
<path fill-rule="nonzero" fill="rgb(93.359375%, 93.75%, 94.139099%)" fill-opacity="1" d="M 660.53125 1916.21875 C 514.265625 1916.21875 395.695312 1861.070312 395.695312 1793.03125 C 395.695312 1724.988281 514.265625 1669.839844 660.53125 1669.839844 C 806.792969 1669.839844 925.363281 1724.988281 925.363281 1793.03125 C 925.363281 1861.070312 806.792969 1916.21875 660.53125 1916.21875 M 660.53125 1672.808594 C 517.796875 1672.808594 402.085938 1726.628906 402.085938 1793.03125 C 402.085938 1859.421875 517.796875 1913.25 660.53125 1913.25 C 803.269531 1913.25 918.976562 1859.421875 918.976562 1793.03125 C 918.976562 1726.628906 803.269531 1672.808594 660.53125 1672.808594 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 93.359375%, 93.75%)" fill-opacity="1" d="M 660.53125 1913.25 C 517.796875 1913.25 402.085938 1859.421875 402.085938 1793.03125 C 402.085938 1726.628906 517.796875 1672.808594 660.53125 1672.808594 C 803.269531 1672.808594 918.976562 1726.628906 918.976562 1793.03125 C 918.976562 1859.421875 803.269531 1913.25 660.53125 1913.25 M 660.53125 1675.78125 C 521.320312 1675.78125 408.46875 1728.269531 408.46875 1793.03125 C 408.46875 1857.78125 521.320312 1910.28125 660.53125 1910.28125 C 799.738281 1910.28125 912.59375 1857.78125 912.59375 1793.03125 C 912.59375 1728.269531 799.738281 1675.78125 660.53125 1675.78125 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 92.96875%, 93.359375%)" fill-opacity="1" d="M 660.53125 1910.28125 C 521.320312 1910.28125 408.46875 1857.78125 408.46875 1793.03125 C 408.46875 1728.269531 521.320312 1675.78125 660.53125 1675.78125 C 799.738281 1675.78125 912.59375 1728.269531 912.59375 1793.03125 C 912.59375 1857.78125 799.738281 1910.28125 660.53125 1910.28125 M 660.53125 1678.75 C 524.847656 1678.75 414.855469 1729.910156 414.855469 1793.03125 C 414.855469 1856.140625 524.847656 1907.308594 660.53125 1907.308594 C 796.214844 1907.308594 906.207031 1856.140625 906.207031 1793.03125 C 906.207031 1729.910156 796.214844 1678.75 660.53125 1678.75 "/>
<path fill-rule="nonzero" fill="rgb(92.576599%, 92.576599%, 92.96875%)" fill-opacity="1" d="M 660.53125 1907.308594 C 524.847656 1907.308594 414.855469 1856.140625 414.855469 1793.03125 C 414.855469 1729.910156 524.847656 1678.75 660.53125 1678.75 C 796.214844 1678.75 906.207031 1729.910156 906.207031 1793.03125 C 906.207031 1856.140625 796.214844 1907.308594 660.53125 1907.308594 M 660.53125 1681.71875 C 528.375 1681.71875 421.242188 1731.558594 421.242188 1793.03125 C 421.242188 1854.5 528.375 1904.339844 660.53125 1904.339844 C 792.6875 1904.339844 899.820312 1854.5 899.820312 1793.03125 C 899.820312 1731.558594 792.6875 1681.71875 660.53125 1681.71875 "/>
<path fill-rule="nonzero" fill="rgb(92.1875%, 92.1875%, 92.96875%)" fill-opacity="1" d="M 660.53125 1904.339844 C 528.375 1904.339844 421.242188 1854.5 421.242188 1793.03125 C 421.242188 1731.558594 528.375 1681.71875 660.53125 1681.71875 C 792.6875 1681.71875 899.820312 1731.558594 899.820312 1793.03125 C 899.820312 1854.5 792.6875 1904.339844 660.53125 1904.339844 M 660.53125 1684.691406 C 531.902344 1684.691406 427.628906 1733.199219 427.628906 1793.03125 C 427.628906 1852.859375 531.902344 1901.371094 660.53125 1901.371094 C 789.160156 1901.371094 893.433594 1852.859375 893.433594 1793.03125 C 893.433594 1733.199219 789.160156 1684.691406 660.53125 1684.691406 "/>
<path fill-rule="nonzero" fill="rgb(91.796875%, 91.796875%, 92.576599%)" fill-opacity="1" d="M 660.53125 1901.371094 C 531.902344 1901.371094 427.628906 1852.859375 427.628906 1793.03125 C 427.628906 1733.199219 531.902344 1684.691406 660.53125 1684.691406 C 789.160156 1684.691406 893.433594 1733.199219 893.433594 1793.03125 C 893.433594 1852.859375 789.160156 1901.371094 660.53125 1901.371094 M 660.53125 1687.660156 C 535.429688 1687.660156 434.011719 1734.839844 434.011719 1793.03125 C 434.011719 1851.21875 535.429688 1898.398438 660.53125 1898.398438 C 785.632812 1898.398438 887.046875 1851.21875 887.046875 1793.03125 C 887.046875 1734.839844 785.632812 1687.660156 660.53125 1687.660156 "/>
<path fill-rule="nonzero" fill="rgb(91.40625%, 91.796875%, 92.1875%)" fill-opacity="1" d="M 660.53125 1898.398438 C 535.429688 1898.398438 434.011719 1851.21875 434.011719 1793.03125 C 434.011719 1734.839844 535.429688 1687.660156 660.53125 1687.660156 C 785.632812 1687.660156 887.046875 1734.839844 887.046875 1793.03125 C 887.046875 1851.21875 785.632812 1898.398438 660.53125 1898.398438 "/>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 1705.609375 1945.660156 C 1485.28125 1945.660156 1306.671875 1862.578125 1306.671875 1760.089844 C 1306.671875 1657.601562 1485.28125 1574.519531 1705.609375 1574.519531 C 1925.929688 1574.519531 2104.539062 1657.601562 2104.539062 1760.089844 C 2104.539062 1862.578125 1925.929688 1945.660156 1705.609375 1945.660156 M 1705.609375 1577.488281 C 1488.808594 1577.488281 1313.058594 1659.238281 1313.058594 1760.089844 C 1313.058594 1860.941406 1488.808594 1942.691406 1705.609375 1942.691406 C 1922.410156 1942.691406 2098.160156 1860.941406 2098.160156 1760.089844 C 2098.160156 1659.238281 1922.410156 1577.488281 1705.609375 1577.488281 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 99.609375%, 99.609375%)" fill-opacity="1" d="M 1705.609375 1942.691406 C 1488.808594 1942.691406 1313.058594 1860.941406 1313.058594 1760.089844 C 1313.058594 1659.238281 1488.808594 1577.488281 1705.609375 1577.488281 C 1922.410156 1577.488281 2098.160156 1659.238281 2098.160156 1760.089844 C 2098.160156 1860.941406 1922.410156 1942.691406 1705.609375 1942.691406 M 1705.609375 1580.460938 C 1492.328125 1580.460938 1319.441406 1660.878906 1319.441406 1760.089844 C 1319.441406 1859.289062 1492.328125 1939.71875 1705.609375 1939.71875 C 1918.878906 1939.71875 2091.769531 1859.289062 2091.769531 1760.089844 C 2091.769531 1660.878906 1918.878906 1580.460938 1705.609375 1580.460938 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.609375%)" fill-opacity="1" d="M 1705.609375 1939.71875 C 1492.328125 1939.71875 1319.441406 1859.289062 1319.441406 1760.089844 C 1319.441406 1660.878906 1492.328125 1580.460938 1705.609375 1580.460938 C 1918.878906 1580.460938 2091.769531 1660.878906 2091.769531 1760.089844 C 2091.769531 1859.289062 1918.878906 1939.71875 1705.609375 1939.71875 M 1705.609375 1583.429688 C 1495.859375 1583.429688 1325.828125 1662.519531 1325.828125 1760.089844 C 1325.828125 1857.648438 1495.859375 1936.75 1705.609375 1936.75 C 1915.351562 1936.75 2085.390625 1857.648438 2085.390625 1760.089844 C 2085.390625 1662.519531 1915.351562 1583.429688 1705.609375 1583.429688 "/>
<path fill-rule="nonzero" fill="rgb(99.21875%, 99.21875%, 99.21875%)" fill-opacity="1" d="M 1705.609375 1936.75 C 1495.859375 1936.75 1325.828125 1857.648438 1325.828125 1760.089844 C 1325.828125 1662.519531 1495.859375 1583.429688 1705.609375 1583.429688 C 1915.351562 1583.429688 2085.390625 1662.519531 2085.390625 1760.089844 C 2085.390625 1857.648438 1915.351562 1936.75 1705.609375 1936.75 M 1705.609375 1586.398438 C 1499.390625 1586.398438 1332.210938 1664.160156 1332.210938 1760.089844 C 1332.210938 1856.011719 1499.390625 1933.78125 1705.609375 1933.78125 C 1911.828125 1933.78125 2079 1856.011719 2079 1760.089844 C 2079 1664.160156 1911.828125 1586.398438 1705.609375 1586.398438 "/>
<path fill-rule="nonzero" fill="rgb(98.826599%, 98.826599%, 98.826599%)" fill-opacity="1" d="M 1705.609375 1933.78125 C 1499.390625 1933.78125 1332.210938 1856.011719 1332.210938 1760.089844 C 1332.210938 1664.160156 1499.390625 1586.398438 1705.609375 1586.398438 C 1911.828125 1586.398438 2079 1664.160156 2079 1760.089844 C 2079 1856.011719 1911.828125 1933.78125 1705.609375 1933.78125 M 1705.609375 1589.371094 C 1502.910156 1589.371094 1338.601562 1665.800781 1338.601562 1760.089844 C 1338.601562 1854.371094 1502.910156 1930.808594 1705.609375 1930.808594 C 1908.300781 1930.808594 2072.621094 1854.371094 2072.621094 1760.089844 C 2072.621094 1665.800781 1908.300781 1589.371094 1705.609375 1589.371094 "/>
<path fill-rule="nonzero" fill="rgb(98.4375%, 98.4375%, 98.4375%)" fill-opacity="1" d="M 1705.609375 1930.808594 C 1502.910156 1930.808594 1338.601562 1854.371094 1338.601562 1760.089844 C 1338.601562 1665.800781 1502.910156 1589.371094 1705.609375 1589.371094 C 1908.300781 1589.371094 2072.621094 1665.800781 2072.621094 1760.089844 C 2072.621094 1854.371094 1908.300781 1930.808594 1705.609375 1930.808594 M 1705.609375 1592.339844 C 1506.441406 1592.339844 1344.980469 1667.441406 1344.980469 1760.089844 C 1344.980469 1852.730469 1506.441406 1927.839844 1705.609375 1927.839844 C 1904.769531 1927.839844 2066.230469 1852.730469 2066.230469 1760.089844 C 2066.230469 1667.441406 1904.769531 1592.339844 1705.609375 1592.339844 "/>
<path fill-rule="nonzero" fill="rgb(98.046875%, 98.046875%, 98.046875%)" fill-opacity="1" d="M 1705.609375 1927.839844 C 1506.441406 1927.839844 1344.980469 1852.730469 1344.980469 1760.089844 C 1344.980469 1667.441406 1506.441406 1592.339844 1705.609375 1592.339844 C 1904.769531 1592.339844 2066.230469 1667.441406 2066.230469 1760.089844 C 2066.230469 1852.730469 1904.769531 1927.839844 1705.609375 1927.839844 M 1705.609375 1595.308594 C 1509.96875 1595.308594 1351.371094 1669.078125 1351.371094 1760.089844 C 1351.371094 1851.089844 1509.96875 1924.859375 1705.609375 1924.859375 C 1901.25 1924.859375 2059.839844 1851.089844 2059.839844 1760.089844 C 2059.839844 1669.078125 1901.25 1595.308594 1705.609375 1595.308594 "/>
<path fill-rule="nonzero" fill="rgb(97.65625%, 97.65625%, 98.046875%)" fill-opacity="1" d="M 1705.609375 1924.859375 C 1509.96875 1924.859375 1351.371094 1851.089844 1351.371094 1760.089844 C 1351.371094 1669.078125 1509.96875 1595.308594 1705.609375 1595.308594 C 1901.25 1595.308594 2059.839844 1669.078125 2059.839844 1760.089844 C 2059.839844 1851.089844 1901.25 1924.859375 1705.609375 1924.859375 M 1705.609375 1598.28125 C 1513.488281 1598.28125 1357.761719 1670.71875 1357.761719 1760.089844 C 1357.761719 1849.449219 1513.488281 1921.890625 1705.609375 1921.890625 C 1897.71875 1921.890625 2053.460938 1849.449219 2053.460938 1760.089844 C 2053.460938 1670.71875 1897.71875 1598.28125 1705.609375 1598.28125 "/>
<path fill-rule="nonzero" fill="rgb(97.264099%, 97.264099%, 97.65625%)" fill-opacity="1" d="M 1705.609375 1921.890625 C 1513.488281 1921.890625 1357.761719 1849.449219 1357.761719 1760.089844 C 1357.761719 1670.71875 1513.488281 1598.28125 1705.609375 1598.28125 C 1897.71875 1598.28125 2053.460938 1670.71875 2053.460938 1760.089844 C 2053.460938 1849.449219 1897.71875 1921.890625 1705.609375 1921.890625 M 1705.609375 1601.25 C 1517.019531 1601.25 1364.140625 1672.359375 1364.140625 1760.089844 C 1364.140625 1847.808594 1517.019531 1918.921875 1705.609375 1918.921875 C 1894.191406 1918.921875 2047.070312 1847.808594 2047.070312 1760.089844 C 2047.070312 1672.359375 1894.191406 1601.25 1705.609375 1601.25 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 97.264099%, 97.264099%)" fill-opacity="1" d="M 1705.609375 1918.921875 C 1517.019531 1918.921875 1364.140625 1847.808594 1364.140625 1760.089844 C 1364.140625 1672.359375 1517.019531 1601.25 1705.609375 1601.25 C 1894.191406 1601.25 2047.070312 1672.359375 2047.070312 1760.089844 C 2047.070312 1847.808594 1894.191406 1918.921875 1705.609375 1918.921875 M 1705.609375 1604.21875 C 1520.550781 1604.21875 1370.53125 1674.011719 1370.53125 1760.089844 C 1370.53125 1846.171875 1520.550781 1915.949219 1705.609375 1915.949219 C 1890.671875 1915.949219 2040.691406 1846.171875 2040.691406 1760.089844 C 2040.691406 1674.011719 1890.671875 1604.21875 1705.609375 1604.21875 "/>
<path fill-rule="nonzero" fill="rgb(96.875%, 96.875%, 96.875%)" fill-opacity="1" d="M 1705.609375 1915.949219 C 1520.550781 1915.949219 1370.53125 1846.171875 1370.53125 1760.089844 C 1370.53125 1674.011719 1520.550781 1604.21875 1705.609375 1604.21875 C 1890.671875 1604.21875 2040.691406 1674.011719 2040.691406 1760.089844 C 2040.691406 1846.171875 1890.671875 1915.949219 1705.609375 1915.949219 M 1705.609375 1607.191406 C 1524.078125 1607.191406 1376.910156 1675.648438 1376.910156 1760.089844 C 1376.910156 1844.53125 1524.078125 1912.980469 1705.609375 1912.980469 C 1887.140625 1912.980469 2034.300781 1844.53125 2034.300781 1760.089844 C 2034.300781 1675.648438 1887.140625 1607.191406 1705.609375 1607.191406 "/>
<path fill-rule="nonzero" fill="rgb(96.484375%, 96.484375%, 96.875%)" fill-opacity="1" d="M 1705.609375 1912.980469 C 1524.078125 1912.980469 1376.910156 1844.53125 1376.910156 1760.089844 C 1376.910156 1675.648438 1524.078125 1607.191406 1705.609375 1607.191406 C 1887.140625 1607.191406 2034.300781 1675.648438 2034.300781 1760.089844 C 2034.300781 1844.53125 1887.140625 1912.980469 1705.609375 1912.980469 M 1705.609375 1610.160156 C 1527.601562 1610.160156 1383.300781 1677.289062 1383.300781 1760.089844 C 1383.300781 1842.890625 1527.601562 1910.011719 1705.609375 1910.011719 C 1883.609375 1910.011719 2027.910156 1842.890625 2027.910156 1760.089844 C 2027.910156 1677.289062 1883.609375 1610.160156 1705.609375 1610.160156 "/>
<path fill-rule="nonzero" fill="rgb(96.09375%, 96.09375%, 96.484375%)" fill-opacity="1" d="M 1705.609375 1910.011719 C 1527.601562 1910.011719 1383.300781 1842.890625 1383.300781 1760.089844 C 1383.300781 1677.289062 1527.601562 1610.160156 1705.609375 1610.160156 C 1883.609375 1610.160156 2027.910156 1677.289062 2027.910156 1760.089844 C 2027.910156 1842.890625 1883.609375 1910.011719 1705.609375 1910.011719 M 1705.609375 1613.128906 C 1531.128906 1613.128906 1389.691406 1678.929688 1389.691406 1760.089844 C 1389.691406 1841.25 1531.128906 1907.039062 1705.609375 1907.039062 C 1880.089844 1907.039062 2021.53125 1841.25 2021.53125 1760.089844 C 2021.53125 1678.929688 1880.089844 1613.128906 1705.609375 1613.128906 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 1705.609375 1907.039062 C 1531.128906 1907.039062 1389.691406 1841.25 1389.691406 1760.089844 C 1389.691406 1678.929688 1531.128906 1613.128906 1705.609375 1613.128906 C 1880.089844 1613.128906 2021.53125 1678.929688 2021.53125 1760.089844 C 2021.53125 1841.25 1880.089844 1907.039062 1705.609375 1907.039062 M 1705.609375 1616.101562 C 1534.660156 1616.101562 1396.070312 1680.570312 1396.070312 1760.089844 C 1396.070312 1839.609375 1534.660156 1904.070312 1705.609375 1904.070312 C 1876.558594 1904.070312 2015.140625 1839.609375 2015.140625 1760.089844 C 2015.140625 1680.570312 1876.558594 1616.101562 1705.609375 1616.101562 "/>
<path fill-rule="nonzero" fill="rgb(95.701599%, 95.701599%, 96.09375%)" fill-opacity="1" d="M 1705.609375 1904.070312 C 1534.660156 1904.070312 1396.070312 1839.609375 1396.070312 1760.089844 C 1396.070312 1680.570312 1534.660156 1616.101562 1705.609375 1616.101562 C 1876.558594 1616.101562 2015.140625 1680.570312 2015.140625 1760.089844 C 2015.140625 1839.609375 1876.558594 1904.070312 1705.609375 1904.070312 M 1705.609375 1619.070312 C 1538.179688 1619.070312 1402.460938 1682.210938 1402.460938 1760.089844 C 1402.460938 1837.96875 1538.179688 1901.101562 1705.609375 1901.101562 C 1873.03125 1901.101562 2008.761719 1837.96875 2008.761719 1760.089844 C 2008.761719 1682.210938 1873.03125 1619.070312 1705.609375 1619.070312 "/>
<path fill-rule="nonzero" fill="rgb(95.3125%, 95.701599%, 95.701599%)" fill-opacity="1" d="M 1705.609375 1901.101562 C 1538.179688 1901.101562 1402.460938 1837.96875 1402.460938 1760.089844 C 1402.460938 1682.210938 1538.179688 1619.070312 1705.609375 1619.070312 C 1873.03125 1619.070312 2008.761719 1682.210938 2008.761719 1760.089844 C 2008.761719 1837.96875 1873.03125 1901.101562 1705.609375 1901.101562 M 1705.609375 1622.039062 C 1541.710938 1622.039062 1408.839844 1683.851562 1408.839844 1760.089844 C 1408.839844 1836.328125 1541.710938 1898.128906 1705.609375 1898.128906 C 1869.5 1898.128906 2002.371094 1836.328125 2002.371094 1760.089844 C 2002.371094 1683.851562 1869.5 1622.039062 1705.609375 1622.039062 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 95.3125%, 95.701599%)" fill-opacity="1" d="M 1705.609375 1898.128906 C 1541.710938 1898.128906 1408.839844 1836.328125 1408.839844 1760.089844 C 1408.839844 1683.851562 1541.710938 1622.039062 1705.609375 1622.039062 C 1869.5 1622.039062 2002.371094 1683.851562 2002.371094 1760.089844 C 2002.371094 1836.328125 1869.5 1898.128906 1705.609375 1898.128906 M 1705.609375 1625.019531 C 1545.238281 1625.019531 1415.230469 1685.488281 1415.230469 1760.089844 C 1415.230469 1834.691406 1545.238281 1895.160156 1705.609375 1895.160156 C 1865.980469 1895.160156 1995.980469 1834.691406 1995.980469 1760.089844 C 1995.980469 1685.488281 1865.980469 1625.019531 1705.609375 1625.019531 "/>
<path fill-rule="nonzero" fill="rgb(94.921875%, 94.921875%, 95.3125%)" fill-opacity="1" d="M 1705.609375 1895.160156 C 1545.238281 1895.160156 1415.230469 1834.691406 1415.230469 1760.089844 C 1415.230469 1685.488281 1545.238281 1625.019531 1705.609375 1625.019531 C 1865.980469 1625.019531 1995.980469 1685.488281 1995.980469 1760.089844 C 1995.980469 1834.691406 1865.980469 1895.160156 1705.609375 1895.160156 M 1705.609375 1627.988281 C 1548.761719 1627.988281 1421.621094 1687.128906 1421.621094 1760.089844 C 1421.621094 1833.050781 1548.761719 1892.191406 1705.609375 1892.191406 C 1862.449219 1892.191406 1989.601562 1833.050781 1989.601562 1760.089844 C 1989.601562 1687.128906 1862.449219 1627.988281 1705.609375 1627.988281 "/>
<path fill-rule="nonzero" fill="rgb(94.53125%, 94.53125%, 94.921875%)" fill-opacity="1" d="M 1705.609375 1892.191406 C 1548.761719 1892.191406 1421.621094 1833.050781 1421.621094 1760.089844 C 1421.621094 1687.128906 1548.761719 1627.988281 1705.609375 1627.988281 C 1862.449219 1627.988281 1989.601562 1687.128906 1989.601562 1760.089844 C 1989.601562 1833.050781 1862.449219 1892.191406 1705.609375 1892.191406 M 1705.609375 1630.960938 C 1552.289062 1630.960938 1428 1688.769531 1428 1760.089844 C 1428 1831.398438 1552.289062 1889.21875 1705.609375 1889.21875 C 1858.921875 1889.21875 1983.210938 1831.398438 1983.210938 1760.089844 C 1983.210938 1688.769531 1858.921875 1630.960938 1705.609375 1630.960938 "/>
<path fill-rule="nonzero" fill="rgb(94.139099%, 94.139099%, 94.53125%)" fill-opacity="1" d="M 1705.609375 1889.21875 C 1552.289062 1889.21875 1428 1831.398438 1428 1760.089844 C 1428 1688.769531 1552.289062 1630.960938 1705.609375 1630.960938 C 1858.921875 1630.960938 1983.210938 1688.769531 1983.210938 1760.089844 C 1983.210938 1831.398438 1858.921875 1889.21875 1705.609375 1889.21875 M 1705.609375 1633.929688 C 1555.820312 1633.929688 1434.390625 1690.410156 1434.390625 1760.089844 C 1434.390625 1829.761719 1555.820312 1886.25 1705.609375 1886.25 C 1855.398438 1886.25 1976.828125 1829.761719 1976.828125 1760.089844 C 1976.828125 1690.410156 1855.398438 1633.929688 1705.609375 1633.929688 "/>
<path fill-rule="nonzero" fill="rgb(93.75%, 93.75%, 94.139099%)" fill-opacity="1" d="M 1705.609375 1886.25 C 1555.820312 1886.25 1434.390625 1829.761719 1434.390625 1760.089844 C 1434.390625 1690.410156 1555.820312 1633.929688 1705.609375 1633.929688 C 1855.398438 1633.929688 1976.828125 1690.410156 1976.828125 1760.089844 C 1976.828125 1829.761719 1855.398438 1886.25 1705.609375 1886.25 M 1705.609375 1636.898438 C 1559.339844 1636.898438 1440.769531 1692.050781 1440.769531 1760.089844 C 1440.769531 1828.121094 1559.339844 1883.28125 1705.609375 1883.28125 C 1851.871094 1883.28125 1970.441406 1828.121094 1970.441406 1760.089844 C 1970.441406 1692.050781 1851.871094 1636.898438 1705.609375 1636.898438 "/>
<path fill-rule="nonzero" fill="rgb(93.359375%, 93.75%, 94.139099%)" fill-opacity="1" d="M 1705.609375 1883.28125 C 1559.339844 1883.28125 1440.769531 1828.121094 1440.769531 1760.089844 C 1440.769531 1692.050781 1559.339844 1636.898438 1705.609375 1636.898438 C 1851.871094 1636.898438 1970.441406 1692.050781 1970.441406 1760.089844 C 1970.441406 1828.121094 1851.871094 1883.28125 1705.609375 1883.28125 M 1705.609375 1639.871094 C 1562.871094 1639.871094 1447.160156 1693.691406 1447.160156 1760.089844 C 1447.160156 1826.480469 1562.871094 1880.308594 1705.609375 1880.308594 C 1848.339844 1880.308594 1964.050781 1826.480469 1964.050781 1760.089844 C 1964.050781 1693.691406 1848.339844 1639.871094 1705.609375 1639.871094 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 93.359375%, 93.75%)" fill-opacity="1" d="M 1705.609375 1880.308594 C 1562.871094 1880.308594 1447.160156 1826.480469 1447.160156 1760.089844 C 1447.160156 1693.691406 1562.871094 1639.871094 1705.609375 1639.871094 C 1848.339844 1639.871094 1964.050781 1693.691406 1964.050781 1760.089844 C 1964.050781 1826.480469 1848.339844 1880.308594 1705.609375 1880.308594 M 1705.609375 1642.839844 C 1566.398438 1642.839844 1453.550781 1695.328125 1453.550781 1760.089844 C 1453.550781 1824.839844 1566.398438 1877.339844 1705.609375 1877.339844 C 1844.820312 1877.339844 1957.671875 1824.839844 1957.671875 1760.089844 C 1957.671875 1695.328125 1844.820312 1642.839844 1705.609375 1642.839844 "/>
<path fill-rule="nonzero" fill="rgb(92.96875%, 92.96875%, 93.359375%)" fill-opacity="1" d="M 1705.609375 1877.339844 C 1566.398438 1877.339844 1453.550781 1824.839844 1453.550781 1760.089844 C 1453.550781 1695.328125 1566.398438 1642.839844 1705.609375 1642.839844 C 1844.820312 1642.839844 1957.671875 1695.328125 1957.671875 1760.089844 C 1957.671875 1824.839844 1844.820312 1877.339844 1705.609375 1877.339844 M 1705.609375 1645.808594 C 1569.921875 1645.808594 1459.929688 1696.96875 1459.929688 1760.089844 C 1459.929688 1823.199219 1569.921875 1874.371094 1705.609375 1874.371094 C 1841.289062 1874.371094 1951.28125 1823.199219 1951.28125 1760.089844 C 1951.28125 1696.96875 1841.289062 1645.808594 1705.609375 1645.808594 "/>
<path fill-rule="nonzero" fill="rgb(92.576599%, 92.576599%, 92.96875%)" fill-opacity="1" d="M 1705.609375 1874.371094 C 1569.921875 1874.371094 1459.929688 1823.199219 1459.929688 1760.089844 C 1459.929688 1696.96875 1569.921875 1645.808594 1705.609375 1645.808594 C 1841.289062 1645.808594 1951.28125 1696.96875 1951.28125 1760.089844 C 1951.28125 1823.199219 1841.289062 1874.371094 1705.609375 1874.371094 M 1705.609375 1648.78125 C 1573.449219 1648.78125 1466.320312 1698.609375 1466.320312 1760.089844 C 1466.320312 1821.558594 1573.449219 1871.398438 1705.609375 1871.398438 C 1837.761719 1871.398438 1944.898438 1821.558594 1944.898438 1760.089844 C 1944.898438 1698.609375 1837.761719 1648.78125 1705.609375 1648.78125 "/>
<path fill-rule="nonzero" fill="rgb(92.1875%, 92.1875%, 92.96875%)" fill-opacity="1" d="M 1705.609375 1871.398438 C 1573.449219 1871.398438 1466.320312 1821.558594 1466.320312 1760.089844 C 1466.320312 1698.609375 1573.449219 1648.78125 1705.609375 1648.78125 C 1837.761719 1648.78125 1944.898438 1698.609375 1944.898438 1760.089844 C 1944.898438 1821.558594 1837.761719 1871.398438 1705.609375 1871.398438 M 1705.609375 1651.75 C 1576.980469 1651.75 1472.699219 1700.25 1472.699219 1760.089844 C 1472.699219 1819.921875 1576.980469 1868.429688 1705.609375 1868.429688 C 1834.238281 1868.429688 1938.511719 1819.921875 1938.511719 1760.089844 C 1938.511719 1700.25 1834.238281 1651.75 1705.609375 1651.75 "/>
<path fill-rule="nonzero" fill="rgb(91.796875%, 91.796875%, 92.576599%)" fill-opacity="1" d="M 1705.609375 1868.429688 C 1576.980469 1868.429688 1472.699219 1819.921875 1472.699219 1760.089844 C 1472.699219 1700.25 1576.980469 1651.75 1705.609375 1651.75 C 1834.238281 1651.75 1938.511719 1700.25 1938.511719 1760.089844 C 1938.511719 1819.921875 1834.238281 1868.429688 1705.609375 1868.429688 M 1705.609375 1654.71875 C 1580.511719 1654.71875 1479.089844 1701.890625 1479.089844 1760.089844 C 1479.089844 1818.28125 1580.511719 1865.449219 1705.609375 1865.449219 C 1830.710938 1865.449219 1932.121094 1818.28125 1932.121094 1760.089844 C 1932.121094 1701.890625 1830.710938 1654.71875 1705.609375 1654.71875 "/>
<path fill-rule="nonzero" fill="rgb(91.40625%, 91.796875%, 92.1875%)" fill-opacity="1" d="M 1705.609375 1865.449219 C 1580.511719 1865.449219 1479.089844 1818.28125 1479.089844 1760.089844 C 1479.089844 1701.890625 1580.511719 1654.71875 1705.609375 1654.71875 C 1830.710938 1654.71875 1932.121094 1701.890625 1932.121094 1760.089844 C 1932.121094 1818.28125 1830.710938 1865.449219 1705.609375 1865.449219 "/>
<path fill-rule="nonzero" fill="rgb(68.164062%, 69.334412%, 71.679688%)" fill-opacity="1" d="M 1430.050781 1214.621094 L 821.78125 1549.820312 L 1557.339844 1977.578125 L 1562.371094 1968.941406 L 842.078125 1550.050781 L 1434.871094 1223.378906 L 1430.050781 1214.621094 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 73.631287%, 73.631287%)" fill-opacity="1" d="M 1898.308594 1416.5 L 1898.050781 1508.710938 L 1862.179688 1545.351562 L 1862.441406 1453.140625 L 1898.308594 1416.5 "/>
<path fill-rule="nonzero" fill="rgb(50.193787%, 50.193787%, 50.193787%)" fill-opacity="1" d="M 1434.621094 1453.128906 L 1434.359375 1545.339844 L 1398.101562 1508.710938 L 1398.359375 1416.5 L 1434.621094 1453.128906 "/>
<path fill-rule="nonzero" fill="rgb(58.006287%, 58.006287%, 58.006287%)" fill-opacity="1" d="M 1746.410156 1466.421875 C 1743.148438 1464.539062 1739.75 1462.78125 1736.261719 1461.121094 C 1735.101562 1460.570312 1733.859375 1460.089844 1732.671875 1459.558594 C 1730.609375 1458.640625 1728.578125 1457.691406 1726.449219 1456.851562 C 1726.089844 1456.710938 1725.710938 1456.589844 1725.339844 1456.441406 C 1724.550781 1456.140625 1723.738281 1455.859375 1722.941406 1455.570312 C 1720.179688 1454.550781 1717.371094 1453.589844 1714.519531 1452.691406 C 1713.550781 1452.390625 1712.589844 1452.078125 1711.609375 1451.789062 C 1710.960938 1451.589844 1710.320312 1451.371094 1709.660156 1451.179688 C 1708.5 1450.851562 1707.28125 1450.621094 1706.109375 1450.308594 C 1703.269531 1449.558594 1700.421875 1448.839844 1697.511719 1448.199219 C 1696.738281 1448.03125 1696 1447.800781 1695.230469 1447.640625 C 1694.089844 1447.410156 1692.929688 1447.261719 1691.789062 1447.050781 C 1689.351562 1446.578125 1686.910156 1446.148438 1684.441406 1445.769531 C 1683.460938 1445.609375 1682.5 1445.398438 1681.511719 1445.261719 C 1680.441406 1445.109375 1679.339844 1445.03125 1678.269531 1444.898438 C 1675.96875 1444.601562 1673.671875 1444.351562 1671.351562 1444.121094 C 1670.25 1444.019531 1669.160156 1443.851562 1668.058594 1443.75 C 1667.03125 1443.671875 1666 1443.660156 1664.96875 1443.589844 C 1662.628906 1443.429688 1660.289062 1443.308594 1657.941406 1443.210938 C 1656.769531 1443.171875 1655.601562 1443.058594 1654.429688 1443.019531 C 1653.488281 1443 1652.550781 1443.050781 1651.621094 1443.039062 C 1649.089844 1443.011719 1646.558594 1443.03125 1644.03125 1443.070312 C 1642.78125 1443.101562 1641.519531 1443.050781 1640.261719 1443.089844 C 1639.511719 1443.121094 1638.78125 1443.210938 1638.03125 1443.238281 C 1634.910156 1443.378906 1631.800781 1443.601562 1628.691406 1443.859375 C 1627.429688 1443.96875 1626.171875 1443.988281 1624.921875 1444.109375 C 1624.308594 1444.171875 1623.71875 1444.261719 1623.121094 1444.328125 C 1622.179688 1444.429688 1621.261719 1444.550781 1620.328125 1444.660156 C 1617.109375 1445.050781 1613.921875 1445.5 1610.75 1446.019531 C 1609.960938 1446.148438 1609.179688 1446.261719 1608.390625 1446.390625 C 1608.019531 1446.460938 1607.640625 1446.5 1607.269531 1446.570312 C 1605.769531 1446.839844 1604.308594 1447.21875 1602.820312 1447.519531 C 1600.261719 1448.039062 1597.691406 1448.550781 1595.171875 1449.160156 C 1593.328125 1449.601562 1591.539062 1450.128906 1589.71875 1450.621094 C 1587.339844 1451.269531 1584.949219 1451.921875 1582.609375 1452.648438 C 1582.109375 1452.808594 1581.578125 1452.929688 1581.078125 1453.089844 C 1578.230469 1454.011719 1575.429688 1455.011719 1572.679688 1456.058594 C 1572.28125 1456.210938 1571.898438 1456.390625 1571.5 1456.539062 C 1568.480469 1457.71875 1565.53125 1458.988281 1562.640625 1460.320312 C 1561.789062 1460.71875 1560.941406 1461.109375 1560.101562 1461.519531 C 1556.910156 1463.070312 1553.769531 1464.679688 1550.769531 1466.421875 C 1550.199219 1466.75 1549.640625 1467.078125 1549.078125 1467.421875 C 1548.558594 1467.730469 1548.078125 1468.058594 1547.570312 1468.378906 C 1546.71875 1468.898438 1545.871094 1469.429688 1545.050781 1469.96875 C 1544.300781 1470.460938 1543.578125 1470.960938 1542.851562 1471.460938 C 1542.058594 1472 1541.269531 1472.550781 1540.511719 1473.101562 C 1539.808594 1473.621094 1539.128906 1474.128906 1538.449219 1474.648438 C 1537.71875 1475.210938 1536.988281 1475.769531 1536.289062 1476.339844 C 1535.640625 1476.871094 1535.011719 1477.398438 1534.378906 1477.929688 C 1533.699219 1478.511719 1533.03125 1479.089844 1532.390625 1479.671875 C 1532.121094 1479.910156 1531.828125 1480.140625 1531.570312 1480.390625 C 1531.320312 1480.609375 1531.109375 1480.851562 1530.871094 1481.078125 C 1529.789062 1482.101562 1528.738281 1483.140625 1527.75 1484.191406 C 1527.410156 1484.539062 1527.058594 1484.890625 1526.730469 1485.25 C 1525.460938 1486.628906 1524.261719 1488.019531 1523.140625 1489.429688 L 1522.988281 1489.609375 C 1522.730469 1489.941406 1522.519531 1490.269531 1522.269531 1490.589844 C 1521.589844 1491.480469 1520.929688 1492.371094 1520.308594 1493.28125 C 1519.980469 1493.769531 1519.671875 1494.261719 1519.351562 1494.75 C 1518.828125 1495.570312 1518.308594 1496.378906 1517.828125 1497.210938 C 1517.699219 1497.429688 1517.550781 1497.648438 1517.421875 1497.878906 C 1517.230469 1498.210938 1517.078125 1498.550781 1516.898438 1498.890625 C 1516.53125 1499.578125 1516.171875 1500.261719 1515.828125 1500.960938 C 1515.550781 1501.539062 1515.269531 1502.121094 1515.011719 1502.710938 C 1514.699219 1503.410156 1514.410156 1504.101562 1514.128906 1504.808594 C 1514.011719 1505.121094 1513.851562 1505.429688 1513.738281 1505.738281 C 1513.628906 1506.019531 1513.550781 1506.308594 1513.460938 1506.589844 C 1513.21875 1507.261719 1513 1507.929688 1512.789062 1508.601562 C 1512.601562 1509.199219 1512.421875 1509.808594 1512.261719 1510.421875 C 1512.078125 1511.089844 1511.921875 1511.769531 1511.769531 1512.441406 C 1511.679688 1512.800781 1511.570312 1513.171875 1511.5 1513.53125 C 1511.449219 1513.78125 1511.429688 1514.019531 1511.378906 1514.269531 C 1511.261719 1514.960938 1511.148438 1515.648438 1511.058594 1516.339844 C 1510.96875 1516.949219 1510.890625 1517.550781 1510.828125 1518.160156 C 1510.761719 1518.859375 1510.71875 1519.550781 1510.679688 1520.25 C 1510.660156 1520.660156 1510.601562 1521.058594 1510.589844 1521.46875 C 1510.578125 1521.871094 1510.570312 1522.28125 1510.570312 1522.691406 L 1510.308594 1614.898438 C 1510.308594 1614.078125 1510.371094 1613.269531 1510.421875 1612.460938 C 1510.460938 1611.761719 1510.5 1611.070312 1510.570312 1610.371094 C 1510.628906 1609.769531 1510.710938 1609.160156 1510.800781 1608.558594 C 1510.890625 1607.859375 1510.988281 1607.171875 1511.121094 1606.480469 C 1511.230469 1605.871094 1511.371094 1605.261719 1511.5 1604.648438 C 1511.648438 1603.980469 1511.820312 1603.308594 1511.988281 1602.628906 C 1512.160156 1602.019531 1512.339844 1601.410156 1512.53125 1600.800781 C 1512.738281 1600.140625 1512.960938 1599.46875 1513.191406 1598.808594 C 1513.398438 1598.210938 1513.628906 1597.609375 1513.871094 1597.019531 C 1514.148438 1596.308594 1514.441406 1595.621094 1514.75 1594.921875 C 1515.011719 1594.339844 1515.28125 1593.75 1515.570312 1593.171875 C 1515.910156 1592.480469 1516.261719 1591.789062 1516.640625 1591.101562 C 1516.941406 1590.539062 1517.25 1589.980469 1517.570312 1589.421875 C 1518.050781 1588.601562 1518.558594 1587.78125 1519.089844 1586.96875 C 1519.410156 1586.46875 1519.71875 1585.980469 1520.050781 1585.488281 C 1520.671875 1584.589844 1521.328125 1583.699219 1522 1582.808594 C 1522.300781 1582.421875 1522.570312 1582.03125 1522.878906 1581.640625 C 1524 1580.230469 1525.199219 1578.839844 1526.460938 1577.460938 C 1526.789062 1577.101562 1527.148438 1576.75 1527.488281 1576.398438 C 1528.488281 1575.351562 1529.53125 1574.308594 1530.609375 1573.289062 C 1531.109375 1572.820312 1531.609375 1572.351562 1532.121094 1571.878906 C 1532.769531 1571.300781 1533.441406 1570.71875 1534.121094 1570.140625 C 1534.738281 1569.609375 1535.378906 1569.078125 1536.03125 1568.550781 C 1536.730469 1567.980469 1537.460938 1567.421875 1538.191406 1566.859375 C 1538.871094 1566.339844 1539.550781 1565.828125 1540.25 1565.320312 C 1541.011719 1564.761719 1541.800781 1564.21875 1542.589844 1563.671875 C 1543.308594 1563.171875 1544.039062 1562.671875 1544.800781 1562.179688 C 1545.609375 1561.640625 1546.449219 1561.121094 1547.300781 1560.589844 C 1548.359375 1559.929688 1549.398438 1559.269531 1550.511719 1558.628906 C 1553.511719 1556.890625 1556.640625 1555.28125 1559.839844 1553.730469 C 1560.679688 1553.320312 1561.53125 1552.929688 1562.378906 1552.53125 C 1565.269531 1551.199219 1568.230469 1549.929688 1571.261719 1548.75 C 1571.648438 1548.589844 1572.019531 1548.421875 1572.410156 1548.269531 C 1575.648438 1547.039062 1578.980469 1545.929688 1582.351562 1544.871094 C 1584.691406 1544.128906 1587.078125 1543.480469 1589.46875 1542.828125 C 1591.28125 1542.339844 1593.070312 1541.808594 1594.910156 1541.371094 C 1597.421875 1540.761719 1599.988281 1540.25 1602.550781 1539.738281 C 1604.421875 1539.359375 1606.25 1538.929688 1608.140625 1538.601562 C 1608.910156 1538.46875 1609.699219 1538.359375 1610.480469 1538.230469 C 1613.648438 1537.710938 1616.839844 1537.261719 1620.050781 1536.871094 C 1620.988281 1536.761719 1621.921875 1536.640625 1622.859375 1536.539062 C 1624.699219 1536.339844 1626.558594 1536.230469 1628.421875 1536.070312 C 1631.53125 1535.808594 1634.640625 1535.589844 1637.78125 1535.449219 C 1639.769531 1535.359375 1641.769531 1535.320312 1643.761719 1535.28125 C 1646.300781 1535.238281 1648.820312 1535.21875 1651.351562 1535.25 C 1653.46875 1535.28125 1655.578125 1535.339844 1657.691406 1535.429688 C 1660.03125 1535.519531 1662.371094 1535.640625 1664.699219 1535.800781 C 1666.839844 1535.949219 1668.96875 1536.128906 1671.089844 1536.328125 C 1673.398438 1536.558594 1675.710938 1536.808594 1678 1537.109375 C 1680.070312 1537.371094 1682.140625 1537.660156 1684.191406 1537.980469 C 1686.648438 1538.359375 1689.089844 1538.789062 1691.519531 1539.261719 C 1693.441406 1539.621094 1695.359375 1540 1697.261719 1540.421875 C 1700.160156 1541.050781 1703.011719 1541.769531 1705.851562 1542.519531 C 1707.679688 1543.011719 1709.539062 1543.460938 1711.351562 1544 C 1712.328125 1544.289062 1713.289062 1544.601562 1714.261719 1544.898438 C 1717.109375 1545.800781 1719.921875 1546.761719 1722.679688 1547.78125 C 1723.480469 1548.070312 1724.289062 1548.351562 1725.078125 1548.660156 C 1727.589844 1549.628906 1730.011719 1550.699219 1732.429688 1551.78125 C 1733.609375 1552.308594 1734.839844 1552.78125 1736 1553.328125 C 1739.488281 1554.988281 1742.890625 1556.75 1746.148438 1558.628906 C 1773.429688 1574.378906 1787.050781 1595.050781 1786.988281 1615.679688 L 1787.25 1523.46875 C 1787.308594 1502.839844 1773.691406 1482.171875 1746.410156 1466.421875 "/>
<g clip-path="url(#clip-1)">
<g clip-path="url(#clip-2)">
<path fill-rule="nonzero" fill="rgb(58.006287%, 58.006287%, 58.006287%)" fill-opacity="1" d="M 1964.339844 1523.078125 L 1964.070312 1615.289062 C 1964.078125 1615.699219 1964.070312 1616.109375 1964.058594 1616.511719 L 1964.320312 1524.300781 C 1964.339844 1523.890625 1964.339844 1523.488281 1964.339844 1523.078125 "/>
</g>
</g>
<g clip-path="url(#clip-3)">
<g clip-path="url(#clip-4)">
<path fill-rule="nonzero" fill="rgb(59.959412%, 59.959412%, 59.959412%)" fill-opacity="1" d="M 1964.320312 1524.300781 L 1964.058594 1616.511719 C 1964 1618.730469 1963.710938 1620.941406 1963.28125 1623.089844 L 1963.539062 1530.871094 C 1963.96875 1528.730469 1964.261719 1526.519531 1964.320312 1524.300781 "/>
</g>
</g>
<g clip-path="url(#clip-5)">
<g clip-path="url(#clip-6)">
<path fill-rule="nonzero" fill="rgb(61.914062%, 61.914062%, 61.914062%)" fill-opacity="1" d="M 1963.539062 1530.871094 L 1963.28125 1623.089844 C 1962.660156 1626.148438 1961.761719 1629.070312 1960.78125 1631.671875 L 1961.039062 1539.460938 C 1962.019531 1536.859375 1962.929688 1533.941406 1963.539062 1530.871094 "/>
</g>
</g>
<g clip-path="url(#clip-7)">
<g clip-path="url(#clip-8)">
<path fill-rule="nonzero" fill="rgb(63.867188%, 63.867188%, 63.867188%)" fill-opacity="1" d="M 1961.039062 1539.460938 L 1960.78125 1631.671875 C 1959.148438 1636.019531 1957.328125 1639.460938 1956.371094 1641.171875 L 1956.628906 1548.949219 C 1957.589844 1547.25 1959.410156 1543.800781 1961.039062 1539.460938 "/>
</g>
</g>
<g clip-path="url(#clip-9)">
<g clip-path="url(#clip-10)">
<path fill-rule="nonzero" fill="rgb(65.818787%, 65.818787%, 65.818787%)" fill-opacity="1" d="M 1956.628906 1548.949219 L 1956.371094 1641.171875 C 1956.011719 1641.808594 1955.769531 1642.199219 1955.699219 1642.308594 L 1955.96875 1550.101562 C 1956.03125 1549.988281 1956.269531 1549.601562 1956.628906 1548.949219 "/>
</g>
</g>
<g clip-path="url(#clip-11)">
<g clip-path="url(#clip-12)">
<path fill-rule="nonzero" fill="rgb(52.146912%, 52.146912%, 52.146912%)" fill-opacity="1" d="M 1342.210938 1550.101562 L 1341.949219 1642.308594 C 1341.519531 1641.621094 1333.289062 1628.371094 1333.21875 1615.289062 L 1333.480469 1523.078125 C 1333.550781 1536.160156 1341.78125 1549.398438 1342.210938 1550.101562 "/>
</g>
</g>
<path fill-rule="nonzero" fill="rgb(74.021912%, 74.021912%, 74.021912%)" fill-opacity="1" d="M 1955.96875 1550.101562 L 1955.699219 1642.308594 L 1885.53125 1653.441406 L 1885.789062 1561.21875 L 1955.96875 1550.101562 "/>
<path fill-rule="nonzero" fill="rgb(64.256287%, 64.256287%, 64.256287%)" fill-opacity="1" d="M 1412.480469 1561.238281 L 1412.21875 1653.460938 L 1341.949219 1642.308594 L 1342.210938 1550.101562 L 1412.480469 1561.238281 "/>
<path fill-rule="nonzero" fill="rgb(67.771912%, 67.771912%, 67.771912%)" fill-opacity="1" d="M 1881.871094 1568.621094 C 1881.269531 1569.671875 1880.589844 1570.699219 1879.949219 1571.738281 C 1878.988281 1573.28125 1878.019531 1574.820312 1876.960938 1576.351562 C 1876.21875 1577.429688 1875.421875 1578.5 1874.621094 1579.570312 C 1873.441406 1581.171875 1872.238281 1582.761719 1870.949219 1584.339844 C 1870.441406 1584.960938 1869.941406 1585.570312 1869.421875 1586.191406 C 1867.449219 1588.5 1865.421875 1590.789062 1863.21875 1593.050781 L 1862.960938 1685.261719 C 1865.160156 1683 1867.191406 1680.710938 1869.148438 1678.398438 C 1869.671875 1677.78125 1870.179688 1677.171875 1870.691406 1676.550781 C 1871.230469 1675.890625 1871.800781 1675.238281 1872.320312 1674.578125 C 1873.050781 1673.648438 1873.679688 1672.710938 1874.371094 1671.769531 C 1875.148438 1670.710938 1875.960938 1669.648438 1876.699219 1668.570312 C 1877.761719 1667.039062 1878.730469 1665.488281 1879.691406 1663.941406 C 1880.328125 1662.910156 1881.011719 1661.878906 1881.609375 1660.839844 C 1881.78125 1660.550781 1881.980469 1660.261719 1882.140625 1659.96875 C 1883.371094 1657.808594 1884.488281 1655.628906 1885.53125 1653.441406 L 1885.800781 1561.21875 C 1884.621094 1563.710938 1883.289062 1566.171875 1881.871094 1568.621094 "/>
<g clip-path="url(#clip-13)">
<g clip-path="url(#clip-14)">
<path fill-rule="nonzero" fill="rgb(52.146912%, 52.146912%, 52.146912%)" fill-opacity="1" d="M 1435.410156 1593.03125 L 1435.148438 1685.25 C 1425.179688 1675.101562 1417.550781 1664.449219 1412.21875 1653.460938 L 1412.480469 1561.238281 C 1417.808594 1572.230469 1425.449219 1582.890625 1435.410156 1593.03125 "/>
</g>
</g>
<path fill-rule="nonzero" fill="rgb(67.771912%, 67.771912%, 67.771912%)" fill-opacity="1" d="M 1757.160156 1651.03125 C 1757.039062 1651.058594 1756.921875 1651.089844 1756.808594 1651.121094 C 1752.769531 1652.261719 1748.691406 1653.308594 1744.578125 1654.308594 C 1743.210938 1654.640625 1741.828125 1654.941406 1740.460938 1655.261719 C 1737.550781 1655.929688 1734.640625 1656.570312 1731.699219 1657.179688 C 1730.109375 1657.5 1728.511719 1657.808594 1726.910156 1658.121094 C 1723.140625 1658.828125 1719.371094 1659.539062 1715.558594 1660.148438 L 1715.300781 1752.359375 C 1717.75 1751.96875 1720.191406 1751.558594 1722.609375 1751.121094 C 1723.96875 1750.871094 1725.300781 1750.578125 1726.660156 1750.328125 C 1728.25 1750.019531 1729.851562 1749.710938 1731.429688 1749.390625 C 1734.371094 1748.789062 1737.289062 1748.140625 1740.199219 1747.46875 C 1741.570312 1747.148438 1742.949219 1746.851562 1744.320312 1746.519531 C 1748.449219 1745.519531 1752.550781 1744.460938 1756.601562 1743.320312 C 1756.691406 1743.300781 1756.789062 1743.269531 1756.890625 1743.25 C 1760.621094 1742.191406 1764.308594 1741.078125 1767.96875 1739.910156 C 1768.691406 1739.679688 1769.421875 1739.441406 1770.140625 1739.199219 L 1770.398438 1646.988281 C 1766.039062 1648.421875 1761.628906 1649.769531 1757.160156 1651.03125 "/>
<path fill-rule="nonzero" fill="rgb(54.101562%, 54.101562%, 54.101562%)" fill-opacity="1" d="M 1583.820312 1660.171875 C 1578.578125 1659.320312 1573.378906 1658.351562 1568.21875 1657.308594 C 1565.570312 1656.769531 1562.949219 1656.171875 1560.328125 1655.578125 C 1558.539062 1655.171875 1556.71875 1654.789062 1554.941406 1654.359375 C 1552.03125 1653.660156 1549.160156 1652.878906 1546.289062 1652.109375 C 1544.769531 1651.699219 1543.230469 1651.328125 1541.730469 1650.898438 C 1537.390625 1649.671875 1533.101562 1648.378906 1528.871094 1646.988281 L 1528.601562 1739.199219 C 1532.679688 1740.539062 1536.808594 1741.800781 1540.988281 1742.988281 C 1541.148438 1743.03125 1541.308594 1743.070312 1541.46875 1743.109375 C 1542.96875 1743.539062 1544.511719 1743.910156 1546.03125 1744.320312 C 1548.898438 1745.089844 1551.769531 1745.871094 1554.679688 1746.570312 C 1556.46875 1747.011719 1558.289062 1747.390625 1560.089844 1747.789062 C 1562.308594 1748.300781 1564.511719 1748.828125 1566.75 1749.289062 C 1567.148438 1749.378906 1567.558594 1749.441406 1567.949219 1749.519531 C 1573.109375 1750.570312 1578.308594 1751.539062 1583.558594 1752.378906 L 1583.820312 1660.171875 "/>
<path fill-rule="nonzero" fill="rgb(59.179688%, 59.179688%, 59.179688%)" fill-opacity="1" d="M 1833.761719 1667.871094 L 1833.5 1760.089844 L 1770.140625 1739.199219 L 1770.398438 1646.988281 L 1833.761719 1667.871094 "/>
<path fill-rule="nonzero" fill="rgb(70.117188%, 70.117188%, 70.117188%)" fill-opacity="1" d="M 1895.75 1725.800781 C 1895.988281 1725.550781 1896.199219 1725.308594 1896.421875 1725.078125 C 1896.589844 1724.898438 1896.75 1724.71875 1896.910156 1724.550781 C 1897.109375 1724.328125 1897.28125 1724.140625 1897.449219 1723.941406 C 1897.578125 1723.800781 1897.71875 1723.648438 1897.828125 1723.519531 C 1898 1723.328125 1898.140625 1723.171875 1898.28125 1723.011719 C 1898.359375 1722.910156 1898.460938 1722.800781 1898.53125 1722.71875 C 1898.710938 1722.511719 1898.851562 1722.339844 1898.96875 1722.199219 L 1898.988281 1722.179688 C 1899.101562 1722.039062 1899.191406 1721.929688 1899.21875 1721.890625 L 1899.480469 1629.679688 C 1899.449219 1629.71875 1899.371094 1629.820312 1899.261719 1629.960938 L 1899.160156 1630.070312 C 1899.058594 1630.191406 1898.941406 1630.328125 1898.789062 1630.511719 L 1898.558594 1630.769531 C 1898.421875 1630.941406 1898.261719 1631.109375 1898.078125 1631.320312 C 1897.980469 1631.441406 1897.851562 1631.578125 1897.730469 1631.710938 C 1897.558594 1631.910156 1897.371094 1632.121094 1897.171875 1632.339844 C 1897.019531 1632.511719 1896.851562 1632.691406 1896.679688 1632.871094 C 1896.46875 1633.101562 1896.25 1633.339844 1896.011719 1633.589844 C 1895.828125 1633.78125 1895.640625 1633.980469 1895.441406 1634.179688 C 1895.191406 1634.449219 1894.929688 1634.71875 1894.648438 1635 C 1894.429688 1635.230469 1894.199219 1635.449219 1893.96875 1635.679688 C 1893.691406 1635.96875 1893.390625 1636.269531 1893.078125 1636.570312 C 1892.828125 1636.820312 1892.570312 1637.070312 1892.300781 1637.320312 C 1891.980469 1637.628906 1891.648438 1637.941406 1891.308594 1638.261719 C 1891.070312 1638.488281 1890.820312 1638.71875 1890.570312 1638.949219 C 1889.640625 1639.808594 1888.640625 1640.699219 1887.570312 1641.609375 C 1887.378906 1641.78125 1887.191406 1641.941406 1887 1642.101562 C 1885.871094 1643.058594 1884.679688 1644.03125 1883.421875 1645.011719 L 1883.199219 1645.171875 C 1881.96875 1646.128906 1880.660156 1647.078125 1879.308594 1648.03125 C 1879.121094 1648.160156 1878.941406 1648.289062 1878.75 1648.421875 C 1877.390625 1649.359375 1875.96875 1650.28125 1874.5 1651.179688 C 1873.898438 1651.550781 1873.300781 1651.921875 1872.691406 1652.28125 C 1871.480469 1652.980469 1870.238281 1653.648438 1868.980469 1654.308594 C 1868.601562 1654.511719 1868.21875 1654.699219 1867.839844 1654.898438 C 1866.898438 1655.378906 1865.960938 1655.839844 1865.011719 1656.300781 C 1864.628906 1656.480469 1864.261719 1656.660156 1863.878906 1656.839844 C 1862.601562 1657.441406 1861.320312 1658.019531 1860.03125 1658.578125 L 1859.921875 1658.628906 C 1858.648438 1659.179688 1857.398438 1659.699219 1856.148438 1660.210938 C 1855.761719 1660.359375 1855.371094 1660.519531 1854.988281 1660.671875 C 1854.058594 1661.039062 1853.140625 1661.398438 1852.25 1661.738281 C 1851.878906 1661.878906 1851.511719 1662.019531 1851.148438 1662.160156 C 1849.949219 1662.609375 1848.769531 1663.039062 1847.648438 1663.441406 L 1847.511719 1663.488281 C 1846.410156 1663.878906 1845.359375 1664.238281 1844.351562 1664.578125 C 1844.019531 1664.691406 1843.71875 1664.789062 1843.398438 1664.898438 C 1842.648438 1665.148438 1841.929688 1665.390625 1841.25 1665.609375 C 1840.988281 1665.691406 1840.71875 1665.78125 1840.46875 1665.859375 C 1839.609375 1666.128906 1838.800781 1666.390625 1838.078125 1666.609375 L 1837.871094 1666.671875 C 1837.199219 1666.871094 1836.609375 1667.050781 1836.089844 1667.210938 C 1835.910156 1667.261719 1835.761719 1667.300781 1835.609375 1667.351562 C 1835.238281 1667.449219 1834.898438 1667.550781 1834.628906 1667.628906 C 1834.53125 1667.660156 1834.398438 1667.691406 1834.320312 1667.71875 C 1834.070312 1667.789062 1833.839844 1667.851562 1833.761719 1667.871094 L 1833.5 1760.089844 C 1833.578125 1760.058594 1833.808594 1760 1834.101562 1759.921875 L 1834.308594 1759.859375 C 1834.589844 1759.78125 1834.949219 1759.671875 1835.371094 1759.550781 C 1835.519531 1759.511719 1835.648438 1759.46875 1835.808594 1759.421875 C 1836.390625 1759.25 1837.019531 1759.058594 1837.78125 1758.828125 L 1837.800781 1758.828125 C 1838.519531 1758.601562 1839.339844 1758.351562 1840.199219 1758.070312 C 1840.308594 1758.039062 1840.398438 1758.011719 1840.511719 1757.96875 C 1840.671875 1757.921875 1840.859375 1757.859375 1841.019531 1757.808594 C 1841.691406 1757.589844 1842.378906 1757.359375 1843.109375 1757.121094 C 1843.441406 1757.011719 1843.75 1756.910156 1844.089844 1756.789062 C 1845.101562 1756.449219 1846.148438 1756.089844 1847.25 1755.699219 L 1847.390625 1755.648438 C 1848.519531 1755.25 1849.710938 1754.808594 1850.910156 1754.359375 C 1851.25 1754.230469 1851.601562 1754.101562 1851.949219 1753.96875 C 1852.871094 1753.621094 1853.800781 1753.25 1854.738281 1752.878906 C 1855.121094 1752.730469 1855.5 1752.578125 1855.878906 1752.421875 C 1857.148438 1751.898438 1858.429688 1751.371094 1859.71875 1750.808594 L 1859.761719 1750.800781 C 1861.050781 1750.238281 1862.339844 1749.648438 1863.628906 1749.050781 C 1864 1748.871094 1864.371094 1748.691406 1864.738281 1748.519531 C 1865.699219 1748.058594 1866.648438 1747.589844 1867.589844 1747.101562 C 1867.96875 1746.910156 1868.339844 1746.71875 1868.710938 1746.53125 C 1869.980469 1745.859375 1871.21875 1745.191406 1872.421875 1744.488281 C 1872.878906 1744.21875 1873.339844 1743.949219 1873.789062 1743.679688 C 1873.941406 1743.589844 1874.078125 1743.488281 1874.230469 1743.390625 C 1875.699219 1742.488281 1877.128906 1741.570312 1878.488281 1740.628906 C 1878.679688 1740.5 1878.859375 1740.371094 1879.050781 1740.238281 C 1880.398438 1739.289062 1881.699219 1738.339844 1882.941406 1737.390625 L 1883.160156 1737.21875 C 1884.421875 1736.238281 1885.609375 1735.269531 1886.738281 1734.308594 C 1886.929688 1734.148438 1887.121094 1733.988281 1887.308594 1733.828125 C 1888.371094 1732.921875 1889.371094 1732.019531 1890.308594 1731.160156 L 1890.539062 1730.960938 C 1890.71875 1730.789062 1890.871094 1730.640625 1891.050781 1730.480469 C 1891.390625 1730.148438 1891.730469 1729.839844 1892.050781 1729.53125 C 1892.308594 1729.269531 1892.558594 1729.03125 1892.820312 1728.78125 C 1893.128906 1728.480469 1893.421875 1728.191406 1893.710938 1727.898438 C 1893.941406 1727.660156 1894.171875 1727.441406 1894.390625 1727.21875 C 1894.671875 1726.929688 1894.929688 1726.660156 1895.179688 1726.390625 C 1895.378906 1726.191406 1895.570312 1725.988281 1895.75 1725.800781 "/>
<path fill-rule="nonzero" fill="rgb(76.951599%, 76.951599%, 76.951599%)" fill-opacity="1" d="M 1528.871094 1646.988281 L 1528.601562 1739.199219 L 1465.488281 1760.089844 L 1465.75 1667.878906 L 1528.871094 1646.988281 "/>
<path fill-rule="nonzero" fill="rgb(52.146912%, 52.146912%, 52.146912%)" fill-opacity="1" d="M 1465.488281 1760.089844 L 1465.75 1667.878906 C 1465.648438 1667.851562 1465.378906 1667.769531 1465.019531 1667.671875 C 1464.890625 1667.628906 1464.738281 1667.589844 1464.570312 1667.550781 C 1464.359375 1667.480469 1464.078125 1667.398438 1463.808594 1667.328125 C 1463.628906 1667.269531 1463.460938 1667.21875 1463.25 1667.160156 C 1462.941406 1667.070312 1462.570312 1666.960938 1462.210938 1666.851562 C 1461.980469 1666.78125 1461.78125 1666.71875 1461.53125 1666.648438 C 1461.121094 1666.519531 1460.660156 1666.378906 1460.199219 1666.238281 C 1459.949219 1666.160156 1459.710938 1666.089844 1459.441406 1666 C 1458.699219 1665.769531 1457.910156 1665.519531 1457.078125 1665.238281 C 1456.910156 1665.191406 1456.710938 1665.121094 1456.539062 1665.058594 C 1455.851562 1664.839844 1455.148438 1664.601562 1454.410156 1664.351562 C 1454.101562 1664.25 1453.769531 1664.128906 1453.460938 1664.03125 C 1452.828125 1663.808594 1452.199219 1663.589844 1451.53125 1663.351562 C 1451.128906 1663.210938 1450.710938 1663.058594 1450.289062 1662.910156 C 1449.699219 1662.691406 1449.101562 1662.46875 1448.480469 1662.238281 C 1448.019531 1662.070312 1447.550781 1661.890625 1447.078125 1661.71875 C 1446.488281 1661.488281 1445.890625 1661.261719 1445.28125 1661.019531 C 1444.921875 1660.878906 1444.550781 1660.730469 1444.179688 1660.589844 C 1443.109375 1660.160156 1442.03125 1659.71875 1440.929688 1659.261719 C 1440.589844 1659.109375 1440.238281 1658.96875 1439.890625 1658.820312 C 1438.53125 1658.238281 1437.148438 1657.628906 1435.78125 1657 C 1435.550781 1656.890625 1435.308594 1656.769531 1435.070312 1656.660156 C 1433.921875 1656.121094 1432.78125 1655.570312 1431.648438 1655 C 1431.28125 1654.808594 1430.910156 1654.621094 1430.539062 1654.429688 C 1429.199219 1653.730469 1427.871094 1653.019531 1426.578125 1652.28125 C 1410.578125 1643.039062 1400.109375 1630.328125 1399.558594 1629.660156 L 1399.300781 1721.878906 C 1399.851562 1722.539062 1410.320312 1735.25 1426.320312 1744.488281 C 1427.609375 1745.230469 1428.941406 1745.949219 1430.28125 1746.648438 C 1430.648438 1746.828125 1431.011719 1747.019531 1431.378906 1747.210938 C 1432.511719 1747.78125 1433.648438 1748.328125 1434.800781 1748.871094 C 1435.039062 1748.980469 1435.28125 1749.101562 1435.53125 1749.21875 C 1436.890625 1749.851562 1438.269531 1750.449219 1439.628906 1751.039062 C 1439.980469 1751.191406 1440.320312 1751.328125 1440.660156 1751.46875 C 1441.761719 1751.929688 1442.851562 1752.378906 1443.941406 1752.808594 C 1444.140625 1752.890625 1444.339844 1752.980469 1444.550781 1753.058594 C 1444.699219 1753.121094 1444.851562 1753.171875 1445 1753.230469 C 1445.640625 1753.480469 1446.261719 1753.710938 1446.878906 1753.949219 C 1447.320312 1754.121094 1447.761719 1754.289062 1448.199219 1754.449219 C 1448.839844 1754.691406 1449.460938 1754.910156 1450.078125 1755.128906 C 1450.480469 1755.28125 1450.878906 1755.429688 1451.269531 1755.570312 C 1451.941406 1755.800781 1452.570312 1756.019531 1453.199219 1756.238281 C 1453.511719 1756.351562 1453.839844 1756.460938 1454.148438 1756.558594 C 1455.019531 1756.859375 1455.851562 1757.140625 1456.648438 1757.398438 L 1456.789062 1757.449219 C 1457.640625 1757.730469 1458.441406 1757.980469 1459.191406 1758.21875 C 1459.398438 1758.28125 1459.570312 1758.328125 1459.761719 1758.390625 C 1460.300781 1758.558594 1460.828125 1758.730469 1461.300781 1758.871094 C 1461.488281 1758.921875 1461.628906 1758.96875 1461.800781 1759.019531 C 1462.21875 1759.148438 1462.628906 1759.269531 1462.988281 1759.371094 C 1463.199219 1759.441406 1463.371094 1759.488281 1463.558594 1759.539062 C 1463.828125 1759.621094 1464.089844 1759.699219 1464.308594 1759.761719 L 1464.410156 1759.789062 C 1464.609375 1759.839844 1464.761719 1759.878906 1464.910156 1759.929688 L 1465.058594 1759.96875 C 1465.269531 1760.03125 1465.421875 1760.070312 1465.488281 1760.089844 "/>
<path fill-rule="nonzero" fill="rgb(66.992188%, 66.992188%, 66.992188%)" fill-opacity="1" d="M 1696.558594 1700.851562 L 1696.300781 1793.058594 L 1715.300781 1752.359375 L 1715.558594 1660.148438 L 1696.558594 1700.851562 "/>
<path fill-rule="nonzero" fill="rgb(50.193787%, 50.193787%, 50.193787%)" fill-opacity="1" d="M 1603.289062 1700.828125 L 1603.03125 1793.050781 L 1583.558594 1752.378906 L 1583.820312 1660.171875 L 1603.289062 1700.828125 "/>
<path fill-rule="nonzero" fill="rgb(58.006287%, 58.006287%, 58.006287%)" fill-opacity="1" d="M 1694.691406 1793.378906 L 1694.898438 1793.339844 C 1695.660156 1793.191406 1696.148438 1793.089844 1696.300781 1793.058594 L 1696.558594 1700.851562 C 1696.410156 1700.878906 1695.929688 1700.980469 1695.171875 1701.128906 L 1694.878906 1701.179688 C 1694.558594 1701.238281 1694.21875 1701.308594 1693.820312 1701.390625 C 1693.660156 1701.410156 1693.460938 1701.449219 1693.289062 1701.480469 C 1692.910156 1701.550781 1692.519531 1701.621094 1692.078125 1701.699219 C 1691.890625 1701.738281 1691.660156 1701.78125 1691.460938 1701.808594 C 1691.011719 1701.890625 1690.550781 1701.96875 1690.050781 1702.058594 C 1689.78125 1702.101562 1689.480469 1702.148438 1689.199219 1702.199219 C 1688.710938 1702.28125 1688.210938 1702.371094 1687.679688 1702.449219 C 1687.351562 1702.511719 1687.011719 1702.558594 1686.660156 1702.621094 C 1686.140625 1702.699219 1685.601562 1702.78125 1685.050781 1702.871094 C 1684.660156 1702.929688 1684.261719 1702.980469 1683.851562 1703.050781 C 1683.28125 1703.128906 1682.691406 1703.210938 1682.101562 1703.300781 C 1681.679688 1703.359375 1681.269531 1703.421875 1680.851562 1703.46875 C 1680.210938 1703.558594 1679.558594 1703.648438 1678.898438 1703.730469 C 1678.460938 1703.789062 1678.019531 1703.851562 1677.570312 1703.898438 C 1676.839844 1704 1676.078125 1704.089844 1675.320312 1704.179688 C 1674.929688 1704.21875 1674.550781 1704.269531 1674.148438 1704.308594 C 1672.988281 1704.441406 1671.808594 1704.570312 1670.589844 1704.691406 C 1668.5 1704.898438 1666.328125 1705.078125 1664.121094 1705.238281 C 1663.820312 1705.261719 1663.519531 1705.289062 1663.210938 1705.308594 C 1661.089844 1705.449219 1658.921875 1705.558594 1656.738281 1705.648438 C 1656.308594 1705.660156 1655.878906 1705.679688 1655.449219 1705.691406 C 1653.621094 1705.75 1651.78125 1705.789062 1649.941406 1705.789062 C 1648.5 1705.789062 1647.070312 1705.769531 1645.640625 1705.730469 L 1645.511719 1705.730469 C 1641.460938 1705.621094 1637.449219 1705.390625 1633.601562 1705.070312 L 1633.421875 1705.058594 C 1632.03125 1704.941406 1630.660156 1704.820312 1629.320312 1704.679688 C 1629.121094 1704.660156 1628.929688 1704.640625 1628.730469 1704.621094 C 1627.589844 1704.511719 1626.460938 1704.390625 1625.359375 1704.261719 C 1624.949219 1704.210938 1624.570312 1704.160156 1624.160156 1704.121094 C 1623.320312 1704.019531 1622.46875 1703.910156 1621.660156 1703.808594 C 1621.171875 1703.75 1620.691406 1703.679688 1620.210938 1703.621094 C 1619.519531 1703.53125 1618.828125 1703.429688 1618.171875 1703.339844 C 1617.609375 1703.261719 1617.070312 1703.179688 1616.53125 1703.101562 C 1616.070312 1703.039062 1615.589844 1702.96875 1615.140625 1702.898438 C 1614.621094 1702.820312 1614.140625 1702.738281 1613.640625 1702.671875 C 1613.21875 1702.601562 1612.78125 1702.53125 1612.371094 1702.46875 C 1611.910156 1702.390625 1611.488281 1702.320312 1611.058594 1702.25 C 1610.660156 1702.191406 1610.25 1702.121094 1609.878906 1702.050781 C 1609.480469 1701.988281 1609.121094 1701.921875 1608.761719 1701.859375 C 1608.429688 1701.800781 1608.089844 1701.738281 1607.789062 1701.691406 C 1607.390625 1701.621094 1607.050781 1701.558594 1606.699219 1701.488281 C 1606.480469 1701.449219 1606.230469 1701.398438 1606.03125 1701.371094 C 1605.648438 1701.300781 1605.339844 1701.238281 1605.039062 1701.179688 C 1604.929688 1701.160156 1604.789062 1701.128906 1604.691406 1701.109375 C 1603.921875 1700.960938 1603.441406 1700.859375 1603.289062 1700.828125 L 1603.03125 1793.050781 C 1603.171875 1793.070312 1603.660156 1793.171875 1604.421875 1793.320312 C 1604.539062 1793.339844 1604.691406 1793.371094 1604.808594 1793.398438 C 1605.109375 1793.460938 1605.398438 1793.511719 1605.769531 1793.578125 C 1605.96875 1793.621094 1606.21875 1793.660156 1606.449219 1793.699219 C 1606.800781 1793.769531 1607.140625 1793.828125 1607.539062 1793.898438 C 1607.789062 1793.949219 1608.078125 1794 1608.351562 1794.050781 C 1608.769531 1794.121094 1609.191406 1794.191406 1609.648438 1794.269531 C 1609.988281 1794.328125 1610.371094 1794.390625 1610.730469 1794.449219 C 1611.179688 1794.53125 1611.621094 1794.601562 1612.109375 1794.679688 C 1612.511719 1794.75 1612.949219 1794.808594 1613.378906 1794.878906 C 1613.871094 1794.960938 1614.351562 1795.03125 1614.871094 1795.109375 C 1615.328125 1795.179688 1615.820312 1795.25 1616.308594 1795.320312 C 1616.730469 1795.378906 1617.121094 1795.441406 1617.558594 1795.511719 C 1617.671875 1795.519531 1617.800781 1795.539062 1617.910156 1795.550781 C 1618.558594 1795.648438 1619.25 1795.738281 1619.929688 1795.828125 C 1620.421875 1795.890625 1620.898438 1795.960938 1621.398438 1796.019531 C 1622.179688 1796.121094 1622.988281 1796.21875 1623.789062 1796.308594 C 1624.238281 1796.371094 1624.671875 1796.421875 1625.121094 1796.46875 C 1626.199219 1796.601562 1627.300781 1796.710938 1628.421875 1796.828125 C 1628.640625 1796.851562 1628.839844 1796.871094 1629.058594 1796.898438 C 1630.390625 1797.03125 1631.761719 1797.148438 1633.140625 1797.269531 L 1633.261719 1797.28125 L 1633.351562 1797.28125 C 1637.199219 1797.601562 1641.210938 1797.839844 1645.25 1797.941406 L 1645.378906 1797.941406 C 1646.808594 1797.980469 1648.238281 1798 1649.679688 1798 C 1651.519531 1798 1653.359375 1797.960938 1655.191406 1797.898438 C 1655.398438 1797.898438 1655.621094 1797.898438 1655.828125 1797.890625 C 1656.050781 1797.890625 1656.261719 1797.871094 1656.480469 1797.859375 C 1658.671875 1797.78125 1660.828125 1797.671875 1662.949219 1797.519531 C 1663.261719 1797.5 1663.558594 1797.480469 1663.859375 1797.449219 C 1666.070312 1797.289062 1668.238281 1797.109375 1670.328125 1796.898438 L 1670.351562 1796.898438 C 1671.558594 1796.78125 1672.738281 1796.660156 1673.890625 1796.53125 C 1674.289062 1796.480469 1674.660156 1796.429688 1675.050781 1796.390625 C 1675.820312 1796.300781 1676.578125 1796.210938 1677.308594 1796.121094 C 1677.761719 1796.058594 1678.191406 1796 1678.628906 1795.949219 C 1679.289062 1795.859375 1679.949219 1795.769531 1680.589844 1795.691406 C 1681.011719 1795.628906 1681.421875 1795.570312 1681.828125 1795.511719 C 1682.429688 1795.429688 1683.019531 1795.339844 1683.589844 1795.261719 C 1684 1795.199219 1684.390625 1795.140625 1684.789062 1795.078125 C 1685.339844 1794.988281 1685.871094 1794.910156 1686.398438 1794.828125 C 1686.738281 1794.769531 1687.089844 1794.71875 1687.421875 1794.660156 C 1687.949219 1794.578125 1688.449219 1794.488281 1688.941406 1794.410156 C 1689.230469 1794.359375 1689.511719 1794.320312 1689.78125 1794.269531 C 1690.300781 1794.179688 1690.769531 1794.101562 1691.21875 1794.019531 C 1691.421875 1793.980469 1691.628906 1793.949219 1691.808594 1793.921875 L 1691.980469 1793.890625 C 1692.390625 1793.808594 1692.71875 1793.75 1693.070312 1793.691406 C 1693.230469 1793.660156 1693.410156 1793.628906 1693.550781 1793.601562 C 1693.980469 1793.519531 1694.351562 1793.449219 1694.691406 1793.378906 "/>
<path fill-rule="nonzero" fill="rgb(86.326599%, 86.326599%, 86.326599%)" fill-opacity="1" d="M 1551.410156 1579.738281 C 1605.628906 1611.050781 1693.199219 1611.03125 1747.050781 1579.738281 C 1800.890625 1548.449219 1800.628906 1497.730469 1746.410156 1466.421875 C 1692.210938 1435.128906 1604.609375 1435.128906 1550.769531 1466.421875 C 1496.929688 1497.710938 1497.210938 1548.449219 1551.410156 1579.738281 M 1833.761719 1667.871094 L 1770.398438 1646.988281 C 1752.929688 1652.730469 1734.5 1657.121094 1715.558594 1660.148438 L 1696.558594 1700.851562 C 1695.359375 1701.101562 1672.539062 1705.800781 1649.941406 1705.789062 C 1627.359375 1705.789062 1604.488281 1701.078125 1603.289062 1700.828125 L 1583.820312 1660.171875 C 1564.828125 1657.121094 1546.378906 1652.710938 1528.871094 1646.988281 L 1465.75 1667.878906 C 1464.589844 1667.558594 1442.621094 1661.539062 1426.578125 1652.28125 C 1410.578125 1643.039062 1400.109375 1630.328125 1399.558594 1629.660156 L 1435.410156 1593.03125 C 1425.449219 1582.890625 1417.808594 1572.230469 1412.480469 1561.238281 L 1342.210938 1550.101562 C 1341.78125 1549.398438 1333.550781 1536.160156 1333.480469 1523.078125 C 1333.410156 1510 1341.460938 1496.769531 1341.878906 1496.078125 L 1412.078125 1484.929688 C 1417.261719 1473.960938 1424.769531 1463.269531 1434.621094 1453.128906 L 1398.359375 1416.5 C 1398.910156 1415.828125 1409.230469 1403.148438 1425.160156 1393.898438 C 1441.089844 1384.640625 1462.941406 1378.621094 1464.089844 1378.300781 L 1527.441406 1399.191406 C 1544.921875 1393.449219 1563.351562 1389.058594 1582.28125 1386.019531 L 1601.289062 1345.328125 C 1602.488281 1345.078125 1625.300781 1340.371094 1647.878906 1340.371094 C 1670.460938 1340.371094 1693.351562 1345.089844 1694.558594 1345.339844 L 1714.019531 1386.011719 C 1733.011719 1389.058594 1751.46875 1393.460938 1768.980469 1399.179688 L 1832.128906 1378.28125 C 1833.28125 1378.601562 1855.230469 1384.640625 1871.230469 1393.878906 C 1887.269531 1403.140625 1897.761719 1415.828125 1898.308594 1416.5 L 1862.441406 1453.140625 C 1872.371094 1463.269531 1880.03125 1473.941406 1885.359375 1484.929688 L 1955.628906 1496.070312 C 1956.058594 1496.769531 1964.261719 1510 1964.339844 1523.078125 C 1964.441406 1536.171875 1956.390625 1549.398438 1955.96875 1550.101562 L 1885.789062 1561.21875 C 1880.589844 1572.21875 1873.070312 1582.910156 1863.21875 1593.050781 L 1899.480469 1629.679688 C 1898.941406 1630.339844 1888.621094 1643.019531 1872.691406 1652.28125 C 1856.761719 1661.53125 1834.910156 1667.558594 1833.761719 1667.871094 "/>
<g clip-path="url(#clip-15)">
<g clip-path="url(#clip-16)">
<g clip-path="url(#clip-17)">
<path fill-rule="nonzero" fill="url(#linear-pattern-0)" d="M 2202.988281 1437.699219 L 2202.988281 1733.898438 L 2722.820312 1733.898438 L 2722.820312 1437.699219 Z M 2202.988281 1437.699219 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-18)">
<g clip-path="url(#clip-19)">
<g clip-path="url(#clip-20)">
<path fill-rule="nonzero" fill="url(#linear-pattern-1)" d="M 2441.449219 1573.191406 L 2441.449219 1798.101562 L 2722.820312 1798.101562 L 2722.820312 1573.191406 Z M 2441.449219 1573.191406 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-21)">
<g clip-path="url(#clip-22)">
<g clip-path="url(#clip-23)">
<path fill-rule="nonzero" fill="url(#linear-pattern-2)" d="M 2202.988281 1598.398438 L 2202.988281 1798.101562 L 2441.449219 1798.101562 L 2441.449219 1598.398438 Z M 2202.988281 1598.398438 "/>
</g>
</g>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2481.328125 1517.199219 L 2453.148438 1501.351562 C 2449.390625 1499.238281 2449.25 1495.808594 2452.828125 1493.730469 C 2456.410156 1491.660156 2462.421875 1491.691406 2466.179688 1493.800781 L 2494.351562 1509.648438 C 2498.109375 1511.769531 2498.261719 1515.199219 2494.679688 1517.269531 C 2491.089844 1519.351562 2485.089844 1519.320312 2481.328125 1517.199219 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2603.558594 1585.96875 L 2508.769531 1532.640625 C 2505.011719 1530.519531 2504.859375 1527.101562 2508.441406 1525.019531 C 2512.019531 1522.941406 2518.03125 1522.980469 2521.789062 1525.089844 L 2616.589844 1578.429688 C 2620.351562 1580.539062 2620.488281 1583.96875 2616.910156 1586.050781 C 2613.328125 1588.121094 2607.320312 1588.089844 2603.558594 1585.96875 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2448.121094 1536.449219 L 2419.941406 1520.601562 C 2416.179688 1518.480469 2416.039062 1515.058594 2419.621094 1512.980469 C 2423.199219 1510.898438 2429.210938 1510.941406 2432.96875 1513.050781 L 2461.140625 1528.898438 C 2464.898438 1531.019531 2465.050781 1534.449219 2461.46875 1536.519531 C 2457.878906 1538.601562 2451.878906 1538.570312 2448.121094 1536.449219 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2570.351562 1605.21875 L 2475.550781 1551.890625 C 2471.800781 1549.769531 2471.648438 1546.339844 2475.230469 1544.269531 C 2478.808594 1542.191406 2484.820312 1542.21875 2488.578125 1544.339844 L 2583.378906 1597.671875 C 2587.140625 1599.789062 2587.28125 1603.21875 2583.699219 1605.289062 C 2580.121094 1607.371094 2574.109375 1607.339844 2570.351562 1605.21875 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2414.898438 1555.699219 L 2386.730469 1539.851562 C 2382.96875 1537.730469 2382.828125 1534.300781 2386.410156 1532.230469 C 2389.988281 1530.148438 2396 1530.179688 2399.761719 1532.300781 L 2427.929688 1548.148438 C 2431.691406 1550.269531 2431.839844 1553.691406 2428.25 1555.769531 C 2424.671875 1557.851562 2418.671875 1557.808594 2414.898438 1555.699219 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2537.140625 1624.46875 L 2442.339844 1571.140625 C 2438.578125 1569.019531 2438.441406 1565.589844 2442.019531 1563.511719 C 2445.601562 1561.441406 2451.609375 1561.46875 2455.371094 1563.589844 L 2550.171875 1616.921875 C 2553.929688 1619.039062 2554.070312 1622.46875 2550.488281 1624.539062 C 2546.910156 1626.621094 2540.898438 1626.578125 2537.140625 1624.46875 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2381.691406 1574.941406 L 2353.519531 1559.089844 C 2349.761719 1556.980469 2349.609375 1553.550781 2353.199219 1551.46875 C 2356.78125 1549.398438 2362.78125 1549.429688 2366.539062 1551.550781 L 2394.71875 1567.398438 C 2398.480469 1569.511719 2398.621094 1572.941406 2395.039062 1575.019531 C 2391.460938 1577.089844 2385.449219 1577.058594 2381.691406 1574.941406 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2503.929688 1643.71875 L 2409.128906 1590.378906 C 2405.371094 1588.269531 2405.230469 1584.839844 2408.808594 1582.761719 C 2412.390625 1580.691406 2418.398438 1580.71875 2422.160156 1582.828125 L 2516.949219 1636.171875 C 2520.710938 1638.28125 2520.859375 1641.710938 2517.28125 1643.789062 C 2513.699219 1645.859375 2507.691406 1645.828125 2503.929688 1643.71875 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2348.480469 1594.191406 L 2320.308594 1578.339844 C 2316.550781 1576.230469 2316.398438 1572.800781 2319.980469 1570.71875 C 2323.570312 1568.648438 2329.570312 1568.679688 2333.328125 1570.789062 L 2361.511719 1586.640625 C 2365.269531 1588.761719 2365.410156 1592.191406 2361.828125 1594.269531 C 2358.25 1596.339844 2352.238281 1596.308594 2348.480469 1594.191406 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 2470.71875 1662.960938 L 2375.921875 1609.628906 C 2372.160156 1607.511719 2372.011719 1604.089844 2375.601562 1602.011719 C 2379.179688 1599.929688 2385.179688 1599.96875 2388.949219 1602.078125 L 2483.738281 1655.421875 C 2487.5 1657.53125 2487.648438 1660.960938 2484.070312 1663.039062 C 2480.488281 1665.109375 2474.480469 1665.078125 2470.71875 1662.960938 "/>
<path fill-rule="nonzero" fill="rgb(100%, 71.679688%, 26.268005%)" fill-opacity="1" d="M 1593.980469 1251.101562 L 1355.519531 1115.601562 L 1636.890625 954.898438 L 1875.351562 1090.398438 L 1593.980469 1251.101562 "/>
<g clip-path="url(#clip-24)">
<g clip-path="url(#clip-25)">
<g clip-path="url(#clip-26)">
<path fill-rule="nonzero" fill="url(#linear-pattern-3)" d="M 1593.980469 1090.398438 L 1593.980469 1315.300781 L 1875.351562 1315.300781 L 1875.351562 1090.398438 Z M 1593.980469 1090.398438 "/>
</g>
</g>
</g>
<path fill-rule="nonzero" fill="rgb(98.046875%, 54.492188%, 7.446289%)" fill-opacity="1" d="M 1355.519531 1115.601562 L 1593.980469 1251.101562 L 1593.980469 1315.300781 L 1355.519531 1179.800781 L 1355.519531 1115.601562 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1633.851562 1034.398438 L 1605.679688 1018.550781 C 1601.921875 1016.441406 1601.769531 1013.011719 1605.351562 1010.929688 C 1608.929688 1008.859375 1614.941406 1008.890625 1618.699219 1011.011719 L 1646.878906 1026.859375 C 1650.640625 1028.96875 1650.78125 1032.398438 1647.199219 1034.480469 C 1643.621094 1036.550781 1637.609375 1036.519531 1633.851562 1034.398438 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1756.089844 1103.179688 L 1661.289062 1049.839844 C 1657.53125 1047.730469 1657.378906 1044.300781 1660.96875 1042.21875 C 1664.550781 1040.148438 1670.550781 1040.179688 1674.308594 1042.289062 L 1769.109375 1095.628906 C 1772.871094 1097.738281 1773.019531 1101.171875 1769.441406 1103.25 C 1765.851562 1105.320312 1759.851562 1105.289062 1756.089844 1103.179688 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1600.640625 1053.648438 L 1572.46875 1037.800781 C 1568.710938 1035.691406 1568.558594 1032.261719 1572.140625 1030.179688 C 1575.71875 1028.109375 1581.730469 1028.140625 1585.488281 1030.25 L 1613.660156 1046.101562 C 1617.421875 1048.21875 1617.570312 1051.648438 1613.988281 1053.71875 C 1610.410156 1055.800781 1604.398438 1055.769531 1600.640625 1053.648438 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1722.878906 1122.421875 L 1628.078125 1069.089844 C 1624.320312 1066.96875 1624.171875 1063.539062 1627.75 1061.46875 C 1631.339844 1059.390625 1637.339844 1059.429688 1641.101562 1061.539062 L 1735.898438 1114.878906 C 1739.660156 1116.988281 1739.808594 1120.421875 1736.21875 1122.5 C 1732.640625 1124.570312 1726.640625 1124.539062 1722.878906 1122.421875 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1567.429688 1072.898438 L 1539.261719 1057.050781 C 1535.5 1054.929688 1535.351562 1051.5 1538.929688 1049.429688 C 1542.511719 1047.351562 1548.519531 1047.390625 1552.28125 1049.5 L 1580.449219 1065.351562 C 1584.210938 1067.46875 1584.359375 1070.898438 1580.78125 1072.96875 C 1577.199219 1075.050781 1571.191406 1075.011719 1567.429688 1072.898438 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1689.660156 1141.671875 L 1594.871094 1088.339844 C 1591.109375 1086.21875 1590.960938 1082.789062 1594.539062 1080.71875 C 1598.121094 1078.640625 1604.128906 1078.671875 1607.890625 1080.789062 L 1702.691406 1134.121094 C 1706.449219 1136.238281 1706.589844 1139.671875 1703.011719 1141.738281 C 1699.429688 1143.820312 1693.429688 1143.789062 1689.660156 1141.671875 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1534.21875 1092.148438 L 1506.039062 1076.300781 C 1502.28125 1074.179688 1502.140625 1070.75 1505.71875 1068.679688 C 1509.300781 1066.601562 1515.308594 1066.628906 1519.070312 1068.75 L 1547.238281 1084.601562 C 1551 1086.710938 1551.148438 1090.140625 1547.570312 1092.21875 C 1543.980469 1094.289062 1537.980469 1094.261719 1534.21875 1092.148438 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1656.449219 1160.921875 L 1561.660156 1107.578125 C 1557.898438 1105.46875 1557.75 1102.039062 1561.328125 1099.960938 C 1564.910156 1097.890625 1570.921875 1097.921875 1574.679688 1100.039062 L 1669.480469 1153.371094 C 1673.238281 1155.488281 1673.378906 1158.910156 1669.800781 1160.988281 C 1666.21875 1163.070312 1660.210938 1163.03125 1656.449219 1160.921875 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1501.011719 1111.390625 L 1472.828125 1095.539062 C 1469.070312 1093.429688 1468.929688 1090 1472.511719 1087.921875 C 1476.089844 1085.851562 1482.101562 1085.878906 1485.859375 1088 L 1514.03125 1103.851562 C 1517.789062 1105.960938 1517.941406 1109.390625 1514.351562 1111.46875 C 1510.769531 1113.539062 1504.769531 1113.511719 1501.011719 1111.390625 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1623.238281 1180.171875 L 1528.441406 1126.828125 C 1524.679688 1124.71875 1524.539062 1121.289062 1528.121094 1119.210938 C 1531.699219 1117.140625 1537.710938 1117.171875 1541.46875 1119.28125 L 1636.269531 1172.621094 C 1640.03125 1174.730469 1640.171875 1178.160156 1636.589844 1180.238281 C 1633.011719 1182.308594 1627 1182.28125 1623.238281 1180.171875 "/>
<g clip-path="url(#clip-27)">
<g clip-path="url(#clip-28)">
<g clip-path="url(#clip-29)">
<path fill-rule="nonzero" fill="url(#linear-pattern-4)" d="M 1368.191406 1918.761719 L 1368.191406 2214.960938 L 1888.019531 2214.960938 L 1888.019531 1918.761719 Z M 1368.191406 1918.761719 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-30)">
<g clip-path="url(#clip-31)">
<g clip-path="url(#clip-32)">
<path fill-rule="nonzero" fill="url(#linear-pattern-5)" d="M 1606.648438 2054.261719 L 1606.648438 2279.160156 L 1888.019531 2279.160156 L 1888.019531 2054.261719 Z M 1606.648438 2054.261719 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-33)">
<g clip-path="url(#clip-34)">
<g clip-path="url(#clip-35)">
<path fill-rule="nonzero" fill="url(#linear-pattern-6)" d="M 1368.191406 2079.46875 L 1368.191406 2279.160156 L 1606.648438 2279.160156 L 1606.648438 2079.46875 Z M 1368.191406 2079.46875 "/>
</g>
</g>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1646.519531 1998.269531 L 1618.351562 1982.410156 C 1614.589844 1980.300781 1614.441406 1976.871094 1618.03125 1974.789062 C 1621.609375 1972.71875 1627.621094 1972.75 1631.378906 1974.871094 L 1659.550781 1990.71875 C 1663.308594 1992.828125 1663.460938 1996.261719 1659.871094 1998.339844 C 1656.289062 2000.410156 1650.28125 2000.378906 1646.519531 1998.269531 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1768.761719 2067.039062 L 1673.960938 2013.699219 C 1670.199219 2011.589844 1670.058594 2008.160156 1673.640625 2006.078125 C 1677.21875 2004.011719 1683.230469 2004.039062 1686.988281 2006.148438 L 1781.78125 2059.488281 C 1785.550781 2061.601562 1785.691406 2065.03125 1782.109375 2067.109375 C 1778.53125 2069.179688 1772.519531 2069.148438 1768.761719 2067.039062 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1613.308594 2017.511719 L 1585.140625 2001.660156 C 1581.378906 1999.550781 1581.230469 1996.121094 1584.820312 1994.039062 C 1588.398438 1991.96875 1594.398438 1992 1598.160156 1994.109375 L 1626.339844 2009.960938 C 1630.101562 2012.078125 1630.238281 2015.511719 1626.660156 2017.578125 C 1623.078125 2019.660156 1617.070312 2019.628906 1613.308594 2017.511719 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1735.550781 2086.28125 L 1640.75 2032.949219 C 1636.988281 2030.828125 1636.851562 2027.398438 1640.429688 2025.328125 C 1644.011719 2023.25 1650.019531 2023.289062 1653.78125 2025.398438 L 1748.570312 2078.738281 C 1752.328125 2080.851562 1752.480469 2084.28125 1748.898438 2086.359375 C 1745.320312 2088.429688 1739.308594 2088.398438 1735.550781 2086.28125 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1580.101562 2036.761719 L 1551.929688 2020.910156 C 1548.171875 2018.789062 1548.019531 2015.359375 1551.601562 2013.289062 C 1555.191406 2011.210938 1561.191406 2011.25 1564.949219 2013.359375 L 1593.128906 2029.210938 C 1596.890625 2031.328125 1597.03125 2034.761719 1593.449219 2036.828125 C 1589.871094 2038.910156 1583.859375 2038.878906 1580.101562 2036.761719 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1702.339844 2105.53125 L 1607.539062 2052.199219 C 1603.78125 2050.078125 1603.628906 2046.648438 1607.21875 2044.578125 C 1610.800781 2042.5 1616.800781 2042.53125 1620.558594 2044.648438 L 1715.359375 2097.980469 C 1719.121094 2100.101562 1719.269531 2103.53125 1715.691406 2105.601562 C 1712.101562 2107.679688 1706.101562 2107.648438 1702.339844 2105.53125 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1546.890625 2056.011719 L 1518.71875 2040.160156 C 1514.960938 2038.039062 1514.808594 2034.609375 1518.390625 2032.539062 C 1521.96875 2030.460938 1527.980469 2030.488281 1531.738281 2032.609375 L 1559.910156 2048.460938 C 1563.679688 2050.570312 1563.820312 2054 1560.238281 2056.078125 C 1556.660156 2058.160156 1550.648438 2058.121094 1546.890625 2056.011719 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1669.128906 2124.78125 L 1574.328125 2071.441406 C 1570.570312 2069.328125 1570.421875 2065.898438 1574 2063.820312 C 1577.589844 2061.75 1583.589844 2061.78125 1587.351562 2063.898438 L 1682.148438 2117.230469 C 1685.910156 2119.351562 1686.058594 2122.769531 1682.46875 2124.851562 C 1678.890625 2126.929688 1672.890625 2126.890625 1669.128906 2124.78125 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1513.679688 2075.25 L 1485.511719 2059.398438 C 1481.75 2057.289062 1481.601562 2053.859375 1485.179688 2051.78125 C 1488.761719 2049.710938 1494.769531 2049.738281 1498.53125 2051.859375 L 1526.699219 2067.710938 C 1530.460938 2069.820312 1530.609375 2073.25 1527.03125 2075.328125 C 1523.449219 2077.398438 1517.441406 2077.371094 1513.679688 2075.25 "/>
<path fill-rule="nonzero" fill="rgb(100%, 99.609375%, 100%)" fill-opacity="1" d="M 1635.921875 2144.03125 L 1541.121094 2090.691406 C 1537.359375 2088.578125 1537.210938 2085.148438 1540.789062 2083.070312 C 1544.378906 2081 1550.378906 2081.03125 1554.140625 2083.140625 L 1648.941406 2136.480469 C 1652.699219 2138.589844 1652.851562 2142.019531 1649.261719 2144.101562 C 1645.679688 2146.171875 1639.679688 2146.140625 1635.921875 2144.03125 "/>
<g clip-path="url(#clip-36)">
<g clip-path="url(#clip-37)">
<g clip-path="url(#clip-38)">
<path fill-rule="nonzero" fill="url(#linear-pattern-7)" d="M 350.382812 1405.210938 L 350.382812 1893.511719 L 1004.910156 1893.511719 L 1004.910156 1405.210938 Z M 350.382812 1405.210938 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-39)">
<g clip-path="url(#clip-40)">
<g clip-path="url(#clip-41)">
<path fill-rule="nonzero" fill="url(#linear-pattern-8)" d="M 258.476562 1830.066406 L 1264.546875 2047.128906 L 1591.75 530.5625 L 585.675781 313.5 Z M 258.476562 1830.066406 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-42)">
<g clip-path="url(#clip-43)">
<g clip-path="url(#clip-44)">
<path fill-rule="nonzero" fill="url(#linear-pattern-9)" d="M 1195.519531 795.820312 L 1195.519531 930.429688 L 1326.851562 930.429688 L 1326.851562 795.820312 Z M 1195.519531 795.820312 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-45)">
<g clip-path="url(#clip-46)">
<g clip-path="url(#clip-47)">
<path fill-rule="nonzero" fill="url(#linear-pattern-10)" d="M 1297.691406 801.898438 L 1297.691406 806.339844 L 1305.398438 806.339844 L 1305.398438 801.898438 Z M 1297.691406 801.898438 "/>
</g>
</g>
</g>
<path fill-rule="nonzero" fill="rgb(99.609375%, 65.039062%, 3.137207%)" fill-opacity="1" d="M 904.894531 815.5 L 667.511719 681.429688 C 661.136719 677.828125 655.925781 669.671875 655.925781 663.289062 C 655.925781 656.921875 661.136719 654.648438 667.511719 658.25 L 904.894531 792.328125 C 911.265625 795.929688 916.480469 804.089844 916.480469 810.460938 C 916.480469 816.828125 911.265625 819.101562 904.894531 815.5 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 1052.460938 955.710938 L 667.511719 737.179688 C 661.136719 733.578125 655.925781 725.429688 655.925781 719.050781 C 655.925781 712.679688 661.136719 710.410156 667.511719 714.011719 L 1052.460938 932.539062 C 1058.828125 936.140625 1064.039062 944.289062 1064.039062 950.671875 C 1064.039062 957.039062 1058.828125 959.308594 1052.460938 955.710938 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 1052.460938 1011.46875 L 667.511719 792.941406 C 661.136719 789.339844 655.925781 781.179688 655.925781 774.808594 C 655.925781 768.441406 661.136719 766.171875 667.511719 769.769531 L 1052.460938 988.289062 C 1058.828125 991.890625 1064.039062 1000.050781 1064.039062 1006.429688 C 1064.039062 1012.800781 1058.828125 1015.070312 1052.460938 1011.46875 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 1052.460938 1067.230469 L 667.511719 848.699219 C 661.136719 845.101562 655.925781 836.941406 655.925781 830.570312 C 655.925781 824.199219 661.136719 821.929688 667.511719 825.53125 L 1052.460938 1044.050781 C 1058.828125 1047.648438 1064.039062 1055.808594 1064.039062 1062.179688 C 1064.039062 1068.558594 1058.828125 1070.828125 1052.460938 1067.230469 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 853.550781 1010.640625 L 667.511719 904.460938 C 661.136719 900.859375 655.925781 892.699219 655.925781 886.328125 C 655.925781 879.960938 661.136719 877.691406 667.511719 881.289062 L 853.550781 987.46875 C 859.925781 991.070312 865.136719 999.230469 865.136719 1005.601562 C 865.136719 1011.96875 859.925781 1014.238281 853.550781 1010.640625 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 904.894531 1207.269531 L 667.511719 1073.191406 C 661.136719 1069.589844 655.925781 1061.441406 655.925781 1055.058594 C 655.925781 1048.691406 661.136719 1046.421875 667.511719 1050.019531 L 904.894531 1184.101562 C 911.265625 1187.699219 916.480469 1195.859375 916.480469 1202.230469 C 916.480469 1208.601562 911.265625 1210.871094 904.894531 1207.269531 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 1052.460938 1347.480469 L 667.511719 1128.949219 C 661.136719 1125.351562 655.925781 1117.191406 655.925781 1110.820312 C 655.925781 1104.449219 661.136719 1102.179688 667.511719 1105.78125 L 1052.460938 1324.300781 C 1058.828125 1327.898438 1064.039062 1336.058594 1064.039062 1342.441406 C 1064.039062 1348.808594 1058.828125 1351.078125 1052.460938 1347.480469 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 1052.460938 1403.238281 L 667.511719 1184.710938 C 661.136719 1181.109375 655.925781 1172.949219 655.925781 1166.578125 C 655.925781 1160.210938 661.136719 1157.941406 667.511719 1161.539062 L 1052.460938 1380.058594 C 1058.828125 1383.660156 1064.039062 1391.820312 1064.039062 1398.191406 C 1064.039062 1404.570312 1058.828125 1406.828125 1052.460938 1403.238281 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 1052.460938 1458.988281 L 667.511719 1240.46875 C 661.136719 1236.871094 655.925781 1228.710938 655.925781 1222.339844 C 655.925781 1215.96875 661.136719 1213.699219 667.511719 1217.300781 L 1052.460938 1435.820312 C 1058.828125 1439.421875 1064.039062 1447.578125 1064.039062 1453.949219 C 1064.039062 1460.320312 1058.828125 1462.589844 1052.460938 1458.988281 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 853.550781 1402.410156 L 667.511719 1296.230469 C 661.136719 1292.628906 655.925781 1284.46875 655.925781 1278.101562 C 655.925781 1271.71875 661.136719 1269.460938 667.511719 1273.050781 L 853.550781 1379.230469 C 859.925781 1382.828125 865.136719 1390.988281 865.136719 1397.371094 C 865.136719 1403.738281 859.925781 1406.011719 853.550781 1402.410156 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 1050.71875 1658.289062 L 903.304688 1574.148438 C 898.253906 1571.300781 894.121094 1564.828125 894.121094 1559.78125 C 894.121094 1554.730469 898.253906 1552.941406 903.304688 1555.789062 L 1050.71875 1639.921875 C 1055.769531 1642.78125 1059.898438 1649.238281 1059.898438 1654.289062 C 1059.898438 1659.339844 1055.769531 1661.140625 1050.71875 1658.289062 "/>
<path fill-rule="nonzero" fill="rgb(73.631287%, 74.021912%, 75.78125%)" fill-opacity="1" d="M 807.308594 1532.949219 L 659.890625 1448.820312 C 654.84375 1445.96875 650.710938 1439.5 650.710938 1434.449219 C 650.710938 1429.398438 654.84375 1427.609375 659.890625 1430.460938 L 807.308594 1514.589844 C 812.359375 1517.441406 816.488281 1523.910156 816.488281 1528.960938 C 816.488281 1534.011719 812.359375 1535.808594 807.308594 1532.949219 "/>
<g clip-path="url(#clip-48)">
<g clip-path="url(#clip-49)">
<g clip-path="url(#clip-50)">
<path fill-rule="nonzero" fill="url(#linear-pattern-11)" d="M 285.078125 1001.273438 L 252.367188 868.152344 L 79.132812 910.71875 L 111.84375 1043.835938 Z M 285.078125 1001.273438 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-51)">
<g clip-path="url(#clip-52)">
<g clip-path="url(#clip-53)">
<path fill-rule="nonzero" fill="url(#linear-pattern-12)" d="M 376.199219 969.75 L 376.199219 1192.109375 L 785.597656 1192.109375 L 785.597656 969.75 Z M 376.199219 969.75 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-54)">
<g clip-path="url(#clip-55)">
<g clip-path="url(#clip-56)">
<path fill-rule="nonzero" fill="url(#linear-pattern-13)" d="M 94.519531 944.558594 L 94.519531 1440.871094 L 752.726562 1440.871094 L 752.726562 944.558594 Z M 94.519531 944.558594 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-57)">
<g clip-path="url(#clip-58)">
<g clip-path="url(#clip-59)">
<path fill-rule="nonzero" fill="url(#linear-pattern-14)" d="M 234.203125 907.519531 L 234.203125 1187.039062 L 402.636719 1187.039062 L 402.636719 907.519531 Z M 234.203125 907.519531 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-60)">
<g clip-path="url(#clip-61)">
<g clip-path="url(#clip-62)">
<path fill-rule="nonzero" fill="url(#linear-pattern-15)" d="M 95.671875 944.558594 L 95.671875 1195.109375 L 752.546875 1195.109375 L 752.546875 944.558594 Z M 95.671875 944.558594 "/>
</g>
</g>
</g>
<path fill-rule="nonzero" fill="rgb(68.164062%, 69.334412%, 71.679688%)" fill-opacity="1" d="M 1764.019531 1223.308594 L 2314.859375 1547.039062 L 2319.921875 1538.421875 L 1769.078125 1214.691406 "/>
<path fill-rule="nonzero" fill="rgb(68.164062%, 69.334412%, 71.679688%)" fill-opacity="1" d="M 2338.371094 1729.589844 L 1839.550781 2023.851562 L 1844.628906 2032.460938 L 2343.449219 1738.210938 "/>
<path fill-rule="nonzero" fill="rgb(17.651367%, 18.431091%, 30.979919%)" fill-opacity="1" d="M 1085.789062 638.621094 C 1085.789062 638.621094 868.222656 656.78125 849.953125 686.960938 C 831.6875 717.140625 887.28125 791.800781 915.871094 851.371094 C 944.464844 910.929688 978.203125 959.050781 978.203125 959.050781 C 978.203125 959.050781 984.804688 963.75 1000.371094 946.480469 C 1015.941406 929.199219 1023.761719 910.058594 1023.761719 910.058594 L 963.28125 752.949219 L 1246.351562 714.140625 C 1246.351562 714.140625 1129.421875 688.761719 1085.789062 638.621094 "/>
<path fill-rule="nonzero" fill="rgb(17.651367%, 18.431091%, 30.979919%)" fill-opacity="1" d="M 984.066406 959.058594 C 984.066406 959.058594 982.707031 985.53125 961.113281 1011.03125 C 939.519531 1036.53125 935.148438 1044.839844 953.941406 1055.328125 C 972.734375 1065.808594 993.605469 1059.261719 1014.949219 1024.289062 C 1036.300781 989.328125 1075.609375 969.128906 1068.53125 947.539062 C 1061.460938 925.949219 1021.96875 914 1021.96875 914 C 1021.96875 914 1005.449219 942.769531 984.066406 959.058594 "/>
<path fill-rule="nonzero" fill="rgb(27.050781%, 28.637695%, 42.747498%)" fill-opacity="1" d="M 1284.710938 685.730469 C 1284.710938 685.730469 1326.179688 748.71875 1242.101562 791.441406 C 1158.019531 834.171875 1062.179688 878.839844 1062.179688 878.839844 C 1062.179688 878.839844 1079.921875 912.730469 1077.199219 984.210938 C 1074.46875 1055.691406 1076.171875 1110.449219 1076.171875 1110.449219 C 1076.171875 1110.449219 1084.011719 1128.671875 1047.941406 1128.480469 C 1011.859375 1128.300781 1010.421875 1121.199219 1010.421875 1121.199219 C 1010.421875 1121.199219 994.972656 954.058594 982.527344 903.988281 C 970.085938 853.921875 956.886719 810.25 978.203125 777.96875 C 999.519531 745.691406 1138.71875 681.578125 1138.71875 681.578125 C 1138.71875 681.578125 1227.488281 715.699219 1284.710938 685.730469 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 65.039062%, 3.137207%)" fill-opacity="1" d="M 1144.128906 333.640625 C 1144.128906 333.640625 1096.691406 374.339844 1081.078125 418.050781 C 1065.46875 461.75 1056.371094 529.730469 1047.628906 541.339844 C 1038.890625 552.949219 989.570312 579.800781 989.570312 579.800781 C 989.570312 579.800781 1012.050781 584.171875 1015.789062 598.53125 C 1019.539062 612.890625 1018.289062 617.878906 1018.289062 617.878906 C 1018.289062 617.878906 1080.101562 587.289062 1089.460938 567.941406 C 1098.828125 548.578125 1127.339844 498.011719 1127.339844 498.011719 L 1144.128906 333.640625 "/>
<path fill-rule="nonzero" fill="rgb(93.75%, 93.75%, 93.75%)" fill-opacity="1" d="M 1144.128906 333.640625 C 1144.128906 333.640625 1124.539062 352.671875 1113.699219 369.640625 C 1102.851562 386.609375 1082.449219 420.238281 1085.789062 429.921875 C 1089.121094 439.601562 1101.691406 456.699219 1102.519531 461.710938 C 1103.351562 466.710938 1095.109375 648.019531 1095.109375 648.019531 C 1095.109375 648.019531 1114.628906 684.828125 1186.339844 696.121094 C 1259.019531 707.570312 1295.351562 691.058594 1295.351562 691.058594 C 1295.351562 691.058594 1271.441406 638.558594 1287.289062 580.148438 C 1303.140625 521.75 1322.988281 438.5 1307.128906 416.480469 C 1291.28125 394.460938 1169.160156 322.378906 1144.128906 333.640625 "/>
<path fill-rule="nonzero" fill="rgb(17.651367%, 18.431091%, 30.979919%)" fill-opacity="1" d="M 1157.238281 204.078125 C 1157.238281 204.078125 1093.019531 219.761719 1118.351562 293.648438 C 1125.089844 313.308594 1101.851562 324.78125 1112.25 345.039062 C 1122.660156 365.308594 1190.46875 370.121094 1190.578125 369.511719 C 1190.699219 368.898438 1204.300781 237.789062 1204.300781 237.789062 L 1157.238281 204.078125 "/>
<path fill-rule="nonzero" fill="rgb(100%, 74.021912%, 74.021912%)" fill-opacity="1" d="M 1154.429688 225.460938 C 1154.429688 225.460938 1133.328125 232.980469 1134.820312 288.839844 C 1135.800781 325.359375 1149.058594 339.089844 1156.128906 344.679688 C 1163.210938 350.269531 1173.039062 350.699219 1173.039062 350.699219 L 1170.039062 372.25 C 1170.039062 372.25 1167.039062 388.238281 1195.109375 394.640625 C 1223.179688 401.039062 1243.160156 370.96875 1243.160156 370.96875 C 1243.160156 370.96875 1238.210938 347.75 1243.050781 325.519531 C 1247.878906 303.289062 1252.941406 247.078125 1230.351562 234.460938 C 1207.769531 221.839844 1154.429688 225.460938 1154.429688 225.460938 "/>
<path fill-rule="nonzero" fill="rgb(91.796875%, 63.476562%, 63.476562%)" fill-opacity="1" d="M 1173.039062 350.699219 C 1173.039062 350.699219 1191.078125 351.21875 1200.308594 343.019531 C 1209.550781 334.808594 1212.320312 332.628906 1212.320312 332.628906 C 1212.320312 332.628906 1207.421875 343.511719 1197.140625 354.460938 C 1186.859375 365.421875 1189.53125 393.121094 1189.53125 393.121094 C 1189.53125 393.121094 1168.421875 388.980469 1170.019531 375.710938 C 1171.621094 362.441406 1173.039062 350.699219 1173.039062 350.699219 "/>
<path fill-rule="nonzero" fill="rgb(23.143005%, 24.31488%, 36.863708%)" fill-opacity="1" d="M 1141.761719 240.21875 C 1141.761719 240.21875 1142.46875 259.820312 1173.679688 271.421875 C 1204.300781 282.800781 1216.808594 286.96875 1216.808594 286.96875 C 1216.808594 286.96875 1220.859375 272.121094 1228.660156 278.988281 C 1240.21875 289.179688 1213.179688 320.691406 1220.691406 339.871094 C 1228.199219 359.050781 1230.519531 373.101562 1271.980469 391.25 C 1313.449219 409.390625 1320.640625 459.578125 1320.640625 459.578125 C 1320.640625 459.578125 1371.679688 465.191406 1386.210938 434.378906 C 1401.25 402.488281 1385.339844 370.210938 1338.339844 357.769531 C 1291.328125 345.328125 1277.980469 327.058594 1284.039062 296.148438 C 1291.480469 258.140625 1283.039062 209.558594 1228.199219 191.128906 C 1173.359375 172.699219 1141.011719 210.839844 1141.761719 240.21875 "/>
<path fill-rule="nonzero" fill="rgb(77.734375%, 77.734375%, 77.734375%)" fill-opacity="1" d="M 877.917969 470.140625 L 1095.789062 594.488281 L 1095.789062 781.621094 L 877.917969 657.261719 L 869.265625 474.378906 L 877.917969 470.140625 "/>
<path fill-rule="nonzero" fill="rgb(93.75%, 93.75%, 93.75%)" fill-opacity="1" d="M 869.265625 474.378906 L 1087.140625 598.738281 L 1087.140625 785.859375 L 869.265625 661.5 L 869.265625 474.378906 "/>
<path fill-rule="nonzero" fill="rgb(69.726562%, 69.334412%, 69.334412%)" fill-opacity="1" d="M 1087.140625 785.859375 L 1222.410156 695.691406 L 1087.140625 616.460938 Z M 1087.140625 785.859375 "/>
<path fill-rule="nonzero" fill="rgb(77.734375%, 77.734375%, 77.734375%)" fill-opacity="1" d="M 1222.410156 695.691406 L 1222.410156 705.109375 L 1087.140625 785.859375 L 1087.140625 598.738281 L 1095.789062 594.488281 L 1095.789062 773.070312 L 1222.410156 695.691406 "/>
<path fill-rule="nonzero" fill="rgb(50.976562%, 49.804688%, 49.804688%)" fill-opacity="1" d="M 1095.789062 671.25 L 1149.191406 703.980469 L 1095.789062 734.910156 Z M 1095.789062 671.25 "/>
<path fill-rule="nonzero" fill="rgb(99.609375%, 65.039062%, 3.137207%)" fill-opacity="1" d="M 1307.128906 421.941406 C 1293.519531 417.320312 1269.269531 432.460938 1268.019531 478.03125 C 1266.769531 523.609375 1268.019531 585.730469 1268.019531 585.730469 L 1163.761719 653.46875 C 1163.761719 653.46875 1185.609375 655.339844 1192.480469 670.328125 C 1199.351562 685.308594 1200.589844 691.058594 1200.589844 691.058594 C 1200.589844 691.058594 1321.089844 624.128906 1325.460938 607.269531 C 1329.828125 590.410156 1335.011719 431.410156 1307.128906 421.941406 "/>
<path fill-rule="nonzero" fill="rgb(100%, 74.021912%, 74.021912%)" fill-opacity="1" d="M 1172.890625 655.390625 C 1172.890625 655.390625 1148.511719 667.640625 1132.328125 678.21875 C 1116.160156 688.789062 1108.691406 713.058594 1109.308594 715.550781 C 1109.929688 718.03125 1129.160156 732.339844 1135.101562 732.339844 C 1141.039062 732.339844 1156.589844 709.949219 1169.03125 702.480469 C 1181.480469 695.019531 1196.28125 679.011719 1196.28125 679.011719 C 1196.28125 679.011719 1197.898438 659.941406 1172.890625 655.390625 "/>
<path fill-rule="nonzero" fill="rgb(17.651367%, 18.431091%, 30.979919%)" fill-opacity="1" d="M 1075.800781 1119.101562 C 1075.800781 1119.101562 1089.871094 1146.671875 1084.191406 1154.101562 C 1078.511719 1161.53125 1024.5 1190.808594 1010.421875 1207.859375 C 996.335938 1224.910156 968.800781 1223.160156 953.941406 1210.921875 C 939.082031 1198.679688 940.121094 1188.191406 966.917969 1166.78125 C 993.714844 1145.359375 1010.421875 1121.199219 1010.421875 1121.199219 C 1010.421875 1121.199219 1047.710938 1137.800781 1075.800781 1119.101562 "/>
<path fill-rule="nonzero" fill="rgb(52.929688%, 54.101562%, 62.693787%)" fill-opacity="1" d="M 2061.421875 596.441406 L 2374.121094 781.78125 C 2384.898438 788.058594 2391.53125 799.589844 2391.53125 812.058594 L 2391.53125 1202.371094 C 2391.53125 1217.320312 2358.601562 1236.238281 2345.679688 1228.71875 L 2054.101562 1036.320312 C 2044.71875 1030.859375 2038.949219 1020.828125 2038.949219 1009.96875 L 2015.621094 614.808594 C 2015.621094 603.269531 2051.460938 590.628906 2061.421875 596.441406 "/>
<g clip-path="url(#clip-63)">
<g clip-path="url(#clip-64)">
<g clip-path="url(#clip-65)">
<path fill-rule="nonzero" fill="url(#linear-pattern-16)" d="M 2555.257812 668.359375 L 2049.71875 499.847656 L 1826.242188 1170.269531 L 2331.78125 1338.785156 Z M 2555.257812 668.359375 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-66)">
<g clip-path="url(#clip-67)">
<g clip-path="url(#clip-68)">
<path fill-rule="nonzero" fill="url(#linear-pattern-17)" d="M 2329.585938 769.109375 L 2112.4375 696.726562 L 2051.914062 878.300781 L 2269.0625 950.683594 Z M 2329.585938 769.109375 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-69)">
<g clip-path="url(#clip-70)">
<g clip-path="url(#clip-71)">
<path fill-rule="nonzero" fill="url(#linear-pattern-18)" d="M 2329.34375 833.039062 L 2112.4375 760.734375 L 2052.15625 941.582031 L 2269.0625 1013.882812 Z M 2329.34375 833.039062 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-72)">
<g clip-path="url(#clip-73)">
<g clip-path="url(#clip-74)">
<path fill-rule="nonzero" fill="url(#linear-pattern-19)" d="M 2329.339844 897.058594 L 2112.4375 824.757812 L 2052.160156 1005.59375 L 2269.0625 1077.894531 Z M 2329.339844 897.058594 "/>
</g>
</g>
</g>
<g clip-path="url(#clip-75)">
<g clip-path="url(#clip-76)">
<g clip-path="url(#clip-77)">
<path fill-rule="nonzero" fill="url(#linear-pattern-20)" d="M 2329.34375 961.066406 L 2112.4375 888.765625 L 2052.15625 1069.613281 L 2269.0625 1141.914062 Z M 2329.34375 961.066406 "/>
</g>
</g>
</g>
</svg>
