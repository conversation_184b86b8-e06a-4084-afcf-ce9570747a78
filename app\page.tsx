"use client";

import { useEffect, useState, useRef } from "react";
import {
  AnimatePresence,
  motion,
  useScroll,
  useTransform,
} from "framer-motion";
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useMobile } from "@/hooks/use-mobile";
import TestimonialCarousel from "@/components/testimonial-slider";
import { featuredServices } from "@/data/services";
import { stats } from "@/data/stats";
import { testimonials } from "@/data/testimonials";
import VisaDestinations from "@/components/destination";
import { CEOIntro } from "@/components/ceo-intro";
import Image from "next/image";
import { DirectorIntro } from "@/components/director-intro";
import IELTSBanner from "@/components/ielts-banner";

export default function Home() {
  const isMobile = useMobile();
  const { scrollY } = useScroll();
  const heroOpacity = useTransform(scrollY, [0, 300], [1, 0]);
  const heroY = useTransform(scrollY, [0, 300], [0, 100]);

  const words = [
    "International Students and Scholars",
    "Academic Dreamers Worldwide",
    "Future Global Professionals",
    "Educational Explorers",
    "Study Abroad Aspirants",
  ];
  const servicesRef = useRef<HTMLDivElement>(null);
  const testimonialsRef = useRef<HTMLDivElement>(null);

  const [mounted, setMounted] = useState(false);
  const [index, setIndex] = useState(0);

  useEffect(() => {
    setMounted(true);
    const interval = setInterval(() => {
      setIndex((prevIndex) => (prevIndex + 1) % words.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  if (!mounted) return null;

  return (
    <>
      <main className="relative overflow-hidden">
        <motion.section
          className="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          {/* Abstract background patterns */}
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-primary/5 rounded-bl-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-primary/10 rounded-tr-full blur-3xl"></div>
            <div className="absolute top-1/4 left-1/4 w-1/4 h-1/4 bg-blue-500/5 rounded-full blur-2xl"></div>
          </div>

          {/* Subtle grid overlay */}
          <div className="absolute inset-0 z-1 bg-grid-pattern opacity-5"></div>

          {/* Floating elements - optional decorative elements */}
          <div className="absolute inset-0 z-2 overflow-hidden">
            <div className="absolute h-20 w-20 rounded-full bg-primary/10 top-1/4 right-1/5 blur-xl"></div>
            <div className="absolute h-32 w-32 rounded-full bg-blue-400/10 bottom-1/4 left-1/5 blur-xl"></div>
            <div className="absolute h-16 w-16 rounded-full bg-primary/20 top-2/3 right-1/3 blur-lg"></div>
          </div>

          <div>
            <Image
              src="/budapest-1.webp"
              alt="Hero image"
              fill
              className="object-cover absolute inset-0 z-0 opacity-5"
            />
          </div>
          <div className="container relative z-10 mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="max-w-2xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-7xl font-bold mb-6 tracking-tight text-white">
                Leading You to <span className="text-primary">World-Class</span>{" "}
                Education
              </h1>

              <div className="h-16 mb-8 relative overflow-hidden">
                <AnimatePresence mode="wait">
                  <motion.p
                    key={index}
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: -50, opacity: 0 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="text-xl md:text-2xl text-slate-300 absolute w-full"
                  >
                    Expert Guidance for{" "}
                    <span className="font-semibold text-primary">
                      {words[index]}
                    </span>
                  </motion.p>
                </AnimatePresence>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/services" className="w-full sm:w-auto">
                  <Button size="lg" className="group w-full">
                    Explore Services
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
                <Link href="/contact" className="w-full sm:w-auto">
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full bg-background/10 backdrop-blur-sm border-slate-600 text-white hover:bg-white/20"
                  >
                    Book Consultation
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>

          <motion.div
            className="absolute bottom-10 left-1/2 -translate-x-1/2"
            animate={{ y: [0, 10, 0] }}
            transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1.5 }}
          >
            <Button
              variant="ghost"
              size="icon"
              className="text-white/70 hover:text-white hover:bg-white/10"
              onClick={() =>
                servicesRef.current?.scrollIntoView({ behavior: "smooth" })
              }
            >
              <ArrowRight className="h-6 w-6 rotate-90" />
            </Button>
          </motion.div>

          {/* Add a global style for the grid pattern */}
          <style jsx global>{`
            .bg-grid-pattern {
              background-image: linear-gradient(
                  to right,
                  rgba(255, 255, 255, 0.05) 1px,
                  transparent 1px
                ),
                linear-gradient(
                  to bottom,
                  rgba(255, 255, 255, 0.05) 1px,
                  transparent 1px
                );
              background-size: 40px 40px;
            }
          `}</style>
        </motion.section>
        {/* Services Section */}
        <section ref={servicesRef} className="py-24 bg-muted/30">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-100px" }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-5xl font-bold mb-4">
                Our Services
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Comprehensive support for your international education journey
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredServices.map((service, index) => (
                <motion.div
                  key={service.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true, margin: "-100px" }}
                >
                  <Card className="h-full overflow-hidden group hover:shadow-lg transition-shadow duration-300 bg-muted/30">
                    <CardContent className="p-6">
                      <div className="mb-4 bg-primary/10 p-3 rounded-full w-fit">
                        {service.icon}
                      </div>
                      <h3 className="text-2xl font-bold mb-2">
                        {service.title}
                      </h3>
                      <p className="text-muted-foreground mb-4 ">
                        {service.description}
                      </p>
                      <Button variant="link" className="p-0 group" asChild>
                        <Link href={`/services#${service.id}`}>
                          Learn more
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
        {/* Countries Section */}
        <section className="py-24 bg-background">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-100px" }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-5xl font-bold mb-4">
                Popular Destinations
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Explore top educational destinations around the world
              </p>
            </motion.div>

            <VisaDestinations />
          </div>
        </section>
        {/*CEO Intro Section */}
        <section className="py-24 bg-muted/30">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-100px" }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-5xl font-bold mb-12">
                Our Founder
              </h2>
              <CEOIntro />
            </motion.div>
          </div>
        </section>{" "}
        <section className="p-24 bg-background">
          <IELTSBanner />
        </section>
        {/* Stats Section */}
        <section className="py-24 bg-primary/10 text-background">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true, margin: "-100px" }}
                  className="text-center"
                >
                  <div className="text-3xl md:text-5xl font-bold mb-4 text-foreground">
                    {stat.value}
                  </div>
                  <div className="text-primary font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
        {/*Director Intro Section */}
        <section className="py-24 bg-background">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-100px" }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-5xl font-bold mb-12">
                Our President
              </h2>
              <DirectorIntro />
            </motion.div>
          </div>
        </section>
        {/* Testimonials Section */}
        <section ref={testimonialsRef} className="py-24 bg-muted/30">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-100px" }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-5xl font-bold mb-4">
                What Our Students Say
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Hear from students who achieved their dreams with our help
              </p>
            </motion.div>

            <TestimonialCarousel testimonials={testimonials} />
          </div>
        </section>
        {/* CTA Section */}
        <section className="py-24 bg-primary/10">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-100px" }}
              className="max-w-4xl mx-auto text-center"
            >
              <h2 className="text-3xl md:text-5xl font-bold mb-6">
                Ready to Start Your Journey?
              </h2>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                Book a free consultation with our education experts and take the
                first step toward your international education goals.
              </p>
              <Link
                href={`https://wa.me/message/3OSI3VSB2QT4F1`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block"
                data-cursor-text="Chat with us"
              >
                <Button size="lg" className="group">
                  Book a Consultation
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </section>
      </main>
    </>
  );
}
