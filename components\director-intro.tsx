import Image from "next/image";
import Link from "next/link";
import { Instagram, Facebook, Mail, Linkedin, Award } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CertificateBadge } from "./certificate-badge";

interface DirectorIntroProps {
  name?: string;
  title?: string;
  bio?: string;
  imageUrl?: string;
  socialLinks?: {
    email?: string;
    instagram?: string;
    facebook?: string;
    linkedin?: string;
  };
}

export function DirectorIntro({
  name = "<PERSON><PERSON><PERSON>",

  title = "Founder @markhor_goc",
  bio = "Business Counselor | British Council Certified Foreign Education Counselor @markhorinternational | Humanitarian",
  imageUrl = "/team/humayun.jpg",
  socialLinks = {
    //     email: "<EMAIL>",
    //     instagram: "https://www.instagram.com/hiraiiqbal",
    //     facebook: "https://www.facebook.com/hiraiiqbal",
    //     linkedin: "https://www.linkedin.com/in/hira-iqbal-1a6b34207/",
  },
}: DirectorIntroProps) {
  return (
    <Card className="overflow-hidden border rounded-lg shadow-lg bg-muted/30">
      <CardContent className="p-6 sm:p-8">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12">
          <div className="flex flex-col items-center lg:items-start space-y-4 lg:max-w-[300px]">
            <div className="overflow-hidden border-4 border-background shadow-xl">
              <Image
                src={imageUrl || "/placeholder.svg"}
                alt={name}
                width={200}
                height={200}
                className="object-top object-cover"
              />
            </div>
            <div className="flex justify-center space-x-4 w-full">
              {socialLinks.email && (
                <Link href={`mailto:${socialLinks.email}`}>
                  <Button size="icon" variant="ghost">
                    <Mail className="h-5 w-5" />
                    <span className="sr-only">Email</span>
                  </Button>
                </Link>
              )}
              {socialLinks.instagram && (
                <Link
                  href={socialLinks.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="icon" variant="ghost">
                    <Instagram className="h-5 w-5" />
                    <span className="sr-only">Instagram</span>
                  </Button>
                </Link>
              )}
              {socialLinks.facebook && (
                <Link
                  href={socialLinks.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="icon" variant="ghost">
                    <Facebook className="h-5 w-5" />
                    <span className="sr-only">Facebook</span>
                  </Button>
                </Link>
              )}{" "}
              {socialLinks.linkedin && (
                <Link
                  href={socialLinks.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="icon" variant="ghost">
                    <Linkedin className="h-5 w-5" />
                    <span className="sr-only">LinkedIn</span>
                  </Button>
                </Link>
              )}
            </div>
          </div>
          <div className="flex flex-col justify-center space-y-4 flex-1">
            <div className="space-y-2 text-center lg:text-left">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">
                {name}
              </h2>
              <p className="text-xl text-muted-foreground">
                {title.includes("@") ? <>President</> : title}
              </p>
            </div>
            <div className="prose prose-sm max-w-none">
              <p
                className="text-base leading-relaxed mb-6 text-justify indent-8
 "
              >
                As President of an foreign educational firm, I bring motivation
                and innovation to team leadership. My expertise in strategic
                management helps build strong educational brands while
                implementing necessary changes in the evolving visa landscape.
                I'm passionate about adding genuine value to students' lives
                through informed global education opportunities.
              </p>
              <p
                className="text-base leading-relaxed mb-6 text-justify indent-8
 "
              >
                My extensive travel history attending education conferences and
                navigating various visa systems firsthand gives me unique
                insights that benefit our clients. This practical experience
                across multiple countries enhances our consultancy's credibility
                and success rate. My experience as a public speaker allows me to
                effectively communicate complex visa processes and provide
                comprehensive student counseling, while my entrepreneurial drive
                ensures our consultancy stays ahead of international education
                trends.
              </p>
              <p
                className="text-base leading-relaxed mb-6 text-justify indent-8
 "
              >
                {" "}
                I excel in leadership and team building, combining swift
                learning abilities with strong follow-up skills to ensure
                student success. My innovative approach and positive energy make
                me the ideal Change Leader for educational consulting services
                seeking transformation.
              </p>
            </div>
            <div className="mt-6 ">
              <h3 className="text-lg font-medium mb-3">Certifications</h3>
              <div className="flex flex-wrap gap-2">
                <CertificateBadge
                  tag="UK agent and counseller trainer"
                  name="UK agent and counseller trainer"
                  image="/team/certificates/agent_humayun.png"
                  member="Humayun Iftikhar"
                />{" "}
                <CertificateBadge
                  tag="PTE Core"
                  name="PTE Core - Train the Trainer Workshop"
                  image="/team/certificates/pte_core_humayun.png"
                  member="Humayun Iftikhar"
                />{" "}
                <CertificateBadge
                  tag="PTE Academic/UKVI"
                  name="PTE Academic/UKVI - Train the Trainer Workshop"
                  image="/team/certificates/pte_academic_humayun.png"
                  member="Humayun Iftikhar"
                />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
