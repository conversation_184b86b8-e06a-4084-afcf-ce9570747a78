"use client";

import { useState } from "react";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Award } from "lucide-react";
import Link from "next/link";

interface Certificate {
  tag: string;
  name: string;
  image?: string;
  url?: string;
}

interface TeamMemberProps {
  member: {
    name: string;
    position: string;
    image: string;
    bio: string;
    specialization: string;
    education: string;
    certificates?: Certificate[];
  };
}

export default function TeamMember({ member }: TeamMemberProps) {
  const [openCertificate, setOpenCertificate] = useState<Certificate | null>(
    null
  );

  return (
    <Card className="h-full overflow-hidden border-none shadow-lg hover:shadow-xl transition-shadow duration-300 bg-muted/30">
      <CardContent className="p-0 flex flex-col h-full">
        <div className="relative h-64 w-full">
          <Image
            src={member.image || "/placeholder.svg"}
            alt={member.name}
            fill
            className="object-cover object-top"
          />
        </div>

        <div className="p-6 flex flex-col flex-grow">
          <div className="flex-grow">
            <div className="flex items-start justify-between mb-2">
              <h3 className="text-xl font-bold">{member.name}</h3>
            </div>
            <p className="text-primary font-medium mb-4">{member.position}</p>
            <p
              className="text-muted-foreground text-sm mb-4"
              dangerouslySetInnerHTML={{ __html: member.bio }}
            />
          </div>

          {member.certificates && member.certificates.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-auto pt-4 justify-end">
              {member.certificates.map((cert, index) =>
                cert.image ? (
                  <Dialog key={index}>
                    <DialogTrigger asChild>
                      <Badge
                        variant="outline"
                        className="cursor-pointer hover:bg-primary/10 transition-colors flex items-center gap-1"
                      >
                        <Award className="h-3 w-3" />
                        <span className="text-xs">{cert.tag}</span>
                      </Badge>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px]">
                      <div className="space-y-4">
                        <h3 className="text-xl font-bold text-center">
                          {cert.name}
                        </h3>
                        <div className="relative h-[400px] w-full overflow-hidden rounded-md">
                          <Image
                            src={cert.image || "/placeholder.svg"}
                            alt={cert.name}
                            fill
                            className="object-contain"
                          />
                        </div>
                        <p className="text-center text-sm text-muted-foreground">
                          Certificate issued to {member.name}
                        </p>
                      </div>
                    </DialogContent>
                  </Dialog>
                ) : cert.url ? (
                  <Link
                    href={cert.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Badge
                      variant="outline"
                      className="cursor-pointer hover:bg-primary/10 transition-colors flex items-center gap-1"
                    >
                      <Award className="h-3 w-3" />
                      <span className="text-xs">{cert.tag}</span>
                    </Badge>
                  </Link>
                ) : null
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
