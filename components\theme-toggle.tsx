"use client"

import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { AnimatePresence, motion } from "framer-motion"
import { Moon, Sun } from "lucide-react"

export function ThemeToggle() {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div className="w-14 h-7" />
  }

  const isDark = theme === "dark"

  return (
    <motion.button
      whileTap={{ scale: 0.95 }}
      onClick={() => setTheme(isDark ? "light" : "dark")}
      className="relative h-7 w-14 rounded-full p-1 overflow-hidden"
      style={{
        background: isDark
          ? "linear-gradient(to right, #0f172a, #1e293b)"
          : "linear-gradient(to right, #60a5fa, #3b82f6)",
      }}
      aria-label="Toggle theme"
    >
      {/* Track */}
      <div className="absolute inset-0 w-full h-full">
        {/* Stars (only visible in dark mode) */}
        <AnimatePresence>
          {isDark && (
            <>
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={`star-${i}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: [0, 1, 0], scale: [0.5, 1, 0.5] }}
                  transition={{
                    repeat: Number.POSITIVE_INFINITY,
                    duration: 2 + Math.random() * 3,
                    delay: Math.random() * 2,
                    repeatType: "loop",
                  }}
                  className="absolute w-0.5 h-0.5 bg-white rounded-full"
                  style={{
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                  }}
                />
              ))}
            </>
          )}
        </AnimatePresence>

        {/* Clouds (only visible in light mode) */}
        <AnimatePresence>
          {!isDark && (
            <>
              {[...Array(2)].map((_, i) => (
                <motion.div
                  key={`cloud-${i}`}
                  initial={{ x: -10, opacity: 0 }}
                  animate={{ x: 20, opacity: [0, 1, 0] }}
                  transition={{
                    repeat: Number.POSITIVE_INFINITY,
                    duration: 4 + Math.random() * 2,
                    delay: Math.random() * 2,
                    repeatType: "loop",
                  }}
                  className="absolute w-3 h-1.5 bg-white/70 rounded-full blur-[1px]"
                  style={{
                    top: `${30 + Math.random() * 40}%`,
                    left: `${Math.random() * 30}%`,
                  }}
                />
              ))}
            </>
          )}
        </AnimatePresence>
      </div>

      {/* Thumb/Handle */}
      <motion.div
        className="relative z-10 w-5 h-5 rounded-full flex items-center justify-center"
        animate={{
          x: isDark ? "100%" : "0%",
          backgroundColor: isDark ? "#f9fafb" : "#fef3c7",
          boxShadow: isDark ? "0 0 8px 2px rgba(255, 255, 255, 0.3)" : "0 0 12px 4px rgba(251, 191, 36, 0.4)",
        }}
        transition={{ type: "spring", stiffness: 300, damping: 15 }}
      >
        <AnimatePresence mode="wait" initial={false}>
          <motion.div
            key={theme}
            initial={{ rotate: -30, opacity: 0 }}
            animate={{ rotate: 0, opacity: 1 }}
            exit={{ rotate: 30, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {isDark ? <Moon className="h-3 w-3 text-slate-800" /> : <Sun className="h-3 w-3 text-amber-600" />}
          </motion.div>
        </AnimatePresence>
      </motion.div>

      <span className="sr-only">Toggle theme</span>
    </motion.button>
  )
}
