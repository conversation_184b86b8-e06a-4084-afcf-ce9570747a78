import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

import { RefObject } from "react";

const HeroSection = ({
  servicesRef,
}: {
  servicesRef: RefObject<HTMLElement>;
}) => {
  return (
    <motion.section
      className="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      {/* Abstract background patterns */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-primary/5 rounded-bl-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-primary/10 rounded-tr-full blur-3xl"></div>
        <div className="absolute top-1/4 left-1/4 w-1/4 h-1/4 bg-blue-500/5 rounded-full blur-2xl"></div>
      </div>

      {/* Subtle grid overlay */}
      <div className="absolute inset-0 z-1 bg-grid-pattern opacity-5"></div>

      {/* Floating elements - optional decorative elements */}
      <div className="absolute inset-0 z-2 overflow-hidden">
        <div className="absolute h-20 w-20 rounded-full bg-primary/10 top-1/4 right-1/5 blur-xl"></div>
        <div className="absolute h-32 w-32 rounded-full bg-blue-400/10 bottom-1/4 left-1/5 blur-xl"></div>
        <div className="absolute h-16 w-16 rounded-full bg-primary/20 top-2/3 right-1/3 blur-lg"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="max-w-2xl mx-auto text-center"
        >
          <h1 className="text-5xl md:text-7xl font-bold mb-6 tracking-tight text-white">
            Your <span className="text-primary">Visa</span> Journey Starts Here
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-slate-300">
            Expert guidance for international visas and immigration to help you
            achieve your global aspirations.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/services" className="w-full sm:w-auto">
              <Button size="lg" className="group w-full">
                Explore Services
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
            <Link href="/consultation" className="w-full sm:w-auto">
              <Button
                size="lg"
                variant="outline"
                className="w-full bg-background/10 backdrop-blur-sm border-slate-600 text-white hover:bg-white/20"
              >
                Free Consultation
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>

      <motion.div
        className="absolute bottom-10 left-1/2 -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1.5 }}
      >
        <Button
          variant="ghost"
          size="icon"
          className="text-white/70 hover:text-white hover:bg-white/10"
          onClick={() =>
            servicesRef.current?.scrollIntoView({ behavior: "smooth" })
          }
        >
          <ArrowRight className="h-6 w-6 rotate-90" />
        </Button>
      </motion.div>

      {/* Add a global style for the grid pattern */}
      <style jsx global>{`
        .bg-grid-pattern {
          background-image: linear-gradient(
              to right,
              rgba(255, 255, 255, 0.05) 1px,
              transparent 1px
            ),
            linear-gradient(
              to bottom,
              rgba(255, 255, 255, 0.05) 1px,
              transparent 1px
            );
          background-size: 40px 40px;
        }
      `}</style>
    </motion.section>
  );
};

export default HeroSection;
